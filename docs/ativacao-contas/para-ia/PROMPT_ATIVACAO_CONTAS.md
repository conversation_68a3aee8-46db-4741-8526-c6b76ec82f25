# 🤖 PROMPT: Sistema de Ativação de Contas - Fluu/Polar

## 📋 Contexto

Este documento serve como guia para agentes de IA que trabalham com o sistema Fluu (fork do Polar.sh). O sistema requer que contas sejam aprovadas antes de receber pagamentos.

---

## 🎯 Objetivo

Ativar contas de pagamento para que organizações e usuários possam receber pagamentos (payouts), processando transações via múltiplos gateways (Stripe, Pagar.me, Mercado Pago).

---

## 🔑 Conceitos-Chave

### 1. Account (Conta de Pagamento)
- **Modelo:** `polar.models.Account`
- **Propósito:** Gerenciar recebimentos e payouts
- **Campos críticos:**
  - `status`: Enum (CREATED, ONBOARDING_STARTED, UNDER_REVIEW, DENIED, **ACTIVE**)
  - `is_payouts_enabled`: Boolean (permite saques)
  - `is_charges_enabled`: Boolean (permite cobranças)
  - `is_details_submitted`: <PERSON>olean (detalhes enviados)
  - `stripe_id`: String (ID no gateway - pode ser fake para dev)
  - `admin_id`: UUID (quem controla a conta)
  - `next_review_threshold`: Integer (threshold em centavos)

### 2. Organization (Organização)
- **Modelo:** `polar.models.Organization`
- **Propósito:** Entidade que vende produtos
- **Campos críticos:**
  - `status`: Enum (created, onboarding_started, under_review, denied, **active**)
  - `account_id`: UUID (conta vinculada)
  - `details_submitted_at`: DateTime (quando detalhes foram enviados)
  - `details`: JSONB (informações do negócio)

### 3. User (Usuário)
- **Modelo:** `polar.models.User`
- **Propósito:** Admin que controla a conta
- **Campos críticos:**
  - `identity_verification_status`: Enum (unverified, pending, **verified**, failed)
  - `account_id`: UUID (conta pessoal vinculada)
  - `is_admin`: Boolean (permissões administrativas)

---

## ✅ Requisitos para Conta Ativa

Uma conta está **pronta para receber pagamentos** quando:

```python
# Checklist de Ativação
✅ Account.status == Account.Status.ACTIVE
✅ Account.is_payouts_enabled == True
✅ Account.is_charges_enabled == True
✅ Account.is_details_submitted == True
✅ Account.stripe_id != None  # Pode ser fake para dev
✅ Account.admin.identity_verification_status in [verified, pending]

# Se tiver Organization:
✅ Organization.status == OrganizationStatus.ACTIVE
✅ Organization.details_submitted_at != None
✅ Organization.details != None ou {}
✅ Organization.account_id == Account.id
```

---

## 🛠️ Scripts Disponíveis

### Script Principal: `activate_account_complete.py` ⭐

**Faz TUDO automaticamente** - Use este primeiro!

```bash
# Ativar organização
uv run python -m scripts.activate_account_complete \
  --organization-slug SLUG \
  --admin-email EMAIL \
  --no-dry-run

# Ativar usuário
uv run python -m scripts.activate_account_complete \
  --user-email EMAIL \
  --no-dry-run
```

**O que faz:**
1. Cria ou atualiza Account
2. Define Stripe ID (fake para dev)
3. Habilita payouts e charges
4. Marca details como submetidos
5. Verifica identidade do admin (bypass KYC)
6. Ativa Organization (se aplicável)
7. Vincula tudo corretamente

### Scripts Específicos

#### 1. Aprovar Conta Existente
```bash
uv run python -m scripts.approve_account_for_payout \
  --email EMAIL \
  --no-dry-run
```

#### 2. Verificar Identidade (Bypass KYC)
```bash
uv run python -m scripts.verify_user_identity EMAIL --no-dry-run
```

#### 3. Mudar Admin da Conta
```bash
uv run python -m scripts.change_account_admin SLUG NEW_EMAIL --no-dry-run
```

#### 4. Adicionar Stripe ID
```bash
uv run python -m scripts.add_stripe_id_to_account \
  --email EMAIL \
  --no-dry-run
```

#### 5. Verificar Status
```bash
# Listar todas as contas
uv run python -m scripts.list_accounts_status list-accounts

# Ver conta específica
uv run python -m scripts.list_accounts_status show --email EMAIL

# Verificar organização
uv run python -m scripts.check_organization_account SLUG
```

---

## 🚨 Problemas Comuns e Soluções

### Erro 1: "Payment processing is not yet available"

**Causa:** Organização não tem conta vinculada ou requisitos não atendidos

**Solução:**
```bash
uv run python -m scripts.activate_account_complete \
  --organization-slug SLUG \
  --admin-email EMAIL \
  --no-dry-run
```

### Erro 2: 403 "You are not the admin of this account"

**Causa:** Usuário logado não é o admin da conta da organização

**Solução:**
```bash
uv run python -m scripts.change_account_admin SLUG EMAIL --no-dry-run
```

### Erro 3: 500 ao criar identity verification

**Causa:** Sistema tenta usar Stripe Identity (KYC) mas não está configurado

**Solução:**
```bash
uv run python -m scripts.verify_user_identity EMAIL --no-dry-run
```

### Erro 4: Conta sem Stripe ID

**Causa:** Dashboard exige stripe_id para mostrar como pronta

**Solução:**
```bash
uv run python -m scripts.add_stripe_id_to_account \
  --email EMAIL \
  --no-dry-run
```

---

## 🔄 Workflow Completo

### Para Nova Organização:

```bash
# 1. Verificar status atual
uv run python -m scripts.check_organization_account SLUG

# 2. Ativar tudo de uma vez
uv run python -m scripts.activate_account_complete \
  --organization-slug SLUG \
  --admin-email EMAIL \
  --no-dry-run

# 3. Confirmar
uv run python -m scripts.check_organization_account SLUG

# 4. Testar no dashboard
# https://fluu.digital/dashboard/SLUG
```

### Para Novo Usuário:

```bash
# 1. Criar e ativar conta
uv run python -m scripts.activate_account_complete \
  --user-email EMAIL \
  --no-dry-run

# 2. Verificar
uv run python -m scripts.approve_account_for_payout --email EMAIL
```

---

## 💡 Boas Práticas

### 1. Sempre use Dry-Run Primeiro

```bash
# Simular (sem --no-dry-run)
uv run python -m scripts.activate_account_complete --organization-slug SLUG --admin-email EMAIL

# Ver o que será feito, depois executar
uv run python -m scripts.activate_account_complete --organization-slug SLUG --admin-email EMAIL --no-dry-run
```

### 2. Verifique Antes de Aprovar

```bash
# Ver status detalhado
uv run python -m scripts.check_organization_account SLUG
uv run python -m scripts.list_accounts_status show --email EMAIL
```

### 3. Use o Script "Faz Tudo" para Novos

Para novas contas, use sempre `activate_account_complete.py` - ele é inteligente e só faz o necessário.

### 4. Scripts Específicos para Correções

Para contas existentes com problemas específicos, use scripts individuais:
- Falta Stripe ID? → `add_stripe_id_to_account.py`
- Admin errado? → `change_account_admin.py`
- KYC bloqueando? → `verify_user_identity.py`

---

## 🌍 Ambiente: Desenvolvimento vs Produção

### Desenvolvimento

```bash
# Stripe IDs fake são OK
stripe_id = f"acct_fake_{account_id.hex[:16]}"

# Bypass KYC é OK
identity_verification_status = "verified"

# Sandbox pode pular validações
if settings.ENV == Environment.sandbox:
    return True
```

### Produção

⚠️ **IMPORTANTE:**

1. **Stripe IDs reais:** Conectar conta real do Stripe
2. **KYC real:** Implementar verificação adequada para Brasil
3. **Não usar scripts de bypass:** Apenas para desenvolvimento

#### KYC para Brasil:

**Opções recomendadas:**
- **Idwall** (https://idwall.co) - Verificação CPF/CNPJ + facial
- **unico IDCheck** (https://unico.io) - Biometria facial
- **Serpro** (https://serpro.gov.br) - API oficial governo

**Implementar:**
```python
# Em vez de usar Stripe Identity
# Criar endpoint custom:
# POST /api/v1/kyc/verify
# - Upload documento
# - Validar CPF/CNPJ
# - Reconhecimento facial
# - Atualizar identity_verification_status após aprovação
```

---

## 📊 Localização dos Arquivos

### Scripts
```
server/scripts/
├── activate_account_complete.py       ⭐ FAZ TUDO
├── approve_account_for_payout.py      - Aprovar conta
├── verify_user_identity.py            - Bypass KYC
├── change_account_admin.py            - Mudar admin
├── add_stripe_id_to_account.py        - Adicionar Stripe ID
├── list_accounts_status.py            - Listar contas
├── check_organization_account.py      - Verificar org
├── create_and_approve_account.py      - Criar conta para org
├── create_user_account.py             - Criar conta para user
└── fix_organization_payment_ready.py  - Corrigir requisitos
```

### Modelos
```
server/polar/models/
├── account.py          - Modelo Account
├── organization.py     - Modelo Organization
└── user.py            - Modelo User (com IdentityVerificationStatus)
```

### Serviços
```
server/polar/
├── account/service.py           - Lógica de contas
├── organization/service.py      - Lógica de organizações
├── user/service.py             - Lógica de usuários
└── payout/service.py           - Lógica de payouts
```

---

## 🧪 Testes

### Testar Ativação Completa

```bash
# 1. Criar organização de teste via interface
# https://fluu.digital/dashboard

# 2. Ativar via script
uv run python -m scripts.activate_account_complete \
  --organization-slug test-org \
  --admin-email <EMAIL> \
  --no-dry-run

# 3. Verificar no dashboard
# https://fluu.digital/dashboard/test-org/finance/account
# Deve mostrar tudo pronto sem erros

# 4. Testar criação de produto
# https://fluu.digital/dashboard/test-org/products
```

---

## 🔍 Debug

### Ver Logs Estruturados

```python
import structlog
log = structlog.get_logger()

log.info("Checking account status", 
         account_id=account.id,
         status=account.status.value,
         payouts_enabled=account.is_payouts_enabled)
```

### Verificar Requisitos Manualmente

```python
from polar.organization.service import organization as org_service

is_ready = await org_service.is_organization_ready_for_payment(
    session, 
    organization
)
# Retorna True se pronta para pagamentos
```

### Verificar no Banco

```sql
-- Ver conta
SELECT id, status, is_payouts_enabled, is_charges_enabled, stripe_id, admin_id
FROM accounts WHERE id = 'ACCOUNT_UUID';

-- Ver organização
SELECT id, slug, status, details_submitted_at, account_id
FROM organizations WHERE slug = 'SLUG';

-- Ver usuário/admin
SELECT id, email, identity_verification_status, account_id
FROM users WHERE email = 'EMAIL';
```

---

## 📚 Referências

### Documentação
- `GUIA_APROVACAO_CONTAS.md` - Guia completo de uso
- `EXEMPLOS_USO_SCRIPTS.md` - 15 cenários práticos
- `SCRIPT_APROVAR_CONTA_SAQUE.md` - Referência técnica
- `RESULTADO_FINAL_APROVACAO.md` - Status final da implementação

### Código
- `server/polar/models/account.py:26` - Modelo Account
- `server/polar/models/user.py:42` - IdentityVerificationStatus
- `server/polar/organization/service.py:622` - is_organization_ready_for_payment
- `server/polar/payout/service.py:216` - Verificação de payout

---

## ⚠️ Avisos Importantes

1. **Scripts de bypass são para desenvolvimento apenas**
2. **Em produção, implemente KYC adequado**
3. **Stripe IDs fake não funcionam com Stripe real**
4. **Sempre teste em staging antes de produção**
5. **Faça backup do banco antes de operações em massa**
6. **Use dry-run primeiro sempre**

---

## 🎯 Resumo para Agentes de IA

**Quando usuário pede para ativar conta:**

1. Use `activate_account_complete.py` (faz tudo)
2. Se problema específico, use script individual
3. Sempre dry-run primeiro
4. Verifique resultado após execução
5. Teste no dashboard

**Prompt rápido:**
```
"Ative a conta da organização X com admin Y para receber pagamentos"
→ uv run python -m scripts.activate_account_complete --organization-slug X --admin-email Y --no-dry-run
```

---

**Criado:** 2025-11-10  
**Versão:** 1.0  
**Projeto:** Fluu (Polar Fork)

