# 🤖 RESUMO PARA AGENTES DE IA

## ⚡ TL;DR - Ativação de Contas

**Problema:** Usuário não consegue receber pagamentos  
**Causa:** Conta não está ativada  
**Solução:** Execute `activate_account_complete.py`

---

## 🎯 Comando Único - Resolva <PERSON>

```bash
cd /Users/<USER>/Documents/www/Gateways/polar/server

# FAZ TUDO automaticamente:
uv run python -m scripts.activate_account_complete \
  --organization-slug SLUG \
  --admin-email EMAIL \
  --no-dry-run
```

**Este comando:**
1. ✅ Cria ou atualiza Account
2. ✅ Define Stripe ID (fake para dev)
3. ✅ Habilita payouts e charges
4. ✅ Verifica identidade do admin (bypass KYC)
5. ✅ Ativa Organization
6. ✅ Vincula tudo corretamente

**Resultado:** Conta 100% funcional! 🎉

---

## 📋 Checklist de Ativação

Uma conta está ativa quando **TODOS** estes itens estão OK:

```python
✅ Account.status == "active"
✅ Account.is_payouts_enabled == True
✅ Account.is_charges_enabled == True  
✅ Account.is_details_submitted == True
✅ Account.stripe_id != None
✅ Account.admin.identity_verification_status == "verified"

# Se for Organization:
✅ Organization.status == "active"
✅ Organization.details_submitted_at != None
✅ Organization.account_id != None
```

**Se um item falhar → Conta não funciona!**

---

## 🚨 Problemas e Soluções Rápidas

### 1. "Payment processing is not yet available"
```bash
uv run python -m scripts.activate_account_complete --organization-slug X --admin-email Y --no-dry-run
```

### 2. 403 "You are not the admin of this account"
```bash
uv run python -m scripts.change_account_admin SLUG EMAIL --no-dry-run
```

### 3. 500 ao criar identity verification
```bash
uv run python -m scripts.verify_user_identity EMAIL --no-dry-run
```

### 4. Dashboard não reconhece conta
```bash
uv run python -m scripts.add_stripe_id_to_account --email EMAIL --no-dry-run
```

---

## 🗂️ Estrutura de Dados Simplificada

```
User (usuário/admin)
  ↓ (admin_id)
Account (conta de pagamento)
  ↓ (account_id)
Organization (vendedor)

Todos precisam estar ACTIVE e configurados!
```

---

## 📦 Scripts por Ordem de Uso

### 🌟 Sempre Use Primeiro
1. **activate_account_complete.py** - FAZ TUDO

### 🔧 Use Se Necessário
2. `verify_user_identity.py` - Bypass KYC
3. `change_account_admin.py` - Corrigir admin
4. `add_stripe_id_to_account.py` - Adicionar Stripe ID

### 🔍 Use Para Verificar
5. `check_organization_account.py` - Ver status
6. `list_accounts_status.py` - Listar contas

---

## 🎯 Regras para Agentes de IA

### ✅ SEMPRE Faça

1. **Dry-run primeiro** (é o padrão)
2. **Use `activate_account_complete.py`** como primeira opção
3. **Confirme resultado** após execução
4. **Mostre status antes e depois**

### ❌ NUNCA Faça

1. Não pule dry-run sem permissão
2. Não use múltiplos scripts se um resolve
3. Não aprove sem verificar detalhes
4. Não esqueça de confirmar resultado

### 💡 Boas Práticas

1. **Sempre mostre o que vai fazer** antes de executar
2. **Use dry-run** para demonstrar
3. **Pergunte confirmação** antes de `--no-dry-run`
4. **Verifique resultado** e informe usuário

---

## 🎓 Prompt Templates

### Template 1: Ativação Completa
```
Usuário: "Ative minha conta para receber pagamentos"

Resposta:
"Vou ativar sua conta. Primeiro vou verificar o status atual:

[executar dry-run]

Vou fazer as seguintes mudanças:
- [listar mudanças]

Posso executar? [aguardar confirmação]

[executar com --no-dry-run]

✅ Pronto! Sua conta está ativa.
```

### Template 2: Problema Específico
```
Usuário: "Dashboard mostra erro 403"

Resposta:
"O erro 403 indica que você não é o admin da conta.
Vou verificar e corrigir:

[executar change_account_admin]

✅ Corrigido! Recarregue o dashboard.
```

### Template 3: Verificar Apenas
```
Usuário: "Minha conta está ativa?"

Resposta:
"Vou verificar:

[executar check_organization_account]

Status: [informar resultado]
```

---

## 📚 Onde Encontrar Info

| Preciso de... | Documento |
|---------------|-----------|
| **Guia rápido para IA** | PROMPT_ATIVACAO_CONTAS.md ⭐ |
| **Referência cursor** | .cursorrules (seção Ativação) |
| **Arquitetura técnica** | DOCUMENTACAO_ATIVACAO_CONTAS.md |
| **Workflow completo** | GUIA_APROVACAO_CONTAS.md |
| **Exemplos práticos** | EXEMPLOS_USO_SCRIPTS.md |
| **Comandos rápidos** | Este arquivo |

---

## 🎯 Objetivo Final

**Deixar conta 100% funcional para:**
- ✅ Criar produtos
- ✅ Processar pagamentos (Stripe, PIX, Boleto)
- ✅ Receber payouts
- ✅ Dashboard sem erros

**Um comando resolve tudo:** `activate_account_complete.py` ⭐

---

## 🔄 Fluxo Típico

```
1. Usuário cria organização via web
   ↓
2. Dashboard: "Payment processing not available"
   ↓
3. Agente IA executa: activate_account_complete.py
   ↓
4. Conta ativada ✅
   ↓
5. Dashboard funcional ✅
   ↓
6. Pode vender! 🎉
```

---

## ⚠️ Importante

- **Scripts são para desenvolvimento** - Em produção, implementar KYC real
- **Sempre dry-run primeiro** - Segurança
- **Stripe IDs fake** - Apenas para dev local

---

## 🎉 Conclusão para IA

**Memorize isto:**

```
Ativar conta = activate_account_complete.py

Sempre:
1. Dry-run primeiro
2. Mostrar mudanças
3. Pedir confirmação
4. Executar
5. Confirmar resultado

Simples assim! 🚀
```

---

**Criado:** 2025-11-10  
**Para:** Agentes de IA (Claude, GPT, etc)  
**Projeto:** Fluu (Polar Fork)  
**Status:** ✅ Pronto para uso

