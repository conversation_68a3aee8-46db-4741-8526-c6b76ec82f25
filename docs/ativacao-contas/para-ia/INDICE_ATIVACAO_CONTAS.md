# 📑 Índice: Sistema de Ativação de Contas

## 🎯 Para Agentes de IA - LEIA ISTO PRIMEIRO

Se você é um agente de IA trabalhando neste projeto:

1. **Leia:** `PROMPT_ATIVACAO_CONTAS.md` ⭐
2. **Consulte:** `.cursorrules` (seção "Sistema de Ativação de Contas")
3. **Use:** `activate_account_complete.py` para ativar contas

---

## 📚 Documentação Disponível

### 🤖 Para Agentes de IA

| Arquivo | Propósito | Tempo |
|---------|-----------|-------|
| **PROMPT_ATIVACAO_CONTAS.md** ⭐ | Guia completo para IA | 10 min |
| **.cursorrules** | Referência rápida | 2 min |

### 👨‍💻 Para Desenvolvedores

| Arquivo | Propósito | Tempo |
|---------|-----------|-------|
| **DOCUMENTACAO_ATIVACAO_CONTAS.md** | Arquitetura e implementação | 20 min |
| **GUIA_APROVACAO_CONTAS.md** | Workflow completo | 15 min |
| **EXEMPLOS_USO_SCRIPTS.md** | 15 cenários práticos | 10 min |

### 📄 Referências

| Arquivo | Propósito |
|---------|-----------|
| **SCRIPT_APROVAR_CONTA_SAQUE.md** | Documentação técnica do script de aprovação |
| **RESULTADO_FINAL_APROVACAO.md** | Status da implementação |
| **START_HERE.md** | Ponto de entrada para usuários |

---

## 🚀 Ação Rápida

### Usuário Pede: "Ative minha conta para receber pagamentos"

```bash
# 1. Verifique status atual (dry-run)
uv run python -m scripts.activate_account_complete \
  --organization-slug SLUG \
  --admin-email EMAIL

# 2. Execute ativação
uv run python -m scripts.activate_account_complete \
  --organization-slug SLUG \
  --admin-email EMAIL \
  --no-dry-run

# 3. Confirme resultado
uv run python -m scripts.check_organization_account SLUG
```

---

## 🔑 Conceitos-Chave (30 segundos)

### Account (Conta de Pagamento)
- Gerencia recebimentos e payouts
- Precisa: `status=ACTIVE`, `is_payouts_enabled=True`, `stripe_id!=null`
- Admin: User com identidade verificada

### Organization (Organização)
- Entidade que vende produtos
- Precisa: `status=active`, `account_id!=null`, `details_submitted_at!=null`

### User (Usuário/Admin)
- Controla a conta
- Precisa: `identity_verification_status=verified` ou `pending`

### Requisito para Funcionar
```
Account ACTIVE + Payouts Enabled + Admin Verified = ✅ Pronta para Vender
```

---

## 📦 Scripts (10 Total)

### ⭐ Principal

```bash
activate_account_complete.py  # FAZ TUDO - use este!
```

### 🔧 Específicos

```bash
approve_account_for_payout.py      # Aprovar conta
verify_user_identity.py            # Bypass KYC (dev)
change_account_admin.py            # Mudar admin
add_stripe_id_to_account.py        # Adicionar Stripe ID
list_accounts_status.py            # Listar/verificar
check_organization_account.py      # Status org
create_user_account.py             # Criar conta user
create_and_approve_account.py      # Criar conta org
fix_organization_payment_ready.py  # Corrigir requisitos
```

---

## 🚨 Problemas Comuns (Tabela de Decisão)

| Sintoma | Diagnóstico | Script | Comando |
|---------|-------------|--------|---------|
| "Payment processing not available" | Org sem conta | `activate_account_complete.py` | `--organization-slug X --admin-email Y --no-dry-run` |
| 403 "Not the admin" | Admin errado | `change_account_admin.py` | `SLUG EMAIL --no-dry-run` |
| 500 ao criar verification | KYC bloqueado | `verify_user_identity.py` | `EMAIL --no-dry-run` |
| Dashboard não reconhece conta | Sem Stripe ID | `add_stripe_id_to_account.py` | `--email EMAIL --no-dry-run` |

---

## 🎯 Fluxo de Decisão

```
Usuário pede ativação
       ↓
Primeira vez? → SIM → activate_account_complete.py ⭐
       ↓
      NÃO
       ↓
Problema específico? → SIM → Consultar tabela acima
       ↓
      NÃO
       ↓
Já está ativo? → Informar usuário
```

---

## 🔍 Como Verificar Status

```bash
# Ver organização completa
uv run python -m scripts.check_organization_account SLUG

# Ver conta específica
uv run python -m scripts.list_accounts_status show --email EMAIL

# Listar todas pendentes
uv run python -m scripts.list_accounts_status list-accounts --not-payout-ready
```

---

## ⚠️ Avisos Importantes

### ✅ Desenvolvimento
- Stripe IDs fake são OK
- Bypass de KYC é OK
- Scripts fazem tudo automaticamente

### ❌ Produção
- **NÃO use Stripe IDs fake**
- **NÃO use bypass de KYC**
- **Implemente KYC para Brasil** (Idwall, unico, Serpro)

---

## 📖 Leitura por Perfil

### Agente de IA
```
1. PROMPT_ATIVACAO_CONTAS.md (leitura obrigatória)
2. .cursorrules seção "Ativação de Contas"
3. Este índice para referência rápida
```

### Desenvolvedor Backend
```
1. DOCUMENTACAO_ATIVACAO_CONTAS.md (arquitetura)
2. GUIA_APROVACAO_CONTAS.md (workflow)
3. Código em server/scripts/*.py
```

### Desenvolvedor Full-Stack
```
1. GUIA_APROVACAO_CONTAS.md (workflow completo)
2. EXEMPLOS_USO_SCRIPTS.md (casos práticos)
3. DOCUMENTACAO_ATIVACAO_CONTAS.md (quando necessário)
```

### DevOps/SRE
```
1. EXEMPLOS_USO_SCRIPTS.md (automação)
2. GUIA_APROVACAO_CONTAS.md (operações)
3. Scripts em server/scripts/
```

---

## 🎓 Aprendizado Progressivo

### Nível 1: Básico (5 min)
- Leia este índice
- Entenda: Account + Organization + User = Sistema funcionando
- Aprenda comando: `activate_account_complete.py`

### Nível 2: Intermediário (20 min)
- Leia `.cursorrules` seção de ativação
- Leia `PROMPT_ATIVACAO_CONTAS.md`
- Teste scripts em dry-run

### Nível 3: Avançado (1h)
- Leia `DOCUMENTACAO_ATIVACAO_CONTAS.md`
- Leia `GUIA_APROVACAO_CONTAS.md`
- Estude código dos scripts

### Nível 4: Expert (2h+)
- Leia todos os documentos
- Entenda arquitetura completa
- Contribua com melhorias

---

## 🔗 Links Rápidos

### Documentação
- [Prompt para IA](PROMPT_ATIVACAO_CONTAS.md) ⭐
- [Documentação Técnica](DOCUMENTACAO_ATIVACAO_CONTAS.md)
- [Guia Completo](GUIA_APROVACAO_CONTAS.md)
- [15 Exemplos](EXEMPLOS_USO_SCRIPTS.md)
- [Cursor Rules](.cursorrules)

### Código
- [Scripts](server/scripts/)
- [Modelo Account](server/polar/models/account.py)
- [Modelo Organization](server/polar/models/organization.py)
- [Modelo User](server/polar/models/user.py)

---

## 📊 Estatísticas

- **10 scripts** criados
- **6 documentos** de ativação
- **1 seção** no .cursorrules
- **4 modelos** envolvidos
- **5 minutos** para ativar nova conta
- **100%** automatizado

---

## 🎯 TL;DR - Resumo Executivo

**O que é:** Sistema para ativar contas antes de receber pagamentos

**Por que:** Dashboard exige Account ativa + Admin verificado + Stripe ID

**Como:** Use `activate_account_complete.py` - faz tudo automaticamente

**Quando:** Sempre que criar nova organização ou conta

**Onde:** `server/scripts/activate_account_complete.py`

**Documentação:** `PROMPT_ATIVACAO_CONTAS.md` para IA, `DOCUMENTACAO_ATIVACAO_CONTAS.md` para humanos

---

**Criado:** 2025-11-10  
**Versão:** 1.0  
**Manutenção:** Atualizar quando adicionar novos scripts ou requisitos

