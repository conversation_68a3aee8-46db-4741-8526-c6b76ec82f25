# 🤖 IA: Referência Rápida - Ativação de Contas

## ⚡ COMANDO ÚNICO - MEMORIZE ISTO

```bash
cd /Users/<USER>/Documents/www/Gateways/polar/server
uv run python -m scripts.activate_account_complete \
  --organization-slug SLUG \
  --admin-email EMAIL \
  --no-dry-run
```

**Isto resolve 95% dos problemas de ativação de conta!**

---

## 🎯 Quando Usar

| Usuário Diz | Use Este Comando |
|-------------|------------------|
| "Ative minha conta" | `activate_account_complete.py` |
| "Não consigo receber pagamentos" | `activate_account_complete.py` |
| "Payment processing not available" | `activate_account_complete.py` |
| "403 erro no dashboard" | `change_account_admin.py` depois `activate_account_complete.py` |
| "500 ao verificar identidade" | `verify_user_identity.py` depois `activate_account_complete.py` |

---

## 📋 Checklist Rápido

Conta está ativa se **TODOS** forem true:

```
✅ Account.status == "active"
✅ Account.is_payouts_enabled == True
✅ Account.stripe_id != None
✅ User.identity_verification_status == "verified"
✅ Organization.status == "active" (se org)
✅ Organization.details_submitted_at != None (se org)
```

**Falta um? → Conta NÃO funciona!**

---

## 🔧 Scripts por Problema

| Problema | Script | Tempo |
|----------|--------|-------|
| Qualquer coisa | `activate_account_complete.py` ⭐ | 5s |
| Erro 403 | `change_account_admin.py` | 3s |
| KYC bloqueando | `verify_user_identity.py` | 3s |
| Sem Stripe ID | `add_stripe_id_to_account.py` | 3s |
| Verificar status | `check_organization_account.py` | 2s |

---

## 🎓 Workflow para IA

```
1. Usuário pede ativação
   ↓
2. Execute dry-run
   ↓
3. Mostre mudanças
   ↓
4. Peça confirmação
   ↓
5. Execute com --no-dry-run
   ↓
6. Confirme resultado
   ↓
7. Instrua usuário a recarregar dashboard
```

---

## 📚 Documentação

| Para | Arquivo | Leia Isto |
|------|---------|-----------|
| **IA** | PROMPT_ATIVACAO_CONTAS.md ⭐ | Sim, agora |
| **IA** | .cursorrules (linha 213-371) | Sim, agora |
| Humano | GUIA_APROVACAO_CONTAS.md | Se necessário |
| Técnico | DOCUMENTACAO_ATIVACAO_CONTAS.md | Se necessário |

---

## ⚠️ LEMBRE-SE

1. **Sempre dry-run primeiro** (é o padrão)
2. **Use activate_account_complete.py** - faz tudo
3. **Confirme resultado** sempre
4. **Instrua usuário** a recarregar dashboard

---

## 🎯 Exemplos de Resposta

### Exemplo 1: Ativação Simples

**Usuário:** "Ative minha conta para vender"

**Você:**
```
Vou ativar sua conta. Primeiro vou verificar o status:

[execute dry-run]

Preciso fazer:
- Criar conta de pagamento
- Habilitar saques e cobranças  
- Verificar sua identidade
- Ativar organização

Posso executar?

[após confirmação]

[execute --no-dry-run]

✅ Pronto! Sua conta está ativa.
Recarregue o dashboard: https://fluu.digital/dashboard/SLUG
```

### Exemplo 2: Erro 403

**Usuário:** "Dashboard mostra 403"

**Você:**
```
O erro 403 indica que você não é o admin da conta.
Vou corrigir:

[execute change_account_admin]

✅ Corrigido! Agora vou garantir que tudo está ativo:

[execute activate_account_complete]

✅ Pronto! Recarregue o dashboard.
```

---

## 🎊 FIM

**Leia:** `PROMPT_ATIVACAO_CONTAS.md` para detalhes completos.

**Use:** `activate_account_complete.py` para resolver tudo.

**Seja feliz!** 🎉

