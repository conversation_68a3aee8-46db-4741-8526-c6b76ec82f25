# Script de Aprovação de Conta para Saque

## 📋 Visão Geral

Script para aprovar contas para receber saques (payouts) no sistema Polar/Fluu.

**Localização**: `server/scripts/approve_account_for_payout.py`

## 🎯 O Que o Script Faz

O script permite aprovar uma conta para receber pagamentos (payouts), realizando as seguintes mudanças:

1. ✅ Altera o **status da conta** para `ACTIVE`
2. ✅ Habilita **saques** (`is_payouts_enabled = True`)
3. ✅ Habilita **cobranças** (`is_charges_enabled = True`)
4. ✅ Marca **detalhes como enviados** (`is_details_submitted = True`)
5. ✅ Define **threshold de próxima revisão** (padrão: 10.000 centavos = $100)
6. ✅ Atualiza automaticamente **organizações vinculadas**

## 🔍 Como Encontrar uma Conta

O script suporta 3 formas de buscar uma conta:

### 1. Por ID da Conta (UUID)
```bash
uv run python -m scripts.approve_account_for_payout --account-id "550e8400-e29b-41d4-a716-************"
```

### 2. Por Email do Administrador
```bash
uv run python -m scripts.approve_account_for_payout --email "<EMAIL>"
```

### 3. Por Slug da Organização
```bash
uv run python -m scripts.approve_account_for_payout --organization-slug "my-organization"
```

## 🚀 Uso

### Modo Dry-Run (Padrão)

Por padrão, o script **NÃO APLICA MUDANÇAS**, apenas mostra o que seria feito:

```bash
# Por ID da conta
uv run python -m scripts.approve_account_for_payout --account-id ACCOUNT_UUID

# Por email
uv run python -m scripts.approve_account_for_payout --email <EMAIL>

# Por organização
uv run python -m scripts.approve_account_for_payout --organization-slug my-org
```

**Saída esperada (dry-run)**:
```
🔍 Conta encontrada (Email: <EMAIL>)

📊 Status Atual:
   ID: 550e8400-e29b-41d4-a716-************
   Admin: <EMAIL>
   Tipo: stripe
   Status: under_review (Under Review)
   País: BR
   Moeda: BRL
   Detalhes Enviados: ✗
   Cobranças Habilitadas: ✗
   Saques Habilitados: ✗
   Próxima Revisão: 0
   ⚠️  Em Revisão - Saques Bloqueados

📝 Mudanças a serem aplicadas:
   • Status: under_review → ACTIVE
   • Saques: Desabilitados → Habilitados
   • Cobranças: Desabilitadas → Habilitadas
   • Detalhes: Não enviados → Enviados
   • Threshold: 0 → 10000

🏃 DRY RUN MODE - Nenhuma mudança foi aplicada
   Use --no-dry-run para aplicar as mudanças
```

### Executar Aprovação Real

Para aplicar as mudanças, adicione `--no-dry-run`:

```bash
# Aprovar conta por ID
uv run python -m scripts.approve_account_for_payout \
  --account-id ACCOUNT_UUID \
  --no-dry-run

# Aprovar conta por email
uv run python -m scripts.approve_account_for_payout \
  --email <EMAIL> \
  --no-dry-run

# Aprovar conta por organização
uv run python -m scripts.approve_account_for_payout \
  --organization-slug my-org \
  --no-dry-run
```

**Saída esperada (aprovação real)**:
```
🔍 Conta encontrada (Email: <EMAIL>)

📊 Status Atual:
   ID: 550e8400-e29b-41d4-a716-************
   Admin: <EMAIL>
   Status: under_review (Under Review)
   ⚠️  Em Revisão - Saques Bloqueados

📝 Mudanças a serem aplicadas:
   • Status: under_review → ACTIVE
   • Saques: Desabilitados → Habilitados
   • Cobranças: Desabilitadas → Habilitadas
   • Detalhes: Não enviados → Enviados
   • Threshold: 0 → 10000

📋 Atualizando 1 organização(ões) vinculada(s):
   ✓ my-org aprovada

✅ Conta aprovada com sucesso!

📊 Status Após Aprovação:
   ID: 550e8400-e29b-41d4-a716-************
   Admin: <EMAIL>
   Status: active (Active)
   Detalhes Enviados: ✓
   Cobranças Habilitadas: ✓
   Saques Habilitados: ✓
   Próxima Revisão: 10000
   ✅ Pronta para Saques
```

### Personalizar Threshold de Revisão

Por padrão, o threshold é 10.000 centavos ($100). Você pode alterá-lo:

```bash
uv run python -m scripts.approve_account_for_payout \
  --account-id ACCOUNT_UUID \
  --next-review-threshold 50000 \
  --no-dry-run
```

Valores comuns:
- `10000` = $100 (padrão)
- `50000` = $500
- `100000` = $1.000
- `1000000` = $10.000

## 🎯 Casos de Uso

### 1. Aprovar Conta em Revisão Manual

Quando uma conta está em `UNDER_REVIEW` e foi analisada manualmente:

```bash
uv run python -m scripts.approve_account_for_payout \
  --email <EMAIL> \
  --no-dry-run
```

### 2. Habilitar Saques para Conta Existente

Se uma conta está `ACTIVE` mas não tem saques habilitados:

```bash
uv run python -m scripts.approve_account_for_payout \
  --organization-slug my-saas \
  --no-dry-run
```

### 3. Verificar Status Sem Alterar

Usar dry-run para apenas inspecionar o status:

```bash
uv run python -m scripts.approve_account_for_payout \
  --email <EMAIL>
```

### 4. Aprovar Múltiplas Contas

```bash
# Listar emails/IDs de contas a aprovar
for email in "<EMAIL>" "<EMAIL>" "<EMAIL>"; do
  uv run python -m scripts.approve_account_for_payout --email "$email" --no-dry-run
done
```

## 🔐 Requisitos de Saque

Para uma conta estar **pronta para saques**, ela precisa:

1. ✅ Status = `ACTIVE`
2. ✅ `is_payouts_enabled = True`
3. ✅ `is_charges_enabled = True`
4. ✅ `is_details_submitted = True`
5. ✅ Para contas Stripe: verificar `payouts_enabled` no Stripe

O script garante que todos esses requisitos sejam atendidos.

## 📊 Status de Conta

### Status Possíveis

| Status | Descrição | Saques |
|--------|-----------|--------|
| `CREATED` | Conta criada, não configurada | ❌ |
| `ONBOARDING_STARTED` | Onboarding iniciado | ❌ |
| `UNDER_REVIEW` | Em revisão manual | ❌ |
| `DENIED` | Negada | ❌ |
| `ACTIVE` | Aprovada e ativa | ✅ |

### Flags Importantes

- **is_payouts_enabled**: Permite receber saques
- **is_charges_enabled**: Permite processar pagamentos
- **is_details_submitted**: Detalhes de onboarding enviados
- **next_review_threshold**: Valor em centavos para próxima revisão

## 🛠️ Troubleshooting

### Erro: "Conta não encontrada"

```bash
❌ Conta não encontrada (Email: <EMAIL>)
```

**Soluções**:
1. Verificar se o email está correto
2. Tentar buscar por ID da conta
3. Tentar buscar pelo slug da organização
4. Verificar no banco de dados diretamente

### Erro: "Você deve fornecer pelo menos um dos seguintes"

```bash
❌ Erro: Você deve fornecer pelo menos um dos seguintes:
   --account-id
   --email
   --organization-slug
```

**Solução**: Fornecer pelo menos um parâmetro de busca.

### Conta já aprovada

```bash
✓ Conta já está aprovada para saques!
```

**Info**: A conta já está configurada corretamente, nada a fazer.

## 🔄 Sincronização com Organizações

O script automaticamente:

1. Atualiza o status da conta
2. Sincroniza o status com **todas as organizações vinculadas**
3. Usa `organization_service.confirm_organization_reviewed()` para cada organização

Isso garante consistência entre:
- `Account.status`
- `Organization.status`
- Capacidades de pagamento

## 🧪 Testando

### 1. Primeiro, sempre rode em dry-run

```bash
uv run python -m scripts.approve_account_for_payout --email <EMAIL>
```

### 2. Verifique as mudanças propostas

Leia a saída cuidadosamente e confirme que as mudanças fazem sentido.

### 3. Execute com --no-dry-run

```bash
uv run python -m scripts.approve_account_for_payout --email <EMAIL> --no-dry-run
```

### 4. Verifique o resultado

Confira o "Status Após Aprovação" e verifique que tudo está correto.

## ⚠️ Avisos Importantes

1. **Sempre teste em dry-run primeiro**
2. **Use em ambiente de desenvolvimento/staging antes de produção**
3. **Verifique se a conta realmente deve ser aprovada**
4. **Para contas Stripe, o Stripe também precisa aprovar**
5. **Contas negadas (DENIED) podem ser revertidas, mas cuidado**

## 📚 Ver Também

- `server/polar/models/account.py` - Modelo Account
- `server/polar/organization/service.py` - Serviço de organizações
- `server/polar/payout/service.py` - Serviço de payouts
- `ARQUITETURA_MULTI_GATEWAY.md` - Arquitetura do sistema

## 🤝 Suporte

Se tiver problemas:

1. Verifique os logs do servidor
2. Consulte a documentação da API
3. Use o modo dry-run para debug
4. Verifique o banco de dados diretamente se necessário

---

**Última atualização**: 2025-11-10

