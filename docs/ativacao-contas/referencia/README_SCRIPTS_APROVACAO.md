# 🚀 Scripts de Aprovação de Contas - Guia Rápido

## 📦 O Que Foi Criado

Dois scripts Python para gerenciar aprovações de contas para saque:

1. **`list_accounts_status.py`** - Listar e inspecionar contas
2. **`approve_account_for_payout.py`** - Aprovar contas para saque

## ⚡ Uso Rápido

### Ver contas pendentes

```bash
uv run python -m scripts.list_accounts_status --not-payout-ready
```

### Ver detalhes de uma conta

```bash
uv run python -m scripts.list_accounts_status show --email <EMAIL>
```

### Aprovar uma conta (dry run)

```bash
uv run python -m scripts.approve_account_for_payout --email <EMAIL>
```

### Aprovar uma conta (executar)

```bash
uv run python -m scripts.approve_account_for_payout --email <EMAIL> --no-dry-run
```

## 📁 Arquivos Criados

```
polar/
├── server/scripts/
│   ├── approve_account_for_payout.py          # Script de aprovação
│   ├── list_accounts_status.py                # Script de listagem
│   └── README_APPROVE_ACCOUNT.md              # Guia rápido
│
├── GUIA_APROVACAO_CONTAS.md                   # 📘 Guia completo com exemplos
├── SCRIPT_APROVAR_CONTA_SAQUE.md              # 📖 Documentação detalhada
└── README_SCRIPTS_APROVACAO.md                # 📄 Este arquivo
```

## 🎯 Workflow Recomendado

```bash
# 1️⃣ Listar contas pendentes
uv run python -m scripts.list_accounts_status --not-payout-ready

# 2️⃣ Ver detalhes de uma conta específica
uv run python -m scripts.list_accounts_status show --email <EMAIL>

# 3️⃣ Simular aprovação (dry run)
uv run python -m scripts.approve_account_for_payout --email <EMAIL>

# 4️⃣ Aprovar (se tudo OK)
uv run python -m scripts.approve_account_for_payout --email <EMAIL> --no-dry-run

# 5️⃣ Confirmar
uv run python -m scripts.list_accounts_status show --email <EMAIL>
```

## 📚 Documentação

| Arquivo | Conteúdo |
|---------|----------|
| **GUIA_APROVACAO_CONTAS.md** | Guia completo com workflow, casos de uso e exemplos |
| **SCRIPT_APROVAR_CONTA_SAQUE.md** | Documentação detalhada do script de aprovação |
| **server/scripts/README_APPROVE_ACCOUNT.md** | Referência rápida de comandos |

## 🔑 Comandos Principais

### Listagem

| Comando | Descrição |
|---------|-----------|
| `list_accounts_status` | Listar todas as contas |
| `list_accounts_status --not-payout-ready` | Apenas não aprovadas |
| `list_accounts_status --status under_review` | Apenas em revisão |
| `list_accounts_status show --email EMAIL` | Detalhes de conta |

### Aprovação

| Comando | Descrição |
|---------|-----------|
| `approve_account_for_payout --email EMAIL` | Dry run |
| `approve_account_for_payout --email EMAIL --no-dry-run` | Executar |
| `approve_account_for_payout --account-id UUID --no-dry-run` | Por ID |
| `approve_account_for_payout --organization-slug SLUG --no-dry-run` | Por org |

## ✅ O Que o Script de Aprovação Faz

1. ✅ Altera status para `ACTIVE`
2. ✅ Habilita saques (`is_payouts_enabled`)
3. ✅ Habilita cobranças (`is_charges_enabled`)
4. ✅ Marca detalhes como enviados
5. ✅ Define threshold de revisão (padrão: $100)
6. ✅ Atualiza organizações vinculadas

## 🛡️ Segurança

- ✅ Modo dry-run por padrão
- ✅ Validação de entrada
- ✅ Logs detalhados
- ✅ Confirmação visual antes de aplicar
- ✅ Transações atômicas

## 📊 Status de Conta

| Emoji | Status | Pode Receber Saques? |
|-------|--------|---------------------|
| ⏳ | Created | ❌ |
| ⏳ | Onboarding Started | ❌ |
| ⚠️ | Under Review | ❌ |
| ❌ | Denied | ❌ |
| ✅ | Active | ✅ |

## 🎨 Exemplos Visuais

### Saída do Script de Listagem

```
📊 Total de contas: 15

==============================================================================
Status                 | Admin Email                         | Capabilities
==============================================================================
⚠️  Under Review        | <EMAIL>              | Saques✗ | Cobranças✗
⏳ Created              | <EMAIL>              | Saques✗ | Cobranças✗
✅ Active               | <EMAIL>              | Saques✓ | Cobranças✓
==============================================================================

✅ Prontas para saque: 5
⚠️  NÃO prontas para saque: 10
```

### Saída do Script de Aprovação

```
🔍 Conta encontrada (Email: <EMAIL>)

📝 Mudanças a serem aplicadas:
   • Status: under_review → ACTIVE
   • Saques: Desabilitados → Habilitados
   • Cobranças: Desabilitadas → Habilitadas

✅ Conta aprovada com sucesso!
```

## ⚠️ Importante

1. **Sempre rode dry-run primeiro** (é o padrão)
2. Teste em desenvolvimento antes de produção
3. Verifique se a conta deve mesmo ser aprovada
4. Faça backup do banco antes de operações em massa

## 🆘 Precisa de Ajuda?

1. Leia **GUIA_APROVACAO_CONTAS.md** para workflow completo
2. Consulte **SCRIPT_APROVAR_CONTA_SAQUE.md** para referência detalhada
3. Use `--help` em qualquer comando

```bash
uv run python -m scripts.list_accounts_status --help
uv run python -m scripts.approve_account_for_payout --help
```

## 🚦 Começar Agora

```bash
# Veja quais contas precisam de aprovação
uv run python -m scripts.list_accounts_status --not-payout-ready

# Siga o workflow acima! ☝️
```

---

**Criado em:** 2025-11-10  
**Localização:** `/Users/<USER>/Documents/www/Gateways/polar/`

