# 📘 Documentação: Sistema de Ativação de Contas

## 🎯 Visão Geral

O sistema Fluu/Polar requer que contas sejam aprovadas antes de processar pagamentos. Este documento explica como funciona o sistema de ativação e como usá-lo.

---

## 📐 Arquitetura

### Fluxo de Ativação

```
┌─────────────────────────────────────────────────────────────────┐
│                     CRIAÇÃO DE ORGANIZAÇÃO                       │
│                    (via Interface Web)                           │
└────────────────────────┬────────────────────────────────────────┘
                         │
                         ▼
┌─────────────────────────────────────────────────────────────────┐
│              ORGANIZAÇÃO CRIADA (Status: created)                │
│  • Sem conta vinculada (account_id = null)                      │
│  • Sem details_submitted_at                                     │
│  • Dashboard: "Payment processing not available"                │
└────────────────────────┬────────────────────────────────────────┘
                         │
                         ▼
┌─────────────────────────────────────────────────────────────────┐
│              EXECUTAR SCRIPT DE ATIVAÇÃO                         │
│         uv run python -m scripts.activate_account_complete       │
│           --organization-slug SLUG --admin-email EMAIL           │
│                      --no-dry-run                                │
└────────────────────────┬────────────────────────────────────────┘
                         │
                         ▼
┌─────────────────────────────────────────────────────────────────┐
│                   SCRIPT FAZ AUTOMATICAMENTE:                    │
│                                                                  │
│  1. Cria Account se não existir                                 │
│     ├─ status = ACTIVE                                          │
│     ├─ is_payouts_enabled = True                                │
│     ├─ is_charges_enabled = True                                │
│     ├─ is_details_submitted = True                              │
│     ├─ stripe_id = "acct_fake_..." (para dev)                   │
│     └─ admin_id = User.id                                       │
│                                                                  │
│  2. Vincula Account à Organization                              │
│     └─ organization.account_id = account.id                     │
│                                                                  │
│  3. Atualiza Organization                                       │
│     ├─ status = ACTIVE                                          │
│     ├─ details_submitted_at = now()                             │
│     └─ details = {...}                                          │
│                                                                  │
│  4. Verifica Identidade do Admin (bypass KYC)                   │
│     └─ user.identity_verification_status = "verified"           │
│                                                                  │
│  5. Vincula User à Account                                      │
│     └─ user.account_id = account.id                             │
│                                                                  │
└────────────────────────┬────────────────────────────────────────┘
                         │
                         ▼
┌─────────────────────────────────────────────────────────────────┐
│               CONTA ATIVADA E PRONTA! ✅                         │
│                                                                  │
│  • Organization.status = "active"                               │
│  • Account.status = "active"                                    │
│  • User.identity_verification_status = "verified"               │
│  • Dashboard: Totalmente funcional                              │
│  • Pode: Criar produtos, processar pagamentos, receber payouts  │
│                                                                  │
└─────────────────────────────────────────────────────────────────┘
```

---

## 🔍 Verificações do Sistema

### Dashboard Frontend

O dashboard verifica se está pronto para pagamentos checando:

```typescript
// ClientPage.tsx
const isPaymentReady = (
  organization.details_submitted_at !== null &&
  organizationAccount?.stripe_id !== null &&
  organizationAccount?.is_details_submitted === true &&
  organizationAccount?.is_payouts_enabled === true &&
  adminUser.identity_verification_status in ['verified', 'pending']
)
```

### Backend API

```python
# organization/service.py
async def is_organization_ready_for_payment(
    session: AsyncReadSession, 
    organization: Organization
) -> bool:
    # 1. Sandbox sempre permite
    if settings.ENV == Environment.sandbox:
        return True
    
    # 2. Organização não bloqueada/negada
    if organization.is_blocked() or organization.status == DENIED:
        return False
    
    # 3. Details submetidos
    if not organization.details_submitted_at or not organization.details:
        return False
    
    # 4. Tem conta ativa
    if organization.account_id is None:
        return False
    
    # 5. Admin com identidade verificada
    admin = account.admin
    if admin.identity_verification_status not in [verified, pending]:
        return False
    
    return True
```

---

## 🗂️ Estrutura de Dados

### Account (Conta de Pagamento)

```python
class Account(RecordModel):
    __tablename__ = "accounts"
    
    # Status da conta
    status: Mapped[Status] = mapped_column(
        StringEnum(Status),
        nullable=False,
        default=Status.CREATED
    )
    # Status possíveis:
    # - CREATED: Conta criada
    # - ONBOARDING_STARTED: Onboarding iniciado
    # - UNDER_REVIEW: Em revisão
    # - DENIED: Negada
    # - ACTIVE: Ativa ✅
    
    # Configurações de pagamento
    is_payouts_enabled: Mapped[bool] = mapped_column(Boolean, nullable=False)
    is_charges_enabled: Mapped[bool] = mapped_column(Boolean, nullable=False)
    is_details_submitted: Mapped[bool] = mapped_column(Boolean, nullable=False)
    
    # Gateway
    account_type: Mapped[AccountType] = mapped_column(
        StringEnum(AccountType),
        nullable=False
    )  # stripe, pagarme, mercadopago
    
    stripe_id: Mapped[str | None] = mapped_column(String(100), nullable=True)
    
    # Admin da conta
    admin_id: Mapped[UUID] = mapped_column(Uuid, ForeignKey("users.id"))
    admin: Mapped["User"] = relationship("User", lazy="raise")
    
    # Localização
    country: Mapped[str] = mapped_column(String(2), nullable=False)  # BR
    currency: Mapped[str] = mapped_column(String(3))  # BRL
    
    # Threshold de revisão
    next_review_threshold: Mapped[int | None] = mapped_column(
        Integer, 
        nullable=True
    )
```

### Organization (Organização)

```python
class Organization(RecordModel):
    __tablename__ = "organizations"
    
    # Identificação
    slug: Mapped[str] = mapped_column(String, unique=True, nullable=False)
    name: Mapped[str] = mapped_column(String, nullable=False)
    
    # Status
    status: Mapped[OrganizationStatus] = mapped_column(
        StringEnum(OrganizationStatus),
        nullable=False
    )
    # Status possíveis:
    # - created: Criada
    # - onboarding_started: Onboarding iniciado
    # - under_review: Em revisão
    # - denied: Negada
    # - active: Ativa ✅
    
    # Conta vinculada
    account_id: Mapped[UUID | None] = mapped_column(
        Uuid,
        ForeignKey("accounts.id"),
        nullable=True
    )
    account: Mapped["Account"] = relationship("Account")
    
    # Detalhes do negócio
    details: Mapped[dict[str, Any]] = mapped_column(JSONB, nullable=False)
    details_submitted_at: Mapped[datetime | None] = mapped_column(
        TIMESTAMP(timezone=True),
        nullable=True
    )
    
    # Threshold
    next_review_threshold: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        default=0
    )
```

### User (Usuário/Admin)

```python
class IdentityVerificationStatus(StrEnum):
    unverified = "unverified"  # Não verificado
    pending = "pending"        # Em processo
    verified = "verified"      # Verificado ✅
    failed = "failed"          # Falhou

class User(RecordModel):
    __tablename__ = "users"
    
    # Identificação
    email: Mapped[str] = mapped_column(String(320), nullable=False)
    is_admin: Mapped[bool] = mapped_column(Boolean, nullable=False, default=False)
    
    # Conta pessoal
    account_id: Mapped[UUID | None] = mapped_column(
        Uuid,
        ForeignKey("accounts.id"),
        nullable=True
    )
    
    # Verificação de identidade (KYC)
    identity_verification_status: Mapped[IdentityVerificationStatus] = mapped_column(
        StringEnum(IdentityVerificationStatus),
        nullable=False,
        default=IdentityVerificationStatus.unverified
    )
    
    identity_verification_id: Mapped[str | None] = mapped_column(
        String,
        nullable=True,
        unique=True
    )  # ID da sessão de verificação no Stripe Identity
    
    @property
    def identity_verified(self) -> bool:
        return self.identity_verification_status == IdentityVerificationStatus.verified
```

---

## 🔧 Implementação dos Scripts

### Script Principal: activate_account_complete.py

```python
"""
Script que ativa conta completamente.
Faz TUDO automaticamente em uma única execução.
"""

async def activate(
    organization_slug: str | None,
    admin_email: str | None,
    user_email: str | None,
    dry_run: bool = True
):
    # 1. Buscar ou criar Account
    if not account:
        account = Account(
            account_type=AccountType.stripe,
            admin_id=admin.id,
            country="BR",
            currency="BRL",
            status=Account.Status.ACTIVE,
            is_details_submitted=True,
            is_charges_enabled=True,
            is_payouts_enabled=True,
            stripe_id=f"acct_fake_{admin.id.hex[:16]}",
            next_review_threshold=10000,
        )
        session.add(account)
    
    # 2. Atualizar Account existente
    else:
        account.status = Account.Status.ACTIVE
        account.is_payouts_enabled = True
        account.is_charges_enabled = True
        account.is_details_submitted = True
        account.admin_id = admin.id
        if not account.stripe_id:
            account.stripe_id = f"acct_fake_{account.id.hex[:16]}"
    
    # 3. Verificar identidade do admin
    if not admin.identity_verified:
        await user_repository.update(
            admin,
            update_dict={
                "identity_verification_status": IdentityVerificationStatus.verified
            }
        )
    
    # 4. Vincular user à account
    if not admin.account_id:
        admin.account_id = account.id
    
    # 5. Atualizar Organization (se aplicável)
    if organization:
        organization.status = OrganizationStatus.ACTIVE
        organization.account_id = account.id
        organization.details_submitted_at = datetime.now(UTC)
        if not organization.details:
            organization.details = {
                "business": {"name": organization.name, "type": "individual"}
            }
    
    await session.commit()
```

---

## 🚀 Uso dos Scripts

### Cenário 1: Nova Organização

```bash
# Situação: Organização criada via web, mas não pode receber pagamentos
# Dashboard mostra: "Payment processing is not yet available"

# Solução:
cd /Users/<USER>/Documents/www/Gateways/polar/server

uv run python -m scripts.activate_account_complete \
  --organization-slug minha-org \
  --admin-email <EMAIL> \
  --no-dry-run

# Resultado:
# ✅ Account criada
# ✅ Organization ativada
# ✅ Admin verificado
# ✅ Dashboard funcional
```

### Cenário 2: Erro 403 no Dashboard

```bash
# Situação: Usuário logado não é admin da conta
# Erro: 403 "You are not the admin of this account"

# Solução:
uv run python -m scripts.change_account_admin \
  minha-org \
  <EMAIL> \
  --no-dry-run

# Resultado:
# ✅ Admin da conta alterado
# ✅ Erro 403 resolvido
```

### Cenário 3: Verificação de Identidade Bloqueando

```bash
# Situação: Dashboard pede KYC (Stripe Identity)
# Erro: 500 ao tentar criar verification session

# Solução:
uv run python -m scripts.verify_user_identity \
  <EMAIL> \
  --no-dry-run

# Resultado:
# ✅ Identidade marcada como verificada
# ✅ KYC bypassed (para desenvolvimento)
```

### Cenário 4: Conta Sem Stripe ID

```bash
# Situação: Dashboard não reconhece conta como pronta
# Falta: stripe_id

# Solução:
uv run python -m scripts.add_stripe_id_to_account \
  --email <EMAIL> \
  --no-dry-run

# Resultado:
# ✅ Stripe ID fake adicionado
# ✅ Dashboard reconhece conta
```

---

## 🔐 Segurança e Produção

### Desenvolvimento (Atual)

```python
# Stripe IDs fake são permitidos
stripe_id = f"acct_fake_{account_id.hex[:16]}"

# KYC pode ser bypassed
identity_verification_status = IdentityVerificationStatus.verified

# Ambiente sandbox pula validações
if settings.ENV == Environment.sandbox:
    return True  # Sempre pronto
```

### Produção (Implementar)

#### 1. Stripe IDs Reais

```python
# Conectar conta real do Stripe
# Via Stripe Connect
account = await stripe.Account.create_async(
    type="express",
    country="BR",
    email=user.email,
    capabilities={
        "card_payments": {"requested": True},
        "transfers": {"requested": True}
    }
)

# Salvar ID real
account.stripe_id = account.id  # "acct_1ABC..."
```

#### 2. KYC para Brasil

**Opção A: Idwall**

```python
import idwall

async def verify_identity_idwall(user: User, cpf: str, document_photo: bytes):
    client = idwall.Client(api_key=settings.IDWALL_API_KEY)
    
    # Validar CPF
    cpf_validation = await client.validate_cpf(cpf)
    
    # Verificação facial
    facial = await client.facial_verification(
        document_photo=document_photo,
        selfie_photo=selfie_photo
    )
    
    if cpf_validation.valid and facial.match > 0.95:
        await user_repository.update(
            user,
            update_dict={
                "identity_verification_status": IdentityVerificationStatus.verified
            }
        )
        return True
    
    return False
```

**Opção B: unico IDCheck**

```python
import unico

async def verify_identity_unico(user: User, cpf: str):
    client = unico.Client(api_key=settings.UNICO_API_KEY)
    
    # Criar sessão de verificação
    session = await client.create_session(
        cpf=cpf,
        callback_url=f"{settings.BASE_URL}/api/v1/kyc/callback"
    )
    
    # Retornar URL para usuário completar verificação
    return session.url
```

**Opção C: Serpro (Governo)**

```python
import serpro

async def verify_cpf_serpro(cpf: str):
    client = serpro.Client(
        consumer_key=settings.SERPRO_KEY,
        consumer_secret=settings.SERPRO_SECRET
    )
    
    # Consultar CPF direto na Receita Federal
    result = await client.consulta_cpf(cpf)
    
    return result.situacao == "REGULAR"
```

#### 3. Implementar Endpoint KYC

```python
# server/polar/kyc/endpoints.py

@router.post("/v1/kyc/verify")
async def start_kyc_verification(
    auth_subject: AuthSubject[User],
    cpf: str,
    document_front: UploadFile,
    document_back: UploadFile,
    selfie: UploadFile,
    session: AsyncSession = Depends(get_db_session),
) -> KYCVerificationResponse:
    """
    Iniciar verificação KYC para usuário brasileiro.
    """
    user = auth_subject.subject
    
    # 1. Validar CPF
    if not validate_cpf(cpf):
        raise ValidationError("CPF inválido")
    
    # 2. Consultar Serpro
    serpro_valid = await verify_cpf_serpro(cpf)
    if not serpro_valid:
        raise ValidationError("CPF não encontrado ou irregular")
    
    # 3. Verificação facial (Idwall ou unico)
    facial_valid = await verify_identity_idwall(
        user,
        cpf,
        document_front.file.read()
    )
    
    # 4. Atualizar status
    if serpro_valid and facial_valid:
        await user_repository.update(
            user,
            update_dict={
                "identity_verification_status": IdentityVerificationStatus.verified
            }
        )
        return KYCVerificationResponse(status="verified")
    
    return KYCVerificationResponse(status="failed")
```

---

## 📊 Matriz de Responsabilidades

| Componente | Responsabilidade | Arquivo |
|------------|------------------|---------|
| **Account** | Gerenciar pagamentos e payouts | `models/account.py` |
| **Organization** | Entidade vendedora | `models/organization.py` |
| **User** | Admin com identidade verificada | `models/user.py` |
| **AccountService** | Lógica de criação de contas | `account/service.py` |
| **OrganizationService** | Validar se pronta para pagamentos | `organization/service.py` |
| **UserService** | Gerenciar KYC | `user/service.py` |
| **PayoutService** | Processar saques | `payout/service.py` |
| **Scripts** | Ativação e manutenção | `scripts/*.py` |

---

## 🎓 Para Desenvolvedores

### Adicionar Nova Validação

```python
# Em organization/service.py

async def is_organization_ready_for_payment(
    session: AsyncReadSession, 
    organization: Organization
) -> bool:
    # ... validações existentes ...
    
    # Adicionar nova validação
    if organization.some_new_field is None:
        return False
    
    return True
```

### Adicionar Novo Script

```python
# scripts/meu_novo_script.py

import asyncio
import typer
from polar.kit.db.postgres import create_async_sessionmaker
from polar.postgres import create_async_engine

cli = typer.Typer()

@cli.command()
async def meu_comando(arg: str):
    engine = create_async_engine("script")
    sessionmaker = create_async_sessionmaker(engine)
    
    async with sessionmaker() as session:
        # Sua lógica aqui
        pass

if __name__ == "__main__":
    asyncio.run(cli())
```

Uso:
```bash
uv run python -m scripts.meu_novo_script ARG
```

---

## 📚 Referências Cruzadas

- **PROMPT_ATIVACAO_CONTAS.md** - Guia rápido para agentes IA
- **GUIA_APROVACAO_CONTAS.md** - Workflow completo para humanos
- **EXEMPLOS_USO_SCRIPTS.md** - 15 cenários práticos
- **RESULTADO_FINAL_APROVACAO.md** - Status da implementação

---

**Versão:** 1.0  
**Data:** 2025-11-10  
**Projeto:** Fluu (Polar Fork)  
**Manutenção:** Atualizar quando adicionar novas validações ou scripts

