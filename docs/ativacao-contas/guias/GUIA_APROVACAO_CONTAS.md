# 📘 Guia Completo: Aprovação de Contas para Saque

## 🎯 Visão Geral

Este guia explica como gerenciar aprovações de contas para receber saques (payouts) no sistema Polar/Fluu.

**Você tem 2 scripts principais:**

1. **`list_accounts_status.py`** - Listar e inspecionar contas
2. **`approve_account_for_payout.py`** - Aprovar contas para saque

## 📋 Workflow Completo

### Passo 1: Listar Contas Pendentes

Primeiro, veja quais contas precisam de aprovação:

```bash
# Listar todas as contas não prontas para saque
uv run python -m scripts.list_accounts_status --not-payout-ready

# Listar apenas contas em revisão
uv run python -m scripts.list_accounts_status --status under_review

# Listar todas as contas
uv run python -m scripts.list_accounts_status
```

**Saída esperada:**
```
📊 Total de contas: 15

==============================================================================
Status                 | Admin Email                         | Capabilities                  | Organizações
==============================================================================
⚠️  Under Review        | <EMAIL>              | Saques✗ | Cobranças✗         | my-saas-app
⏳ Created              | <EMAIL>              | Saques✗ | Cobranças✗         | another-org
✅ Active               | <EMAIL>              | Saques✓ | Cobranças✓         | working-org
==============================================================================

📈 Resumo:
   Active: 5
   Under Review: 8
   Created: 2

✅ Prontas para saque: 5
⚠️  NÃO prontas para saque: 10

💡 Dica: Para aprovar uma conta, use:
   uv run python -m scripts.approve_account_for_payout --email ADMIN_EMAIL --no-dry-run
```

### Passo 2: Inspecionar Conta Específica

Veja detalhes de uma conta antes de aprovar:

```bash
# Por email
uv run python -m scripts.list_accounts_status show --email <EMAIL>

# Por ID
uv run python -m scripts.list_accounts_status show --account-id "550e8400-e29b-41d4-a716-************"
```

**Saída esperada:**
```
================================================================================
📊 DETALHES DA CONTA
================================================================================

ID: 550e8400-e29b-41d4-a716-************
Admin: <EMAIL>
Tipo: stripe
Status: under_review (Under Review)

País: BR
Moeda: BRL

Detalhes Enviados: ✗
Cobranças Habilitadas: ✗
Saques Habilitados: ✗
Próxima Revisão: Não definida

⚠️  Status: EM REVISÃO - Saques bloqueados

🏢 Organizações vinculadas (1):
   • my-saas-app (Status: under_review)

================================================================================

💡 Para aprovar esta conta:
   uv run python -m scripts.approve_account_for_payout --account-id 550e8400-e29b-41d4-a716-************ --no-dry-run
```

### Passo 3: Simular Aprovação (Dry Run)

Sempre rode em dry-run primeiro para ver o que seria alterado:

```bash
uv run python -m scripts.approve_account_for_payout --email <EMAIL>
```

**Saída esperada:**
```
🔍 Conta encontrada (Email: <EMAIL>)

📊 Status Atual:
   ID: 550e8400-e29b-41d4-a716-************
   Admin: <EMAIL>
   Tipo: stripe
   Status: under_review (Under Review)
   País: BR
   Moeda: BRL
   Detalhes Enviados: ✗
   Cobranças Habilitadas: ✗
   Saques Habilitados: ✗
   Próxima Revisão: 0
   ⚠️  Em Revisão - Saques Bloqueados

📝 Mudanças a serem aplicadas:
   • Status: under_review → ACTIVE
   • Saques: Desabilitados → Habilitados
   • Cobranças: Desabilitadas → Habilitadas
   • Detalhes: Não enviados → Enviados
   • Threshold: 0 → 10000

🏃 DRY RUN MODE - Nenhuma mudança foi aplicada
   Use --no-dry-run para aplicar as mudanças
```

### Passo 4: Aprovar Conta

Se tudo estiver correto, aprove a conta:

```bash
uv run python -m scripts.approve_account_for_payout --email <EMAIL> --no-dry-run
```

**Saída esperada:**
```
🔍 Conta encontrada (Email: <EMAIL>)

📊 Status Atual:
   Status: under_review (Under Review)
   ⚠️  Em Revisão - Saques Bloqueados

📝 Mudanças a serem aplicadas:
   • Status: under_review → ACTIVE
   • Saques: Desabilitados → Habilitados
   • Cobranças: Desabilitadas → Habilitadas
   • Detalhes: Não enviados → Enviados
   • Threshold: 0 → 10000

📋 Atualizando 1 organização(ões) vinculada(s):
   ✓ my-saas-app aprovada

✅ Conta aprovada com sucesso!

📊 Status Após Aprovação:
   ID: 550e8400-e29b-41d4-a716-************
   Admin: <EMAIL>
   Status: active (Active)
   Detalhes Enviados: ✓
   Cobranças Habilitadas: ✓
   Saques Habilitados: ✓
   Próxima Revisão: 10000
   ✅ Pronta para Saques
```

### Passo 5: Confirmar Aprovação

Verifique que a conta foi aprovada corretamente:

```bash
uv run python -m scripts.list_accounts_status show --email <EMAIL>
```

## 🛠️ Comandos Úteis

### Listar Contas

```bash
# Todas as contas
uv run python -m scripts.list_accounts_status

# Apenas em revisão
uv run python -m scripts.list_accounts_status --status under_review

# Apenas não aprovadas
uv run python -m scripts.list_accounts_status --not-payout-ready

# Limitar a 10 resultados
uv run python -m scripts.list_accounts_status --limit 10

# Mostrar detalhes de uma conta
uv run python -m scripts.list_accounts_status show --email <EMAIL>
```

### Aprovar Contas

```bash
# Dry run (padrão)
uv run python -m scripts.approve_account_for_payout --email <EMAIL>

# Executar aprovação
uv run python -m scripts.approve_account_for_payout --email <EMAIL> --no-dry-run

# Por ID da conta
uv run python -m scripts.approve_account_for_payout --account-id UUID --no-dry-run

# Por organização
uv run python -m scripts.approve_account_for_payout --organization-slug my-org --no-dry-run

# Com threshold customizado
uv run python -m scripts.approve_account_for_payout --email <EMAIL> --next-review-threshold 50000 --no-dry-run
```

## 📊 Entendendo os Status

### Status de Conta

| Emoji | Status | Descrição | Ação |
|-------|--------|-----------|------|
| ⏳ | `CREATED` | Conta criada | Aprovar |
| ⏳ | `ONBOARDING_STARTED` | Onboarding iniciado | Aprovar |
| ⚠️ | `UNDER_REVIEW` | Em revisão | **Aprovar se OK** |
| ❌ | `DENIED` | Negada | Revisar caso |
| ✅ | `ACTIVE` | Ativa e pronta | Nenhuma |
| ⚡ | `ACTIVE` (parcial) | Ativa mas sem saques | Habilitar saques |

### Capabilities

- **Saques✓** - Pode receber payouts
- **Saques✗** - NÃO pode receber payouts (precisa aprovar)
- **Cobranças✓** - Pode processar pagamentos
- **Cobranças✗** - NÃO pode processar pagamentos

## 🎯 Casos de Uso Comuns

### Caso 1: Aprovar Nova Conta

```bash
# 1. Ver contas pendentes
uv run python -m scripts.list_accounts_status --status created

# 2. Inspecionar conta específica
uv run python -m scripts.list_accounts_status show --email <EMAIL>

# 3. Simular aprovação
uv run python -m scripts.approve_account_for_payout --email <EMAIL>

# 4. Aprovar
uv run python -m scripts.approve_account_for_payout --email <EMAIL> --no-dry-run
```

### Caso 2: Revisar Contas em Análise

```bash
# 1. Listar todas em revisão
uv run python -m scripts.list_accounts_status --status under_review

# 2. Para cada conta, inspecionar e decidir
uv run python -m scripts.list_accounts_status show --email <EMAIL>

# 3. Aprovar se tudo OK
uv run python -m scripts.approve_account_for_payout --email <EMAIL> --no-dry-run
```

### Caso 3: Aprovar em Lote

```bash
# Criar arquivo com lista de emails
cat > accounts_to_approve.txt << EOF
<EMAIL>
<EMAIL>
<EMAIL>
EOF

# Aprovar todas (com dry-run primeiro)
while read email; do
  echo "Processando: $email"
  uv run python -m scripts.approve_account_for_payout --email "$email"
done < accounts_to_approve.txt

# Se tudo OK, aprovar de verdade
while read email; do
  echo "Aprovando: $email"
  uv run python -m scripts.approve_account_for_payout --email "$email" --no-dry-run
done < accounts_to_approve.txt
```

### Caso 4: Habilitar Saques para Conta Ativa

Se uma conta está ativa mas não tem saques habilitados:

```bash
uv run python -m scripts.approve_account_for_payout --email <EMAIL> --no-dry-run
```

O script detecta automaticamente o que precisa ser ajustado.

## 🔐 Requisitos para Aprovação

Uma conta aprovada terá:

✅ Status = `ACTIVE`  
✅ `is_payouts_enabled = True`  
✅ `is_charges_enabled = True`  
✅ `is_details_submitted = True`  
✅ `next_review_threshold` definido  
✅ Organizações vinculadas também `ACTIVE`  

## ⚠️ Avisos e Boas Práticas

### ✅ FAÇA

1. **Sempre rode dry-run primeiro**
2. Inspecione a conta antes de aprovar
3. Verifique se há fraude ou atividade suspeita
4. Use threshold apropriado ao volume esperado
5. Documente aprovações importantes

### ❌ NÃO FAÇA

1. Não aprove contas suspeitas
2. Não pule o dry-run em produção
3. Não aprove sem verificar detalhes
4. Não use thresholds muito altos sem motivo
5. Não aprove contas sem admin verificado

## 🔍 Troubleshooting

### Conta não encontrada

```bash
❌ Conta não encontrada (Email: <EMAIL>)
```

**Soluções:**
- Verificar se o email está correto
- Tentar buscar por ID: `uv run python -m scripts.list_accounts_status --limit 100`
- Verificar no banco de dados

### Conta já aprovada

```bash
✓ Conta já está aprovada para saques!
```

**Info:** Nada a fazer, conta já está OK.

### Organização não atualizada

Se organizações não foram atualizadas:
1. Verificar logs do servidor
2. Rodar script novamente
3. Verificar manualmente no banco de dados

### Erros de permissão

Se houver erro ao conectar ao banco:
- Verificar variáveis de ambiente
- Verificar credenciais do banco
- Verificar se o servidor está rodando

## 📚 Arquivos Relacionados

### Scripts
- `server/scripts/approve_account_for_payout.py` - Aprovação
- `server/scripts/list_accounts_status.py` - Listagem
- `server/scripts/README_APPROVE_ACCOUNT.md` - Guia rápido

### Documentação
- `SCRIPT_APROVAR_CONTA_SAQUE.md` - Doc completa
- `GUIA_APROVACAO_CONTAS.md` - Este guia
- `ARQUITETURA_MULTI_GATEWAY.md` - Arquitetura

### Código Fonte
- `server/polar/models/account.py` - Modelo Account
- `server/polar/organization/service.py` - Lógica de aprovação
- `server/polar/payout/service.py` - Lógica de payouts

## 🧪 Testando

### Ambiente de Desenvolvimento

```bash
# 1. Criar conta de teste
# (via interface ou API)

# 2. Listar contas
uv run python -m scripts.list_accounts_status

# 3. Aprovar conta de teste
uv run python -m scripts.approve_account_for_payout --email <EMAIL> --no-dry-run

# 4. Verificar resultado
uv run python -m scripts.list_accounts_status show --email <EMAIL>
```

### Staging

Antes de usar em produção, teste em staging:

```bash
# Usar em staging primeiro
export DATABASE_URL="postgresql://..."

# Rodar scripts
uv run python -m scripts.list_accounts_status
uv run python -m scripts.approve_account_for_payout --email <EMAIL> --no-dry-run
```

### Produção

Em produção, sempre:

1. ✅ Backup do banco antes
2. ✅ Dry-run primeiro
3. ✅ Verificar logs
4. ✅ Confirmar resultado
5. ✅ Monitorar após aprovação

## 📞 Suporte

Se precisar de ajuda:

1. Consulte esta documentação
2. Verifique logs do servidor
3. Use modo dry-run para debug
4. Verifique banco de dados diretamente
5. Consulte documentação da API

## 📝 Exemplos Completos

### Exemplo 1: Workflow Diário

```bash
#!/bin/bash
# workflow_diario.sh

echo "=== Contas Pendentes de Aprovação ==="
uv run python -m scripts.list_accounts_status --not-payout-ready

echo ""
echo "=== Contas em Revisão ==="
uv run python -m scripts.list_accounts_status --status under_review
```

### Exemplo 2: Script de Aprovação em Massa

```bash
#!/bin/bash
# approve_batch.sh

# Lista de emails para aprovar
EMAILS=(
  "<EMAIL>"
  "<EMAIL>"
  "<EMAIL>"
)

# Dry run primeiro
echo "=== DRY RUN ==="
for email in "${EMAILS[@]}"; do
  echo "Checking: $email"
  uv run python -m scripts.approve_account_for_payout --email "$email"
  echo "---"
done

# Pedir confirmação
read -p "Aprovar todas? (yes/no): " confirm
if [ "$confirm" = "yes" ]; then
  echo "=== APROVANDO ==="
  for email in "${EMAILS[@]}"; do
    echo "Aprovando: $email"
    uv run python -m scripts.approve_account_for_payout --email "$email" --no-dry-run
    echo "✓ Aprovado"
  done
fi
```

---

**Última atualização:** 2025-11-10  
**Versão:** 1.0

