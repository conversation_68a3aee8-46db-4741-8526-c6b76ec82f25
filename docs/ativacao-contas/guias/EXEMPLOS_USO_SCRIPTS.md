# 💡 Exemplos Práticos de Uso dos Scripts

## 🎬 Cenário 1: Primeira Vez Usando os Scripts

Você acabou de instalar o sistema e quer ver o estado das contas.

```bash
# Passo 1: Ver todas as contas
uv run python -m scripts.list_accounts_status

# Saída:
# 📊 Total de contas: 3
# ⚠️  Under Review  | <EMAIL> | Saques✗
# ⏳ Created        | <EMAIL> | Saques✗
# ✅ Active         | <EMAIL>        | Saques✓

# Passo 2: Ver apenas as que precisam aprovação
uv run python -m scripts.list_accounts_status --not-payout-ready

# Saída:
# 📊 Total de contas: 2
# ⚠️  Under Review  | <EMAIL> | Saques✗
# ⏳ Created        | <EMAIL> | Saques✗
```

## 🎬 Cenário 2: Aprovar Uma Conta Nova

Um novo merchant se cadastrou e completou o onboarding.

```bash
# Passo 1: Ver detalhes da conta
uv run python -m scripts.list_accounts_status show --email <EMAIL>

# Saída:
# ID: 550e8400-e29b-41d4-a716-************
# Admin: <EMAIL>
# Status: under_review (Under Review)
# ⚠️  Status: EM REVISÃO - Saques bloqueados

# Passo 2: Simular aprovação (DRY RUN)
uv run python -m scripts.approve_account_for_payout --email <EMAIL>

# Saída:
# 📝 Mudanças a serem aplicadas:
#    • Status: under_review → ACTIVE
#    • Saques: Desabilitados → Habilitados
#    • Cobranças: Desabilitadas → Habilitadas
# 🏃 DRY RUN MODE - Nenhuma mudança foi aplicada

# Passo 3: Se tudo OK, aprovar de verdade
uv run python -m scripts.approve_account_for_payout --email <EMAIL> --no-dry-run

# Saída:
# ✅ Conta aprovada com sucesso!
# ✅ Pronta para Saques

# Passo 4: Confirmar
uv run python -m scripts.list_accounts_status show --email <EMAIL>

# Saída:
# Status: active (Active)
# ✅ Status: PRONTA PARA SAQUES
```

## 🎬 Cenário 3: Conta Ativa Mas Sem Saques

Uma conta antiga está ativa mas não tem saques habilitados.

```bash
# Passo 1: Identificar o problema
uv run python -m scripts.list_accounts_status show --email <EMAIL>

# Saída:
# Status: active (Active)
# Saques Habilitados: ✗
# ⚡ Status: ATIVA mas não pronta para saques

# Passo 2: Corrigir
uv run python -m scripts.approve_account_for_payout --email <EMAIL> --no-dry-run

# Saída:
# 📝 Mudanças a serem aplicadas:
#    • Saques: Desabilitados → Habilitados
# ✅ Conta aprovada com sucesso!
```

## 🎬 Cenário 4: Aprovar Várias Contas

Você revisou manualmente 5 contas e quer aprovar todas.

```bash
# Método 1: Loop simples
for <NAME_EMAIL> <EMAIL> <EMAIL>; do
  uv run python -m scripts.approve_account_for_payout --email "$email" --no-dry-run
done

# Método 2: De arquivo
cat > to_approve.txt << EOF
<EMAIL>
<EMAIL>
<EMAIL>
<EMAIL>
<EMAIL>
EOF

# Primeiro, DRY RUN de todas
echo "=== SIMULAÇÃO ==="
while read email; do
  echo "--- $email ---"
  uv run python -m scripts.approve_account_for_payout --email "$email"
done < to_approve.txt

# Se tudo OK, aprovar todas
echo "=== APROVANDO ==="
while read email; do
  echo "Aprovando: $email"
  uv run python -m scripts.approve_account_for_payout --email "$email" --no-dry-run
  echo "✓"
done < to_approve.txt
```

## 🎬 Cenário 5: Workflow Diário de Análise

Todo dia você verifica contas pendentes.

```bash
#!/bin/bash
# daily_review.sh

echo "=========================================="
echo "RELATÓRIO DIÁRIO DE CONTAS"
echo "Data: $(date)"
echo "=========================================="

echo ""
echo "1️⃣  CONTAS EM REVISÃO"
echo "---"
uv run python -m scripts.list_accounts_status --status under_review

echo ""
echo "2️⃣  CONTAS CRIADAS HOJE"
echo "---"
uv run python -m scripts.list_accounts_status --status created --limit 10

echo ""
echo "3️⃣  TOTAL NÃO APROVADAS"
echo "---"
uv run python -m scripts.list_accounts_status --not-payout-ready

echo ""
echo "=========================================="
echo "ANÁLISE COMPLETA"
echo "=========================================="
```

## 🎬 Cenário 6: Buscar Conta Por Organização

Você sabe o nome da organização mas não o email do admin.

```bash
# Passo 1: Buscar por slug da organização
uv run python -m scripts.approve_account_for_payout --organization-slug "my-saas-company"

# Saída:
# 🔍 Conta encontrada (Organização: my-saas-company)
# Admin: <EMAIL>
# Status: under_review
# ...

# Passo 2: Aprovar se necessário
uv run python -m scripts.approve_account_for_payout --organization-slug "my-saas-company" --no-dry-run
```

## 🎬 Cenário 7: Aprovar Com Threshold Alto

Merchant de grande volume precisa de threshold maior.

```bash
# Threshold de $10.000 (1.000.000 centavos)
uv run python -m scripts.approve_account_for_payout \
  --email <EMAIL> \
  --next-review-threshold 1000000 \
  --no-dry-run

# Saída:
# 📝 Mudanças a serem aplicadas:
#    • Status: under_review → ACTIVE
#    • Threshold: 0 → 1000000
# ✅ Conta aprovada com sucesso!
```

## 🎬 Cenário 8: Verificar Status Após Webhook

O Stripe enviou webhook de conta aprovada, verificar se está sincronizado.

```bash
# Ver se já está sincronizado
uv run python -m scripts.list_accounts_status show --email <EMAIL>

# Se não estiver, forçar sync aprovando
uv run python -m scripts.approve_account_for_payout --email <EMAIL> --no-dry-run
```

## 🎬 Cenário 9: Encontrar Conta Por ID

Você tem apenas o UUID da conta.

```bash
# Por ID
uv run python -m scripts.list_accounts_status show \
  --account-id "550e8400-e29b-41d4-a716-************"

# Aprovar por ID
uv run python -m scripts.approve_account_for_payout \
  --account-id "550e8400-e29b-41d4-a716-************" \
  --no-dry-run
```

## 🎬 Cenário 10: Gerar Relatório

Criar relatório de contas para enviar ao time.

```bash
#!/bin/bash
# generate_report.sh

OUTPUT="account_report_$(date +%Y%m%d).txt"

{
  echo "RELATÓRIO DE CONTAS POLAR/FLUU"
  echo "Data: $(date)"
  echo "========================================"
  echo ""
  
  echo "TODAS AS CONTAS:"
  echo "---"
  uv run python -m scripts.list_accounts_status
  
  echo ""
  echo "CONTAS PENDENTES:"
  echo "---"
  uv run python -m scripts.list_accounts_status --not-payout-ready
  
} > "$OUTPUT"

echo "Relatório salvo em: $OUTPUT"
cat "$OUTPUT"
```

## 🎬 Cenário 11: Aprovação Condicional

Aprovar apenas se o threshold atual for 0.

```bash
#!/bin/bash

EMAIL="<EMAIL>"

# Pegar informações da conta
uv run python -m scripts.list_accounts_status show --email "$EMAIL" > /tmp/account_info.txt

# Verificar se threshold é 0
if grep -q "Próxima Revisão: Não definida" /tmp/account_info.txt; then
  echo "Threshold não definido, aprovando..."
  uv run python -m scripts.approve_account_for_payout --email "$EMAIL" --no-dry-run
else
  echo "Threshold já definido, pulando..."
fi
```

## 🎬 Cenário 12: Debug de Conta Problemática

Conta não está processando pagamentos, investigar.

```bash
# Passo 1: Ver status completo
uv run python -m scripts.list_accounts_status show --email <EMAIL>

# Verificar:
# - Status está ACTIVE?
# - Saques habilitados?
# - Cobranças habilitadas?
# - Organizações vinculadas?

# Passo 2: Se algo estiver errado, corrigir
uv run python -m scripts.approve_account_for_payout --email <EMAIL> --no-dry-run

# Passo 3: Verificar novamente
uv run python -m scripts.list_accounts_status show --email <EMAIL>
```

## 🎬 Cenário 13: Filtrar Por Status Específico

Ver apenas contas em um status específico.

```bash
# Todas criadas recentemente
uv run python -m scripts.list_accounts_status --status created

# Todas em revisão
uv run python -m scripts.list_accounts_status --status under_review

# Todas negadas (para revisar casos)
uv run python -m scripts.list_accounts_status --status denied

# Todas ativas
uv run python -m scripts.list_accounts_status --status active
```

## 🎬 Cenário 14: Monitoramento Contínuo

Script para rodar a cada hora e alertar sobre novas contas.

```bash
#!/bin/bash
# monitor_accounts.sh

LAST_COUNT_FILE="/tmp/polar_account_count.txt"

# Contar contas pendentes
CURRENT_COUNT=$(uv run python -m scripts.list_accounts_status --not-payout-ready 2>/dev/null | grep "Total de contas:" | awk '{print $4}')

# Ler último count
if [ -f "$LAST_COUNT_FILE" ]; then
  LAST_COUNT=$(cat "$LAST_COUNT_FILE")
else
  LAST_COUNT=0
fi

# Comparar
if [ "$CURRENT_COUNT" -gt "$LAST_COUNT" ]; then
  echo "🚨 ALERTA: $CURRENT_COUNT contas pendentes (antes: $LAST_COUNT)"
  uv run python -m scripts.list_accounts_status --not-payout-ready
else
  echo "✅ OK: $CURRENT_COUNT contas pendentes"
fi

# Salvar count atual
echo "$CURRENT_COUNT" > "$LAST_COUNT_FILE"
```

## 🎬 Cenário 15: Aprovação Interativa

Script que pergunta antes de aprovar cada conta.

```bash
#!/bin/bash
# interactive_approval.sh

# Listar contas pendentes
uv run python -m scripts.list_accounts_status --status under_review > /tmp/pending.txt

# Para cada conta em revisão
uv run python -m scripts.list_accounts_status --status under_review 2>/dev/null | \
  grep "@" | \
  awk '{print $3}' | \
  while read email; do
    echo ""
    echo "========================================"
    uv run python -m scripts.list_accounts_status show --email "$email"
    echo "========================================"
    
    read -p "Aprovar esta conta? (y/n): " answer
    if [ "$answer" = "y" ]; then
      uv run python -m scripts.approve_account_for_payout --email "$email" --no-dry-run
      echo "✅ Aprovado: $email"
    else
      echo "⏭️  Pulado: $email"
    fi
  done
```

## 📝 Notas Importantes

1. **Sempre teste em desenvolvimento primeiro**
2. **Use dry-run por padrão**
3. **Verifique logs após operações em massa**
4. **Faça backup do banco antes de scripts grandes**
5. **Documente aprovações importantes**

---

**Dica:** Salve seus scripts personalizados em `server/scripts/custom/` para não perder quando atualizar o sistema.

