# ✅ Checklist: Primeiro Uso dos Scripts

## 🎯 Use Este Checklist

Marque cada item conforme for completando.

---

## 📋 ANTES DE COMEÇAR

- [ ] Estou no diretório do projeto: `/Users/<USER>/Documents/www/Gateways/polar`
- [ ] O servidor está configurado (variáveis de ambiente OK)
- [ ] Tenho acesso ao banco de dados
- [ ] Li o `README_SCRIPTS_APROVACAO.md`

---

## 🧪 TESTE 1: VERIFICAR SE OS SCRIPTS FUNCIONAM

### Passo 1: Listar Contas
```bash
cd /Users/<USER>/Documents/www/Gateways/polar/server
uv run python -m scripts.list_accounts_status
```

- [ ] Comando executou sem erros
- [ ] Vi a lista de contas (ou "Nenhuma conta encontrada")

### Passo 2: Ver Ajuda
```bash
uv run python -m scripts.list_accounts_status --help
uv run python -m scripts.approve_account_for_payout --help
```

- [ ] Mensagens de ajuda foram exibidas corretamente

---

## 🎬 TESTE 2: PRIMEIRO USO REAL

### Passo 1: Identificar Contas Pendentes
```bash
uv run python -m scripts.list_accounts_status --not-payout-ready
```

- [ ] Executou sem erros
- [ ] Vi a lista de contas pendentes (anote quantas são: _______)

### Passo 2: Escolher Uma Conta Para Testar

Escolha uma conta da lista acima:
- Email escolhido: ________________________________

### Passo 3: Ver Detalhes da Conta
```bash
uv run python -m scripts.list_accounts_status show --email SEU_EMAIL_AQUI
```

- [ ] Vi os detalhes da conta
- [ ] Confirmo que esta conta deve ser aprovada

### Passo 4: Simular Aprovação (DRY RUN)
```bash
uv run python -m scripts.approve_account_for_payout --email SEU_EMAIL_AQUI
```

- [ ] Executou em modo dry-run
- [ ] Vi a mensagem "DRY RUN MODE - Nenhuma mudança foi aplicada"
- [ ] As mudanças propostas fazem sentido

### Passo 5: Aprovar de Verdade
```bash
uv run python -m scripts.approve_account_for_payout --email SEU_EMAIL_AQUI --no-dry-run
```

- [ ] Executou a aprovação
- [ ] Vi a mensagem "✅ Conta aprovada com sucesso!"

### Passo 6: Confirmar Aprovação
```bash
uv run python -m scripts.list_accounts_status show --email SEU_EMAIL_AQUI
```

- [ ] Status mudou para "active (Active)"
- [ ] Vi "✅ Pronta para Saques"
- [ ] Saques e Cobranças estão habilitados (✓)

---

## ✅ TESTE 3: WORKFLOW COMPLETO

Agora repita o processo com outra conta para fixar:

- [ ] **Passo 1:** Listei contas pendentes
- [ ] **Passo 2:** Escolhi uma conta
- [ ] **Passo 3:** Vi detalhes da conta
- [ ] **Passo 4:** Simulei aprovação (dry-run)
- [ ] **Passo 5:** Aprovei a conta (--no-dry-run)
- [ ] **Passo 6:** Confirmei que funcionou

---

## 📚 DOCUMENTAÇÃO LIDA

Marque conforme for lendo:

- [ ] `README_SCRIPTS_APROVACAO.md` - Visão geral (5 min)
- [ ] `SUMARIO_SCRIPTS_CRIADOS.md` - O que foi criado (3 min)
- [ ] `GUIA_APROVACAO_CONTAS.md` - Guia completo (15 min)
- [ ] `EXEMPLOS_USO_SCRIPTS.md` - Exemplos práticos (10 min)

Opcional:
- [ ] `SCRIPT_APROVAR_CONTA_SAQUE.md` - Referência técnica

---

## 🚀 PRÓXIMOS PASSOS

Depois de completar este checklist:

### Para Uso Diário
- [ ] Criar script `daily_review.sh` (ver `EXEMPLOS_USO_SCRIPTS.md` - Cenário 5)
- [ ] Configurar em cronjob/schedule se necessário
- [ ] Documentar processo interno da equipe

### Para Produção
- [ ] Testar em ambiente de staging primeiro
- [ ] Fazer backup do banco de dados
- [ ] Configurar alertas/monitoramento
- [ ] Treinar equipe no uso dos scripts

### Personalização
- [ ] Criar scripts customizados em `server/scripts/custom/`
- [ ] Adaptar thresholds padrão se necessário
- [ ] Ajustar workflow para sua equipe

---

## 📊 COMANDOS MAIS USADOS

Salve estes comandos (copie para um arquivo local):

```bash
# Ver contas pendentes
uv run python -m scripts.list_accounts_status --not-payout-ready

# Ver detalhes de conta
uv run python -m scripts.list_accounts_status show --email EMAIL

# Simular aprovação
uv run python -m scripts.approve_account_for_payout --email EMAIL

# Aprovar conta
uv run python -m scripts.approve_account_for_payout --email EMAIL --no-dry-run
```

---

## ⚠️ LEMBRE-SE SEMPRE

### Antes de Aprovar
- [ ] Verifiquei se a conta deve mesmo ser aprovada
- [ ] Rodei em dry-run primeiro
- [ ] Li as mudanças que serão aplicadas

### Ao Aprovar
- [ ] Estou usando `--no-dry-run` conscientemente
- [ ] Sei o que estou fazendo
- [ ] Posso reverter se necessário

### Após Aprovar
- [ ] Confirmei que funcionou
- [ ] Documentei se necessário
- [ ] Monitorei a conta por alguns dias

---

## 🎉 CONCLUÍDO!

Quando completar todos os checkboxes acima, você estará pronto para usar os scripts em produção.

### Seus Próximos Passos

1. **Use diariamente:** Verifique contas pendentes
2. **Consulte documentação:** Quando tiver dúvidas
3. **Compartilhe:** Ensine a equipe

---

## 📝 NOTAS PESSOAIS

Use este espaço para suas anotações:

```
_________________________________________________________________

_________________________________________________________________

_________________________________________________________________

_________________________________________________________________

_________________________________________________________________
```

---

## 🆘 PROBLEMAS?

Se algo não funcionar:

1. ✅ Verifique que está no diretório correto
2. ✅ Verifique variáveis de ambiente
3. ✅ Consulte seção "Troubleshooting" em `GUIA_APROVACAO_CONTAS.md`
4. ✅ Verifique logs do servidor
5. ✅ Use `--help` nos comandos

---

**Data de Conclusão:** ____ / ____ / ________

**Assinatura:** ________________________

✅ **PRONTO PARA PRODUÇÃO!**

