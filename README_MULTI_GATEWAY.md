# 🚀 Fluu - Plataforma Multi-Gateway para o Brasil

## 🎯 O Que É?

**Fluu** é um fork estratégico do [Polar.sh](https://polar.sh) adaptado para o mercado brasileiro, com suporte nativo a **múltiplos gateways de pagamento** e **PIX**.

```
┌──────────────────────────────────────────────────┐
│  🇧🇷 FEITO PARA O BRASIL, DO BRASIL              │
├──────────────────────────────────────────────────┤
│  ✅ PIX nativo (QR Code + confirmação instant)  │
│  ✅ Multi-gateway (Stripe + Pagar.me + mais)    │
│  ✅ Taxas até 50% menores                        │
│  ✅ Gateways brasileiros (parcelamento, boleto) │
│  ✅ Open-source (MIT License)                    │
│  ✅ Self-hosted ou Cloud                         │
└──────────────────────────────────────────────────┘
```

---

## 💡 Por Que Fluu?

### Problema

- **Stripe no Brasil**: Taxas altas (4.99% + R$ 0,39), sem PIX, sem parcelamento
- **Vendor Lock-in**: Difícil trocar de gateway
- **Conversão Baixa**: Sem métodos de pagamento locais

### Solução

- **Multi-Gateway**: Escolha o melhor gateway para cada transação
- **PIX**: Conversão 30-40% maior, taxa 0.99%
- **Economia**: R$ 2.000+ por mês em taxas (para R$ 100k GMV)

---

## 📊 Status do Projeto

### ✅ Fase 1: Stripe Opcional (CONCLUÍDA - 2025-11-10)
- [x] Tornar Stripe opcional na criação de produtos
- [x] Tratamento robusto de erros
- [x] Logs estruturados
- [x] Documentação completa

### 🔄 Fase 2: Pagar.me + PIX (80% CONCLUÍDA - 2025-11-10)
- [x] Provider Pagar.me implementado
- [x] Suporte a PIX (QR Code generation)
- [x] Suporte a Boleto
- [x] Script de teste sandbox
- [x] Documentação arquitetura
- [x] Documentação estratégia de negócio
- [ ] Frontend PIX (QR Code display)
- [ ] Testes E2E
- [ ] Deploy produção

### 🎯 Fase 3: Mercado Pago (PLANEJADA - Q2 2025)
- [ ] Provider Mercado Pago
- [ ] PIX Mercado Pago
- [ ] Parcelamento sem juros
- [ ] Split de pagamento

---

## 📁 Estrutura do Projeto

```
polar/
├── ARQUITETURA_MULTI_GATEWAY.md        # 🏗️ Arquitetura técnica
├── ESTRATEGIA_NEGOCIO_MULTI_GATEWAY.md # 💼 Estratégia de negócio
├── GUIA_IMPLEMENTACAO_GATEWAYS.md      # 🔧 Como adicionar gateway
├── STRIPE_OPTIONAL_IMPLEMENTATION.md   # ✅ Fase 1 detalhes
├── CHANGELOG_STRIPE_OPTIONAL.md        # 📝 Changelog Fase 1
├── test_pagarme_pix.py                 # 🧪 Teste Pagar.me + PIX
│
├── server/
│   ├── polar/
│   │   ├── product/
│   │   │   └── service.py              # ✅ Stripe opcional
│   │   ├── checkout/
│   │   │   └── service.py              # 🔄 Multi-gateway checkout
│   │   ├── integrations/
│   │   │   └── payment_providers/
│   │   │       ├── registry.py         # 🎯 Registry central
│   │   │       ├── base.py             # Interface PaymentProvider
│   │   │       ├── stripe/             # Gateway Stripe
│   │   │       └── pagarme/            # 🆕 Gateway Pagar.me
│   │   │           ├── provider.py     # ✅ PIX + Boleto
│   │   │           ├── webhooks.py     # Webhook handler
│   │   │           └── schemas.py      # Pydantic schemas
│   │   └── enums.py                    # PaymentProcessor enum
│   └── .env.example
│
└── clients/                             # Frontend (Next.js/React)
    └── apps/
        └── web/
            └── src/
                └── components/
                    └── Checkout/        # 🔄 Multi-gateway checkout UI
```

---

## 🚀 Quick Start

### 1. Clonar Repositório

```bash
git clone https://github.com/seu-usuario/polar.git fluu
cd fluu
```

### 2. Configurar Backend

```bash
cd server

# Copiar .env.example
cp .env.example .env

# Editar .env
nano .env
```

**Variáveis importantes**:

```bash
# Stripe (opcional)
POLAR_STRIPE_SECRET_KEY=sk_test_xxx
POLAR_STRIPE_PUBLISHABLE_KEY=pk_test_xxx

# Pagar.me (recomendado para Brasil)
POLAR_PAGARME_SECRET_KEY=sk_test_xxx
POLAR_PAGARME_PUBLISHABLE_KEY=pk_test_xxx
ENABLE_PAGARME=true

# Database
DATABASE_URL=postgresql://user:pass@localhost:5432/polar

# Redis
REDIS_URL=redis://localhost:6379/0
```

### 3. Instalar Dependências

```bash
# Instalar uv (package manager)
curl -LsSf https://astral.sh/uv/install.sh | sh

# Instalar dependências
uv sync

# Rodar migrações
uv run alembic upgrade head
```

### 4. Rodar Servidor

```bash
uv run task api
```

Servidor rodando em: http://localhost:8000

### 5. Testar Pagar.me + PIX

```bash
# Voltar para raiz do projeto
cd ..

# Rodar teste
python3 test_pagarme_pix.py
```

Você deve ver:
```
✅ Conexão com Pagar.me estabelecida
✅ Customer criado
✅ Order criada
🎉 PIX GERADO COM SUCESSO!
   QR Code PIX: 00020126...
```

---

## 🧪 Testes

### Testar Stripe Opcional

```bash
# Comentar POLAR_STRIPE_SECRET_KEY no .env
# Reiniciar servidor

# Criar produto (deve funcionar!)
curl -X POST http://localhost:8000/v1/products/ \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Product",
    "organization_id": "org_xxx",
    "prices": [{
      "type": "one_time",
      "price_amount": 10000,
      "price_currency": "brl"
    }]
  }'

# Deve retornar 201 (não 500!)
# stripe_product_id será null
```

### Testar Pagar.me + PIX

```bash
python3 test_pagarme_pix.py
```

---

## 📚 Documentação

### Para Desenvolvedores

- **[ARQUITETURA_MULTI_GATEWAY.md](./ARQUITETURA_MULTI_GATEWAY.md)**: Arquitetura técnica completa
- **[GUIA_IMPLEMENTACAO_GATEWAYS.md](./GUIA_IMPLEMENTACAO_GATEWAYS.md)**: Como adicionar novo gateway
- **[STRIPE_OPTIONAL_IMPLEMENTATION.md](./STRIPE_OPTIONAL_IMPLEMENTATION.md)**: Detalhes Fase 1

### Para Product/Negócio

- **[ESTRATEGIA_NEGOCIO_MULTI_GATEWAY.md](./ESTRATEGIA_NEGOCIO_MULTI_GATEWAY.md)**: Estratégia, métricas, ROI

### API Documentation

```bash
# Com servidor rodando
open http://localhost:8000/docs
```

---

## 💰 Economia com Multi-Gateway

### Cenário: R$ 100k GMV/mês

| Métrica | Stripe Only | Fluu (Multi-Gateway) | Economia |
|---------|-------------|----------------------|----------|
| Taxa média | 4.99% | 2.49% | **-50%** |
| Custo/mês | R$ 5.185 | R$ 2.540 | **R$ 2.645** |
| Custo/ano | R$ 62.220 | R$ 30.480 | **R$ 31.740** |

**ROI**: 1-2 meses de implementação vs R$ 31k+ economizados/ano

---

## 🎯 Roadmap

### Q1 2025
- [x] ✅ Fork do Polar.sh
- [x] ✅ Stripe opcional
- [x] ✅ Provider Pagar.me
- [x] ✅ PIX backend
- [ ] 🔄 PIX frontend
- [ ] 🔄 Beta com 10 clientes

### Q2 2025
- [ ] 50+ clientes pagantes
- [ ] Provider Mercado Pago
- [ ] Assinaturas com PIX
- [ ] Dashboard de analytics

### Q3 2025
- [ ] 200+ clientes
- [ ] Provider PicPay
- [ ] Split de pagamento
- [ ] White-label

### Q4 2025
- [ ] 500+ clientes
- [ ] Anti-fraude BR
- [ ] Conciliação bancária
- [ ] Enterprise features

---

## 🤝 Contribuindo

### Como Contribuir

1. Fork o repositório
2. Crie uma branch (`git checkout -b feature/meu-gateway`)
3. Commit suas mudanças (`git commit -am 'feat: add meu-gateway'`)
4. Push para a branch (`git push origin feature/meu-gateway`)
5. Abra um Pull Request

### Guidelines

- Siga o padrão de código existente
- Adicione testes para novas funcionalidades
- Atualize documentação
- Use commits semânticos (feat, fix, docs, etc)

### Adicionando Novo Gateway

Veja [GUIA_IMPLEMENTACAO_GATEWAYS.md](./GUIA_IMPLEMENTACAO_GATEWAYS.md) para passo a passo completo.

---

## 📊 Métricas Atuais

### Fase 1 (Stripe Opcional)
- ✅ **100%** implementado
- ✅ **0** erros de linting
- ✅ **0** breaking changes
- ✅ **100%** compatibilidade com Polar.sh

### Fase 2 (Pagar.me + PIX)
- 🔄 **80%** implementado
- ✅ **PIX QR Code** funcional
- ✅ **Boleto** suportado
- 🔄 **Frontend** em progresso

---

## 🆘 Suporte

### Issues & Bugs

Abra uma issue em: https://github.com/seu-usuario/polar/issues

### Comunidade

- Discord: [em breve]
- Twitter: [@FluuBR](https://twitter.com/FluuBR)
- Email: <EMAIL>

### Consultoría

Para implementação enterprise, entre em contato:
- Email: <EMAIL>
- WhatsApp: [em breve]

---

## 📜 Licença

MIT License - veja [LICENSE](./LICENSE) para detalhes.

Baseado em [Polar.sh](https://polar.sh) por [@frankie567](https://github.com/frankie567)

---

## 🌟 Agradecimentos

- **Polar.sh**: Base sólida e arquitetura excelente
- **Pagar.me**: Suporte à implementação
- **Comunidade Python/FastAPI**: Ferramentas incríveis
- **Banco Central do Brasil**: Por criar o PIX 🇧🇷

---

## 📞 Contato

**Ismael**  
Founder, Fluu  
📧 <EMAIL>  
🐦 [@IsmaelFluu](https://twitter.com/IsmaelFluu)

---

<div align="center">
  
**Feito com ❤️ no Brasil 🇧🇷**

Economize em taxas. Aumente conversão. Use PIX.

[Website](https://fluu.digital) • [Docs](./ARQUITETURA_MULTI_GATEWAY.md) • [GitHub](https://github.com/seu-usuario/polar)

</div>

