# ⚡ IMPLEMENTAÇÃO IMEDIATA - Remover Dependência do Stripe

## 🎯 O QUE FAZER AGORA

### Problema
❌ Criar produto retorna erro 500 porque Stripe não está configurado

### Solução
✅ Tornar Stripe opcional - produto é criado no banco independente do gateway

---

## 📝 MUDANÇA PRINCIPAL

### Arquivo: `server/polar/product/service.py`

#### Adicionar no início da classe ProductService:

```python
def _should_create_in_stripe(self) -> bool:
    """Verifica se deve criar produto no Stripe."""
    from polar.config import settings
    return bool(
        settings.STRIPE_SECRET_KEY 
        and settings.STRIPE_SECRET_KEY != "" 
        and not settings.STRIPE_SECRET_KEY.startswith("YOUR_")
        and not settings.STRIPE_SECRET_KEY.startswith("sk_test_fake")
    )
```

#### Modificar método create() - linhas ~240-270:

**ANTES:**
```python
metadata: dict[str, str] = {"product_id": str(product.id)}
metadata["organization_id"] = str(organization.id)
metadata["organization_name"] = organization.slug

stripe_product = await stripe_service.create_product(
    product.get_stripe_name(),
    description=product.description,
    metadata=metadata,
)
product.stripe_product_id = stripe_product.id

for price in product.all_prices:
    if isinstance(price, HasStripePriceId):
        stripe_price = await stripe_service.create_price_for_product(
            stripe_product.id,
            price.get_stripe_price_params(product.recurring_interval),
        )
        price.stripe_price_id = stripe_price.id
    session.add(price)

await session.flush()
```

**DEPOIS:**
```python
metadata: dict[str, str] = {"product_id": str(product.id)}
metadata["organization_id"] = str(organization.id)
metadata["organization_name"] = organization.slug

# Criar no Stripe apenas se configurado
if self._should_create_in_stripe():
    try:
        stripe_product = await stripe_service.create_product(
            product.get_stripe_name(),
            description=product.description,
            metadata=metadata,
        )
        product.stripe_product_id = stripe_product.id
        
        for price in product.all_prices:
            if isinstance(price, HasStripePriceId):
                stripe_price = await stripe_service.create_price_for_product(
                    stripe_product.id,
                    price.get_stripe_price_params(product.recurring_interval),
                )
                price.stripe_price_id = stripe_price.id
            session.add(price)
        
        log.info(
            "product.created_in_stripe",
            product_id=str(product.id),
            stripe_product_id=stripe_product.id,
        )
    except Exception as e:
        log.warning(
            "product.stripe_creation_failed",
            product_id=str(product.id),
            error=str(e),
            error_type=type(e).__name__,
        )
        # Produto continua sendo criado mesmo se Stripe falhar
else:
    log.info(
        "product.stripe_skipped",
        product_id=str(product.id),
        reason="Stripe not configured",
    )

await session.flush()
```

---

## 📋 CHECKLIST DE IMPLEMENTAÇÃO

### 1. Modificar ProductService
- [ ] Adicionar método `_should_create_in_stripe()`
- [ ] Modificar método `create()` - envolver Stripe em if/try
- [ ] Modificar método `update()` - mesma lógica
- [ ] Buscar outros métodos que usam `stripe_service`

### 2. Testar Localmente
```bash
cd /Users/<USER>/Documents/www/Gateways/polar/server

# Editar .env - comentar ou remover POLAR_STRIPE_SECRET_KEY
# POLAR_STRIPE_SECRET_KEY=

# Iniciar API
uv run task api

# Em outro terminal, testar criar produto
curl -X POST "http://127.0.0.1:8000/v1/products/" \
  -H "Content-Type: application/json" \
  -H "Cookie: fluu_session=COOKIE_DA_SESSAO" \
  -d '{
    "name": "Test Product",
    "description": "Test",
    "organization_id": "UUID_DA_ORG",
    "prices": [{"amount_type": "fixed", "price_currency": "usd", "price_amount": 1000}]
  }'

# Deve retornar 201, não 500
```

### 3. Commit
```bash
git add server/polar/product/service.py
git commit -m "fix: make Stripe optional for product creation"
```

### 4. Deploy
```bash
cd /Users/<USER>/Documents/www/Gateways/polar
./deploy/cloud-run/deploy-backend.sh
```

---

## 🧪 VALIDAÇÃO

Após deploy:

1. **Teste sem Stripe**:
   - Criar produto em produção
   - ✅ HTTP 201
   - ✅ Produto aparece na listagem
   - ✅ `stripe_product_id` é NULL

2. **Teste com Stripe** (futuro):
   - Configurar POLAR_STRIPE_SECRET_KEY
   - Criar produto
   - ✅ HTTP 201
   - ✅ Produto no banco E no Stripe
   - ✅ `stripe_product_id` preenchido

---

## 🚀 DEPOIS DESSA CORREÇÃO

### Próximo Passo: Integrar Pagar.me

1. Adicionar campos ao modelo:
```sql
ALTER TABLE products 
ADD COLUMN pagarme_product_id VARCHAR(255),
ADD COLUMN pagarme_plan_id VARCHAR(255),
ADD COLUMN preferred_payment_processor VARCHAR(50);
```

2. Criar serviço Pagar.me para produtos

3. Integrar no ProductService (similar ao Stripe)

---

## 📞 DOCUMENTAÇÃO COMPLETA

Ver: `PROMPT_REMOVER_DEPENDENCIA_STRIPE.md`

---

**⏱️ Tempo estimado**: 30-60 minutos  
**🎯 Prioridade**: CRÍTICA  
**✅ Depois**: Produtos podem ser criados sem erro 500


