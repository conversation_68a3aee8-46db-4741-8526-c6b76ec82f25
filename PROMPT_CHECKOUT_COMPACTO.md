# 🚀 PROMPT: Checkout Multi-Gateway Brasil (Compacto)

## 🎯 MISSÃO

Desacoplar Stripe do checkout e usar **Pagar.me como default** (PIX + Boleto + Cartão) adaptado para Brasil.

## 📖 CONTEXTO COMPLETO

**Antes de começa<PERSON>, leia**:
- `ANALISE_CHECKOUT_BRASIL.md` - Análise completa dos problemas
- `PROMPT_IMPLEMENTAR_CHECKOUT_BRASIL.md` - Código completo e detalhado

## 🚨 PROBLEMA ATUAL

```python
# server/polar/checkout/service.py - Linha 438, 615
checkout = Checkout(
    payment_processor=PaymentProcessor.stripe,  # ❌ HARDCODED
    ...
)
```

```typescript
// clients/packages/checkout/src/components/CheckoutForm.tsx - Linha 1060
if (paymentProcessor === 'stripe') {
    return <StripeCheckoutForm {...props} />
}
return <DummyCheckoutForm {...props} />  // ❌ Outros gateways são dummy
```

**Resultado**: Checkout sempre usa Stripe, chaves inválidas causam erro 401/500, sem PIX.

---

## ✅ SOLUÇÃO - FASE 1: Backend (PRIORIDADE)

### 1.1 Criar método de seleção dinâmica

**Arquivo**: `server/polar/checkout/service.py` (após linha 203)

```python
def _get_default_payment_processor(
    self,
    product: Product | None = None,
    customer: Customer | None = None,
    checkout_link: CheckoutLink | None = None,
) -> PaymentProcessor:
    """Determina gateway: CheckoutLink > Product > Customer BR > Pagar.me > Stripe"""
    log = structlog.get_logger()
    
    if checkout_link and checkout_link.payment_processor:
        return checkout_link.payment_processor
    
    if product and hasattr(product, 'preferred_payment_processor'):
        if product.preferred_payment_processor:
            return product.preferred_payment_processor
    
    if customer and customer.billing_address:
        if customer.billing_address.country == "BR" and settings.ENABLE_PAGARME:
            return PaymentProcessor.pagarme
    
    if settings.ENABLE_PAGARME:
        log.info("Using Pagar.me as default")
        return PaymentProcessor.pagarme
    
    return PaymentProcessor.stripe


async def _prepare_payment_processor_metadata(
    self, checkout: Checkout, session: AsyncSession
) -> dict[str, Any]:
    """Prepara metadata do gateway (publishable_key, etc)"""
    
    if checkout.payment_processor == PaymentProcessor.stripe:
        metadata = {"publishable_key": settings.STRIPE_PUBLISHABLE_KEY}
        if checkout.customer and checkout.customer.stripe_customer_id:
            try:
                session = await stripe_service.create_customer_session(
                    checkout.customer.stripe_customer_id
                )
                metadata["customer_session_client_secret"] = session.client_secret
            except Exception as e:
                log.warning("Stripe customer session failed", error=str(e))
        return metadata
    
    elif checkout.payment_processor == PaymentProcessor.pagarme:
        return {
            "publishable_key": settings.PAGARME_PUBLISHABLE_KEY,
            "pix_enabled": True,
            "boleto_enabled": True,
            "credit_card_enabled": True,
        }
    
    return {}
```

### 1.2 Modificar criação de checkouts

**Linhas 438, 615, 771** - Substituir:

```python
# ANTES
payment_processor=PaymentProcessor.stripe,

# DEPOIS
payment_processor=self._get_default_payment_processor(product, customer, checkout_link),
```

**Linhas 485, 630, 836** - Substituir bloco condicional:

```python
# ANTES
if checkout.payment_processor == PaymentProcessor.stripe:
    checkout.payment_processor_metadata = {...}

# DEPOIS
checkout.payment_processor_metadata = await self._prepare_payment_processor_metadata(
    checkout, session
)
```

### 1.3 Configuração

**Arquivo**: `deploy/cloud-run/env.yaml`

```yaml
POLAR_ENABLE_PAGARME: "true"
POLAR_PAGARME_SECRET_KEY: sk_test_SUA_CHAVE_AQUI
POLAR_PAGARME_PUBLISHABLE_KEY: pk_test_SUA_CHAVE_AQUI
```

---

## ✅ SOLUÇÃO - FASE 2: Frontend

### 2.1 Criar componente Pagar.me

**Arquivo NOVO**: `clients/packages/checkout/src/components/PagarmeCheckoutForm.tsx`

```typescript
'use client'
import { useState } from 'react'

const PagarmeCheckoutForm = (props) => {
  const [paymentMethod, setPaymentMethod] = useState<'pix' | 'credit_card' | 'boleto'>('pix')
  
  return (
    <div className="space-y-6">
      {/* Seletor: PIX | Cartão | Boleto */}
      <div className="grid grid-cols-1 gap-2">
        <button onClick={() => setPaymentMethod('pix')} 
                className={paymentMethod === 'pix' ? 'border-green-500' : ''}>
          ⚡ PIX - Aprovação instantânea
        </button>
        <button onClick={() => setPaymentMethod('credit_card')}>
          💳 Cartão de Crédito
        </button>
        <button onClick={() => setPaymentMethod('boleto')}>
          🧾 Boleto - Vence em 3 dias
        </button>
      </div>
      
      {/* TODO: Adicionar BaseCheckoutForm com campos */}
      {/* TODO: Adicionar modal QR Code PIX */}
    </div>
  )
}

export default PagarmeCheckoutForm
```

### 2.2 Integrar no switch

**Arquivo**: `clients/packages/checkout/src/components/CheckoutForm.tsx` (linha 1055)

```typescript
// Adicionar import
import PagarmeCheckoutForm from './PagarmeCheckoutForm'

// Modificar switch (linha 1060)
const CheckoutForm = (props) => {
  const { checkout: { paymentProcessor } } = props

  if (paymentProcessor === 'stripe') return <StripeCheckoutForm {...props} />
  if (paymentProcessor === 'pagarme') return <PagarmeCheckoutForm {...props} />  // 🆕
  
  return <DummyCheckoutForm {...props} />
}
```

---

## ✅ SOLUÇÃO - FASE 3: Confirmação Backend

**Arquivo**: `server/polar/checkout/service.py` (após linha 1500)

```python
async def confirm_pagarme(
    self, session: AsyncSession, locker: Locker,
    checkout: Checkout, checkout_confirm: CheckoutConfirm
) -> Checkout:
    """Confirma checkout com Pagar.me (PIX/Boleto/Cartão)"""
    
    if checkout.status != CheckoutStatus.open:
        raise NotOpenCheckout(checkout)
    
    provider = PaymentProviderRegistry.get(PaymentProcessor.pagarme)
    customer = await self._ensure_customer(session, checkout)
    
    if not customer.tax_id:
        raise ValidationError("CPF/CNPJ obrigatório para Brasil")
    
    payment_method = checkout_confirm.get("payment_method", "pix")
    
    if payment_method == "pix":
        payment = await provider.create_pix_payment(
            amount=checkout.total_amount,
            currency="brl",
            customer={"email": customer.email, "tax_id": customer.tax_id[0]},
            metadata={"checkout_id": str(checkout.id)}
        )
        checkout.payment_processor_metadata = {
            **checkout.payment_processor_metadata,
            "pix_qr_code": payment.get("pix_qr_code"),
            "payment_id": payment.get("id"),
        }
    
    checkout.status = CheckoutStatus.confirmed
    session.add(checkout)
    await session.commit()
    return checkout
```

**Arquivo**: `server/polar/checkout/endpoints.py` (método `client_confirm`)

```python
# Adicionar roteamento após obter checkout
if checkout.payment_processor == PaymentProcessor.stripe:
    return await checkout_service.confirm(...)
elif checkout.payment_processor == PaymentProcessor.pagarme:
    return await checkout_service.confirm_pagarme(...)  # 🆕
```

---

## 📋 CHECKLIST DE IMPLEMENTAÇÃO

### Backend
- [ ] Criar `_get_default_payment_processor()` 
- [ ] Criar `_prepare_payment_processor_metadata()`
- [ ] Modificar linhas 438, 615, 771
- [ ] Substituir blocos condicionais 485, 630, 836
- [ ] Criar `confirm_pagarme()`
- [ ] Modificar `client_confirm()` endpoint

### Frontend
- [ ] Criar `PagarmeCheckoutForm.tsx`
- [ ] Integrar no `CheckoutForm.tsx`
- [ ] Adicionar import

### Config
- [ ] Adicionar chaves Pagar.me em `env.yaml`
- [ ] Testar: `curl POST /v1/checkouts/client` → `"payment_processor": "pagarme"`

---

## 🧪 TESTE RÁPIDO

```bash
# 1. Criar checkout
curl -X POST http://localhost:8000/v1/checkouts/client \
  -d '{"product_price_id": "xxx"}'

# Deve retornar:
# "payment_processor": "pagarme"  ✅
# "payment_processor_metadata": {"pix_enabled": true}  ✅

# 2. Abrir frontend
open http://localhost:3000/checkout/xxxxx
# Deve mostrar: ⚡ PIX | 💳 Cartão | 🧾 Boleto
```

---

## 📚 REFERÊNCIAS DETALHADAS

**Código completo e explicações**:
- `PROMPT_IMPLEMENTAR_CHECKOUT_BRASIL.md` - Versão detalhada com TODO o código
- `ANALISE_CHECKOUT_BRASIL.md` - Análise completa dos problemas

**Provider Pagar.me já existe**:
- `server/polar/integrations/payment_providers/pagarme/provider.py`

**Registry já funciona**:
- `server/polar/integrations/payment_providers/registry.py`

---

## 🎯 ORDEM DE EXECUÇÃO

1. **Implementar Backend FASE 1** (2-3h) → Testar API retorna `pagarme`
2. **Implementar Frontend FASE 2** (2-3h) → Testar renderiza form correto
3. **Implementar Backend FASE 3** (1-2h) → Testar PIX gera QR Code
4. **Polir + Testar E2E** (2-3h) → Deploy

**Total**: ~8-11 horas de trabalho focado

---

## 💡 DICAS

- Use `structlog.get_logger()` para logs
- Mantenha Stripe funcionando (não remova)
- Valide CPF/CNPJ no backend
- PIX QR Code expira em 10 min

**Bora começar!** 🚀🇧🇷

