# ❌ ERRO 500 ao Criar Produto - Stripe AuthenticationError

## 🔴 PROBLEMA IDENTIFICADO

### Erro ao criar produto
```json
POST https://api.fluu.digital/v1/products/
Status: 500 Internal Server Error
Error: stripe._error.AuthenticationError
```

### Causa Raiz
O sistema **SEMPRE** tenta criar o produto no Stripe, mas a chave do Stripe não está configurada (está como `YOUR_STRIPE_SECRET_KEY`).

### Log do Erro
```
<class 'stripe._error.AuthenticationError'>
path: /v1/products/
```

---

## ⚡ SOLUÇÃO RÁPIDA (5 minutos)

### Opção 1: Configurar Stripe Test Key

1. **Acesse**: https://dashboard.stripe.com/test/apikeys

2. **Copie a "Secret key"** (começa com `sk_test_...`)

3. **Atualize env.yaml**:
```bash
cd /Users/<USER>/Documents/www/Gateways/polar/deploy/cloud-run
```

Edite `env.yaml` e substitua:
```yaml
POLAR_STRIPE_SECRET_KEY: sk_test_SUA_CHAVE_AQUI
POLAR_STRIPE_PUBLISHABLE_KEY: pk_test_SUA_CHAVE_PUBLICA_AQUI
```

4. **Atualize o serviço**:
```bash
gcloud run services update fluu-api \
  --region us-east1 \
  --env-vars-file=env.yaml
```

⏱️ Tempo: ~2 minutos

### Opção 2: Desabilitar Stripe Temporariamente (Requer Código)

Modificar `server/polar/product/service.py` para tornar Stripe opcional:

```python
# Linha 250-265 em product/service.py
if settings.STRIPE_SECRET_KEY and settings.STRIPE_SECRET_KEY != "":
    stripe_product = await stripe_service.create_product(...)
    product.stripe_product_id = stripe_product.id
    
    for price in product.all_prices:
        if isinstance(price, HasStripePriceId):
            stripe_price = await stripe_service.create_price_for_product(...)
            price.stripe_price_id = stripe_price.id
        session.add(price)
else:
    log.warning("Stripe not configured, skipping Stripe product creation")
```

Isso requer rebuild e redeploy.

---

## 🎯 RECOMENDAÇÃO

**Use Opção 1** (configurar Stripe test key)

### Por quê?
- ✅ Mais rápido (2 min vs 15 min)
- ✅ Não requer mudança de código
- ✅ Stripe é necessário para cobranças funcionarem
- ✅ Pode usar mode de teste até ter conta real

### Como obter chaves de teste

1. **Criar conta Stripe**: https://dashboard.stripe.com/register
2. **Ativar test mode** (toggle no canto superior direito)
3. **Copiar chaves**: https://dashboard.stripe.com/test/apikeys
4. **Adicionar ao env.yaml**
5. **Atualizar serviço**

---

## 📝 ARQUIVO DE CONFIGURAÇÃO

Arquivo: `deploy/cloud-run/env.yaml`

```yaml
# ============================================
# STRIPE (OBRIGATÓRIO)
# ============================================
# Obter em: https://dashboard.stripe.com/test/apikeys (Test Mode)
# Ou: https://dashboard.stripe.com/apikeys (Live Mode)

POLAR_STRIPE_SECRET_KEY: sk_test_SUA_CHAVE_SECRETA_AQUI
POLAR_STRIPE_PUBLISHABLE_KEY: pk_test_SUA_CHAVE_PUBLICA_AQUI  
POLAR_STRIPE_WEBHOOK_SECRET: whsec_SEU_WEBHOOK_SECRET_AQUI
POLAR_STRIPE_STATEMENT_DESCRIPTOR: FLUU
```

---

## 🔄 COMANDO PARA ATUALIZAR

Depois de editar o `env.yaml` com as chaves corretas:

```bash
cd /Users/<USER>/Documents/www/Gateways/polar/deploy/cloud-run

gcloud run services update fluu-api \
  --region us-east1 \
  --env-vars-file=env.yaml
```

Aguarde ~2 minutos e teste criar produto novamente.

---

## 🧪 TESTE APÓS CONFIGURAR

1. Acesse: https://fluu.digital/dashboard/sua-org/products
2. Clique em "Create Product"
3. Preencha os dados
4. ✅ Deve criar o produto sem erro 500
5. ✅ Produto será criado tanto no banco de dados quanto no Stripe (test mode)

---

## 🆘 SE NÃO QUISER USAR STRIPE AGORA

### Solução Temporária: Mock do Stripe

Você pode criar chaves de API falsas que passam validação básica:

```bash
# ATENÇÃO: Isso NÃO vai funcionar para pagamentos reais!
# Apenas evita o erro 500 inicial

POLAR_STRIPE_SECRET_KEY: sk_test_fake_key_for_development_only_12345678
```

Mas isso vai falhar quando tentar realmente usar a API do Stripe.

---

## 📞 ALTERNATIVA: Usar Pagar.me

O sistema tem suporte parcial para Pagar.me, mas precisa de configuração:

```yaml
POLAR_ENABLE_PAGARME: "true"
POLAR_PAGARME_SECRET_KEY: sk_test_SUA_CHAVE_PAGARME
POLAR_PAGARME_PUBLISHABLE_KEY: pk_test_SUA_CHAVE_PAGARME
POLAR_PAGARME_WEBHOOK_SECRET: SEU_WEBHOOK_SECRET
```

Mas o Stripe ainda é chamado na criação de produto, então ainda precisa configurar ao menos uma chave de teste.

---

## ✅ PRÓXIMOS PASSOS

1. **AGORA**: Obter chaves de teste do Stripe
2. **2 min**: Atualizar env.yaml
3. **2 min**: Aplicar no Cloud Run
4. **1 min**: Testar criação de produto
5. **Total**: ~7 minutos

Link direto para chaves: https://dashboard.stripe.com/test/apikeys

---

**Criado em**: 2025-11-10  
**Prioridade**: 🔴 ALTA - Bloqueia criação de produtos  
**Tempo para resolver**: ~7 minutos


