{"name": "@polar-sh/checkout", "description": "Polar Checkout SDK", "author": "Polar", "license": "Apache-2.0", "type": "module", "version": "0.1.14", "repository": {"type": "git", "url": "https://github.com/polarsource/polar.git", "directory": "clients/packages/checkout"}, "scripts": {"build": "rm -rf dist/ && tsup", "lint": "prettier -c ."}, "exports": {"./embed": {"types": "./dist/embed.d.ts", "default": "./dist/embed.js"}, "./components": {"types": "./dist/components/index.d.ts", "default": "./dist/components/index.js"}, "./hooks": {"types": "./dist/hooks/index.d.ts", "default": "./dist/hooks/index.js"}, "./providers": {"types": "./dist/providers/index.d.ts", "default": "./dist/providers/index.js"}}, "devDependencies": {"@polar-sh/typescript-config": "workspace:*", "@stripe/react-stripe-js": "^4.0.2", "@stripe/stripe-js": "^7.9.0", "@types/react": "^19.2.2", "react": "^19.2.0", "terser": "^5.44.0", "tsup": "^8.5.0", "typescript": "latest"}, "publishConfig": {"access": "public"}, "dependencies": {"@polar-sh/sdk": "^0.40.2", "@polar-sh/ui": "workspace:^", "event-source-plus": "^0.1.12", "eventemitter3": "^5.0.1", "markdown-to-jsx": "^8.0.0", "react-hook-form": "^7.65.0", "react-icons": "^5.5.0"}, "peerDependencies": {"@stripe/react-stripe-js": "^3.6.0 || ^4.0.2", "@stripe/stripe-js": "^7.1.0", "react": "^18 || ^19"}}