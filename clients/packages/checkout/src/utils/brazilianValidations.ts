/**
 * Utilitários para validações e formatações brasileiras
 */

// Máscaras de formatação
export const formatCPF = (value: string): string => {
  return value
    .replace(/\D/g, '')
    .replace(/(\d{3})(\d)/, '$1.$2')
    .replace(/(\d{3})(\d)/, '$1.$2')
    .replace(/(\d{3})(\d{1,2})/, '$1-$2')
    .replace(/(-\d{2})\d+?$/, '$1')
}

export const formatCNPJ = (value: string): string => {
  return value
    .replace(/\D/g, '')
    .replace(/(\d{2})(\d)/, '$1.$2')
    .replace(/(\d{3})(\d)/, '$1.$2')
    .replace(/(\d{3})(\d)/, '$1/$2')
    .replace(/(\d{4})(\d{1,2})/, '$1-$2')
    .replace(/(-\d{2})\d+?$/, '$1')
}

export const formatPhone = (value: string): string => {
  return value
    .replace(/\D/g, '')
    .replace(/(\d{2})(\d)/, '($1) $2')
    .replace(/(\d{5})(\d)/, '$1-$2')
    .replace(/(-\d{4})\d+?$/, '$1')
}

export const formatCEP = (value: string): string => {
  return value
    .replace(/\D/g, '')
    .replace(/(\d{5})(\d)/, '$1-$2')
    .replace(/(-\d{3})\d+?$/, '$1')
}

// Validações
export const validateCPF = (cpf: string): boolean => {
  cpf = cpf.replace(/\D/g, '')
  
  if (cpf.length !== 11 || /^(\d)\1{10}$/.test(cpf)) {
    return false
  }

  let sum = 0
  for (let i = 0; i < 9; i++) {
    sum += parseInt(cpf.charAt(i)) * (10 - i)
  }
  let remainder = (sum * 10) % 11
  if (remainder === 10 || remainder === 11) remainder = 0
  if (remainder !== parseInt(cpf.charAt(9))) return false

  sum = 0
  for (let i = 0; i < 10; i++) {
    sum += parseInt(cpf.charAt(i)) * (11 - i)
  }
  remainder = (sum * 10) % 11
  if (remainder === 10 || remainder === 11) remainder = 0
  if (remainder !== parseInt(cpf.charAt(10))) return false

  return true
}

export const validateCNPJ = (cnpj: string): boolean => {
  cnpj = cnpj.replace(/\D/g, '')
  
  if (cnpj.length !== 14 || /^(\d)\1{13}$/.test(cnpj)) {
    return false
  }

  let length = cnpj.length - 2
  let numbers = cnpj.substring(0, length)
  let digits = cnpj.substring(length)
  let sum = 0
  let pos = length - 7

  for (let i = length; i >= 1; i--) {
    sum += parseInt(numbers.charAt(length - i)) * pos--
    if (pos < 2) pos = 9
  }

  let result = sum % 11 < 2 ? 0 : 11 - (sum % 11)
  if (result !== parseInt(digits.charAt(0))) return false

  length = length + 1
  numbers = cnpj.substring(0, length)
  sum = 0
  pos = length - 7

  for (let i = length; i >= 1; i--) {
    sum += parseInt(numbers.charAt(length - i)) * pos--
    if (pos < 2) pos = 9
  }

  result = sum % 11 < 2 ? 0 : 11 - (sum % 11)
  return result === parseInt(digits.charAt(1))
}

export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i
  return emailRegex.test(email)
}

export const validatePhone = (phone: string): boolean => {
  const cleanPhone = phone.replace(/\D/g, '')
  return cleanPhone.length >= 10 && cleanPhone.length <= 11
}

export const validateCEP = (cep: string): boolean => {
  const cleanCep = cep.replace(/\D/g, '')
  return cleanCep.length === 8
}

// Formatação automática baseada no comprimento
export const formatCpfCnpj = (value: string): string => {
  const cleanValue = value.replace(/\D/g, '')
  
  if (cleanValue.length <= 11) {
    return formatCPF(value)
  } else {
    return formatCNPJ(value)
  }
}

// Validação automática baseada no comprimento
export const validateCpfCnpj = (value: string): boolean => {
  const cleanValue = value.replace(/\D/g, '')
  
  if (cleanValue.length === 11) {
    return validateCPF(value)
  } else if (cleanValue.length === 14) {
    return validateCNPJ(value)
  }
  
  return false
}

// Interface para dados de endereço da API ViaCEP
export interface ViaCepResponse {
  cep: string
  logradouro: string
  complemento: string
  bairro: string
  localidade: string
  uf: string
  ibge: string
  gia: string
  ddd: string
  siafi: string
  erro?: boolean
}

// Busca de endereço por CEP
export const fetchAddressByCEP = async (cep: string): Promise<ViaCepResponse | null> => {
  const cleanCep = cep.replace(/\D/g, '')
  
  if (cleanCep.length !== 8) {
    throw new Error('CEP deve ter 8 dígitos')
  }

  try {
    const response = await fetch(`https://viacep.com.br/ws/${cleanCep}/json/`)
    
    if (!response.ok) {
      throw new Error('Erro ao consultar CEP')
    }
    
    const data: ViaCepResponse = await response.json()
    
    if (data.erro) {
      throw new Error('CEP não encontrado')
    }
    
    return data
  } catch (error) {
    console.error('Erro ao buscar CEP:', error)
    throw error
  }
}

// Estados brasileiros
export const BRAZILIAN_STATES = [
  { code: 'AC', name: 'Acre' },
  { code: 'AL', name: 'Alagoas' },
  { code: 'AP', name: 'Amapá' },
  { code: 'AM', name: 'Amazonas' },
  { code: 'BA', name: 'Bahia' },
  { code: 'CE', name: 'Ceará' },
  { code: 'DF', name: 'Distrito Federal' },
  { code: 'ES', name: 'Espírito Santo' },
  { code: 'GO', name: 'Goiás' },
  { code: 'MA', name: 'Maranhão' },
  { code: 'MT', name: 'Mato Grosso' },
  { code: 'MS', name: 'Mato Grosso do Sul' },
  { code: 'MG', name: 'Minas Gerais' },
  { code: 'PA', name: 'Pará' },
  { code: 'PB', name: 'Paraíba' },
  { code: 'PR', name: 'Paraná' },
  { code: 'PE', name: 'Pernambuco' },
  { code: 'PI', name: 'Piauí' },
  { code: 'RJ', name: 'Rio de Janeiro' },
  { code: 'RN', name: 'Rio Grande do Norte' },
  { code: 'RS', name: 'Rio Grande do Sul' },
  { code: 'RO', name: 'Rondônia' },
  { code: 'RR', name: 'Roraima' },
  { code: 'SC', name: 'Santa Catarina' },
  { code: 'SP', name: 'São Paulo' },
  { code: 'SE', name: 'Sergipe' },
  { code: 'TO', name: 'Tocantins' }
]

// Utilitário para determinar se é CPF ou CNPJ
export const getCpfCnpjType = (value: string): 'cpf' | 'cnpj' | 'unknown' => {
  const cleanValue = value.replace(/\D/g, '')
  
  if (cleanValue.length <= 11) {
    return 'cpf'
  } else if (cleanValue.length <= 14) {
    return 'cnpj'
  }
  
  return 'unknown'
}

// Placeholder baseado no tipo de documento
export const getCpfCnpjPlaceholder = (value: string): string => {
  const type = getCpfCnpjType(value)
  
  switch (type) {
    case 'cpf':
      return '000.000.000-00'
    case 'cnpj':
      return '00.000.000/0000-00'
    default:
      return 'CPF ou CNPJ'
  }
}

// Mensagem de erro personalizada
export const getCpfCnpjErrorMessage = (value: string): string => {
  const type = getCpfCnpjType(value)
  
  switch (type) {
    case 'cpf':
      return 'CPF inválido'
    case 'cnpj':
      return 'CNPJ inválido'
    default:
      return 'CPF/CNPJ inválido'
  }
}

// Limpar formatação
export const cleanNumericString = (value: string): string => {
  return value.replace(/\D/g, '')
}

// Verificar se é um número de telefone celular
export const isCellPhone = (phone: string): boolean => {
  const cleanPhone = cleanNumericString(phone)
  // Celular tem 11 dígitos e o 9º dígito é 9
  return cleanPhone.length === 11 && cleanPhone.charAt(2) === '9'
}

// Formatação de moeda brasileira
export const formatCurrency = (value: number): string => {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  }).format(value / 100) // Assumindo que o valor está em centavos
}
