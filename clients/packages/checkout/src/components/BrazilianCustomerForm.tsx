'use client'

import { useState, useEffect } from 'react'
import Input from '@polar-sh/ui/components/atoms/Input'
import { UseFormReturn } from 'react-hook-form'
import type { CheckoutUpdatePublic } from '@polar-sh/sdk/models/components/checkoutupdatepublic'
import {
  formatCEP,
  formatCpfCnpj,
  validateCpfCnpj,
  getCpfCnpjType,
  getCpfCnpjPlaceholder,
  fetchAddressByCEP
} from '../utils/brazilianValidations'

interface BrazilianCustomerFormProps {
  form: UseFormReturn<CheckoutUpdatePublic>
}

const BrazilianCustomerForm = ({ form }: BrazilianCustomerFormProps) => {
  const [isLoadingCep, setIsLoadingCep] = useState(false)
  const [cpfCnpjType, setCpfCnpjType] = useState<'cpf' | 'cnpj'>('cpf')

  const handleAddressByCEP = async (cep: string) => {
    const cleanCep = cep.replace(/\D/g, '')

    if (cleanCep.length !== 8) return

    setIsLoadingCep(true)

    try {
      const data = await fetchAddressByCEP(cep)

      if (data) {
        // Preencher campos de endereço automaticamente
        form.setValue('customerBillingAddress.line1', data.logradouro)
        form.setValue('customerBillingAddress.city', data.localidade)
        form.setValue('customerBillingAddress.state', data.uf)
      }
    } catch (error) {
      console.error('Erro ao buscar CEP:', error)
    } finally {
      setIsLoadingCep(false)
    }
  }

  const handleCpfCnpjChange = (value: string) => {
    const type = getCpfCnpjType(value)
    setCpfCnpjType(type === 'unknown' ? 'cpf' : type)
    return formatCpfCnpj(value)
  }

  return (
    <div className="space-y-6">
      <div className="border-b border-gray-200 pb-4">
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          📋 Dados do Cliente
        </h3>
        <p className="text-sm text-gray-600">
          Preencha seus dados para finalizar a compra
        </p>
      </div>

      {/* Dados Pessoais */}
      <div className="space-y-4">
        <h4 className="font-medium text-gray-800">Dados Pessoais</h4>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Nome Completo *
            </label>
            <Input
              {...form.register('customerName', { required: 'Nome é obrigatório' })}
              placeholder="João Silva"
              className="w-full"
            />
            {form.formState.errors.customerName && (
              <p className="text-red-500 text-xs mt-1">
                {form.formState.errors.customerName.message}
              </p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Email *
            </label>
            <Input
              {...form.register('customerEmail', {
                required: 'Email é obrigatório',
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: 'Email inválido'
                }
              })}
              type="email"
              placeholder="<EMAIL>"
              className="w-full"
            />
            {form.formState.errors.customerEmail && (
              <p className="text-red-500 text-xs mt-1">
                {form.formState.errors.customerEmail.message}
              </p>
            )}
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            CPF/CNPJ *
          </label>
          <Input
            {...form.register('customerTaxId', {
              required: 'CPF/CNPJ é obrigatório',
              validate: (value) => {
                const stringValue = String(value)
                return validateCpfCnpj(stringValue) || 'CPF/CNPJ inválido'
              },
              onChange: (e) => {
                e.target.value = handleCpfCnpjChange(e.target.value)
              }
            })}
            placeholder={cpfCnpjType === 'cpf' ? '000.000.000-00' : '00.000.000/0000-00'}
            className="w-full"
          />
          {form.formState.errors.customerTaxId && (
            <p className="text-red-500 text-xs mt-1">
              {form.formState.errors.customerTaxId.message}
            </p>
          )}
        </div>
      </div>

      {/* Endereço Simplificado */}
      <div className="space-y-4">
        <h4 className="font-medium text-gray-800">Endereço de Cobrança</h4>

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <span className="text-blue-600">🇧🇷</span>
            <div>
              <div className="font-medium text-blue-800">Endereço Brasileiro</div>
              <div className="text-sm text-blue-700">
                Preencha seu endereço completo para emissão da nota fiscal
              </div>
            </div>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            País *
          </label>
          <Input
            {...form.register('customerBillingAddress.country', { required: 'País é obrigatório' })}
            value="BR"
            readOnly
            className="w-full bg-gray-50"
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              CEP *
            </label>
            <Input
              {...form.register('customerBillingAddress.postalCode', {
                required: 'CEP é obrigatório',
                onChange: (e) => {
                  const formatted = formatCEP(e.target.value)
                  e.target.value = formatted
                  if (formatted.replace(/\D/g, '').length === 8) {
                    fetchAddressByCEP(formatted)
                  }
                }
              })}
              placeholder="00000-000"
              className="w-full"
            />
            {isLoadingCep && (
              <p className="text-blue-500 text-xs mt-1">Buscando endereço...</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Estado *
            </label>
            <Input
              {...form.register('customerBillingAddress.state', { required: 'Estado é obrigatório' })}
              placeholder="SP"
              className="w-full"
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Cidade *
          </label>
          <Input
            {...form.register('customerBillingAddress.city', { required: 'Cidade é obrigatória' })}
            placeholder="São Paulo"
            className="w-full"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Endereço Completo *
          </label>
          <Input
            {...form.register('customerBillingAddress.line1', { required: 'Endereço é obrigatório' })}
            placeholder="Rua das Flores, 123, Centro"
            className="w-full"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Complemento
          </label>
          <Input
            {...form.register('customerBillingAddress.line2')}
            placeholder="Apto 101, Bloco A"
            className="w-full"
          />
        </div>
      </div>
    </div>
  )
}

export default BrazilianCustomerForm
