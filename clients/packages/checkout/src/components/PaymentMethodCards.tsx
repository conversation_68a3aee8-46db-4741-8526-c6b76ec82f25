'use client'

import { useState } from 'react'
import { 
  FaCreditCard, 
  FaBarcode, 
  FaApplePay, 
  FaGooglePay 
} from 'react-icons/fa'
import { SiPix, SiSamsungpay } from 'react-icons/si'
import Button from '@polar-sh/ui/components/atoms/Button'
import Input from '@polar-sh/ui/components/atoms/Input'

export type PaymentMethod = 'credit_card' | 'pix' | 'boleto' | 'apple_pay' | 'google_pay' | 'samsung_pay'

interface PaymentMethodCardsProps {
  selectedMethod: PaymentMethod
  onMethodSelect: (method: PaymentMethod) => void
  onSubmit: () => void
  loading?: boolean
  disabled?: boolean
}

const PaymentMethodCards = ({ 
  selectedMethod, 
  onMethodSelect, 
  onSubmit, 
  loading = false, 
  disabled = false 
}: PaymentMethodCardsProps) => {
  const [cardData, setCardData] = useState({
    holderName: '',
    number: '',
    expiry: '',
    cvv: '',
    installments: '1'
  })

  const paymentMethods = [
    {
      id: 'credit_card' as PaymentMethod,
      icon: <FaCreditCard className="w-8 h-8" />,
      label: 'Cartão',
      functional: true
    },
    {
      id: 'pix' as PaymentMethod,
      icon: <SiPix className="w-8 h-8 text-teal-500" />,
      label: 'PIX',
      functional: true
    },
    {
      id: 'apple_pay' as PaymentMethod,
      icon: <FaApplePay className="w-8 h-8" />,
      label: 'Apple Pay',
      functional: false
    },
    {
      id: 'google_pay' as PaymentMethod,
      icon: <FaGooglePay className="w-8 h-8" />,
      label: 'Google Pay',
      functional: false
    },
    {
      id: 'samsung_pay' as PaymentMethod,
      icon: <SiSamsungpay className="w-8 h-8" />,
      label: 'Samsung Pay',
      functional: false
    },
    {
      id: 'boleto' as PaymentMethod,
      icon: <FaBarcode className="w-8 h-8" />,
      label: 'Boleto',
      functional: false
    }
  ]

  const formatCardNumber = (value: string) => {
    return value
      .replace(/\D/g, '')
      .replace(/(\d{4})(?=\d)/g, '$1 ')
      .slice(0, 19)
  }

  const formatExpiry = (value: string) => {
    return value
      .replace(/\D/g, '')
      .replace(/(\d{2})(\d)/, '$1/$2')
      .slice(0, 5)
  }

  const formatCVV = (value: string) => {
    return value.replace(/\D/g, '').slice(0, 4)
  }

  const handleCardInputChange = (field: string, value: string) => {
    let formattedValue = value

    switch (field) {
      case 'number':
        formattedValue = formatCardNumber(value)
        break
      case 'expiry':
        formattedValue = formatExpiry(value)
        break
      case 'cvv':
        formattedValue = formatCVV(value)
        break
    }

    setCardData(prev => ({
      ...prev,
      [field]: formattedValue
    }))
  }

  const renderCreditCardForm = () => (
    <div className="space-y-4 mt-6">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Nome do titular
        </label>
        <Input
          type="text"
          placeholder="Digite o nome do titular"
          value={cardData.holderName}
          onChange={(e) => handleCardInputChange('holderName', e.target.value)}
          className="w-full"
        />
      </div>

      <div className="relative">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Número do cartão
        </label>
        <Input
          type="text"
          placeholder="Digite o número do seu cartão"
          value={cardData.number}
          onChange={(e) => handleCardInputChange('number', e.target.value)}
          className="w-full pr-12"
        />
        <div className="absolute right-3 top-9 text-gray-400">
          <FaCreditCard className="w-5 h-5" />
        </div>
      </div>

      <div className="grid grid-cols-3 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Vencimento
          </label>
          <Input
            type="text"
            placeholder="MM/AA"
            value={cardData.expiry}
            onChange={(e) => handleCardInputChange('expiry', e.target.value)}
            className="w-full"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            CVV
          </label>
          <Input
            type="text"
            placeholder="000"
            value={cardData.cvv}
            onChange={(e) => handleCardInputChange('cvv', e.target.value)}
            className="w-full"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Parcelamento
          </label>
          <select
            value={cardData.installments}
            onChange={(e) => handleCardInputChange('installments', e.target.value)}
            className="w-full h-10 px-3 py-2 border border-gray-200 rounded-xl bg-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="1">1x de R$ 20,19</option>
            <option value="2">2x de R$ 10,10</option>
            <option value="3">3x de R$ 6,73</option>
            <option value="6">6x de R$ 3,37</option>
            <option value="12">12x de R$ 1,68</option>
          </select>
        </div>
      </div>
    </div>
  )

  const renderPixInfo = () => (
    <div className="mt-6 p-4 bg-teal-50 border border-teal-200 rounded-lg">
      <div className="flex items-center space-x-2">
        <SiPix className="w-5 h-5 text-teal-600" />
        <div>
          <div className="font-medium text-teal-800">PIX - Pagamento Instantâneo</div>
          <div className="text-sm text-teal-700">
            Aprovação imediata • Disponível 24h • Sem taxas extras
          </div>
        </div>
      </div>
    </div>
  )

  const renderMockPaymentInfo = (method: PaymentMethod) => {
    const getPaymentInfo = () => {
      switch (method) {
        case 'boleto':
          return (
            <div className="mt-6 p-4 bg-orange-50 border border-orange-200 rounded-lg">
              <div className="flex items-center space-x-2">
                <div className="text-orange-600">
                  <FaBarcode className="w-5 h-5" />
                </div>
                <div>
                  <div className="font-medium text-orange-800">Boleto Bancário</div>
                  <div className="text-sm text-orange-700">
                    Vencimento em 3 dias úteis • Pague em qualquer banco
                  </div>
                  <div className="text-xs text-orange-600 mt-1">
                    🚧 Em breve - Integração em desenvolvimento
                  </div>
                </div>
              </div>
            </div>
          )
        case 'apple_pay':
          return (
            <div className="mt-6 p-4 bg-gray-50 border border-gray-200 rounded-lg">
              <div className="flex items-center space-x-2">
                <div className="text-gray-600">
                  <FaApplePay className="w-5 h-5" />
                </div>
                <div>
                  <div className="font-medium text-gray-800">Apple Pay</div>
                  <div className="text-sm text-gray-700">
                    Pagamento rápido e seguro com Touch ID ou Face ID
                  </div>
                  <div className="text-xs text-gray-600 mt-1">
                    🚧 Em breve - Integração em desenvolvimento
                  </div>
                </div>
              </div>
            </div>
          )
        case 'google_pay':
          return (
            <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center space-x-2">
                <div className="text-blue-600">
                  <FaGooglePay className="w-5 h-5" />
                </div>
                <div>
                  <div className="font-medium text-blue-800">Google Pay</div>
                  <div className="text-sm text-blue-700">
                    Pagamento seguro com sua conta Google
                  </div>
                  <div className="text-xs text-blue-600 mt-1">
                    🚧 Em breve - Integração em desenvolvimento
                  </div>
                </div>
              </div>
            </div>
          )
        case 'samsung_pay':
          return (
            <div className="mt-6 p-4 bg-purple-50 border border-purple-200 rounded-lg">
              <div className="flex items-center space-x-2">
                <div className="text-purple-600">
                  <SiSamsungpay className="w-5 h-5" />
                </div>
                <div>
                  <div className="font-medium text-purple-800">Samsung Pay</div>
                  <div className="text-sm text-purple-700">
                    Pagamento seguro com dispositivos Samsung
                  </div>
                  <div className="text-xs text-purple-600 mt-1">
                    🚧 Em breve - Integração em desenvolvimento
                  </div>
                </div>
              </div>
            </div>
          )
        default:
          return null
      }
    }

    return getPaymentInfo()
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <FaCreditCard className="w-5 h-5 text-gray-600" />
          <h2 className="text-lg font-medium text-gray-900">Pagamento</h2>
        </div>
        <div className="text-sm text-gray-500">
          {selectedMethod === 'credit_card' ? 'Cartão de crédito' : 
           selectedMethod === 'pix' ? 'PIX' :
           selectedMethod === 'boleto' ? 'Boleto bancário' :
           selectedMethod === 'apple_pay' ? 'Apple Pay' :
           selectedMethod === 'google_pay' ? 'Google Pay' :
           selectedMethod === 'samsung_pay' ? 'Samsung Pay' : ''}
        </div>
      </div>

      {/* Payment Method Cards */}
      <div className="grid grid-cols-6 gap-3">
        {paymentMethods.map((method) => (
          <button
            key={method.id}
            onClick={() => onMethodSelect(method.id)}
            disabled={disabled}
            className={`
              relative p-4 border-2 rounded-lg transition-all duration-200 
              flex flex-col items-center justify-center space-y-2 min-h-[80px]
              ${selectedMethod === method.id 
                ? 'border-blue-500 bg-blue-50 shadow-md' 
                : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
              }
              ${!method.functional ? 'opacity-60' : ''}
              ${disabled ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'}
            `}
          >
            <div className={`
              ${selectedMethod === method.id ? 'text-blue-600' : 'text-gray-600'}
              ${!method.functional ? 'grayscale' : ''}
            `}>
              {method.icon}
            </div>
            
            {selectedMethod === method.id && (
              <div className="absolute -top-1 -right-1 w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
                <div className="w-2 h-2 bg-white rounded-full" />
              </div>
            )}
            
            {!method.functional && (
              <div className="absolute -top-1 -left-1 text-xs bg-yellow-100 text-yellow-800 px-1 py-0.5 rounded text-[10px]">
                Em breve
              </div>
            )}
          </button>
        ))}
      </div>

      {/* Payment Method Content */}
      {selectedMethod === 'credit_card' && renderCreditCardForm()}
      {selectedMethod === 'pix' && renderPixInfo()}
      {['boleto', 'apple_pay', 'google_pay', 'samsung_pay'].includes(selectedMethod) && 
        renderMockPaymentInfo(selectedMethod)}

      {/* Submit Button */}
      <div className="pt-4">
        <Button
          onClick={onSubmit}
          disabled={disabled || loading}
          loading={loading}
          fullWidth
          size="lg"
          className="bg-blue-600 hover:bg-blue-700 text-white"
        >
          {loading ? 'Processando...' : 'Confirmar Pagamento'}
        </Button>
      </div>
    </div>
  )
}

export default PaymentMethodCards
