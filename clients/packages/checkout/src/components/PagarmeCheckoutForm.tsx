'use client'

import { useState } from 'react'
import type { CheckoutPublic } from '@polar-sh/sdk/models/components/checkoutpublic'
import type { CheckoutPublicConfirmed } from '@polar-sh/sdk/models/components/checkoutpublicconfirmed'
import type { CheckoutUpdatePublic } from '@polar-sh/sdk/models/components/checkoutupdatepublic'
import { UseFormReturn } from 'react-hook-form'
import { ThemingPresetProps } from '@polar-sh/ui/hooks/theming'

interface CheckoutFormProps {
  form: UseFormReturn<CheckoutUpdatePublic>
  checkout: CheckoutPublic
  update: (data: CheckoutUpdatePublic) => Promise<CheckoutPublic>
  confirm: (
    data: any,
    stripe?: any,
    elements?: any,
  ) => Promise<CheckoutPublicConfirmed>
  loading: boolean
  loadingLabel: string | undefined
  disabled?: boolean
  isUpdatePending?: boolean
  theme?: 'light' | 'dark'
  themePreset: ThemingPresetProps
}

interface BaseCheckoutFormProps {
  form: UseFormReturn<CheckoutUpdatePublic>
  checkout: CheckoutPublic
  confirm: (data: any) => Promise<CheckoutPublicConfirmed>
  update: (data: CheckoutUpdatePublic) => Promise<CheckoutPublic>
  loading: boolean
  loadingLabel: string | undefined
  disabled?: boolean
  isUpdatePending?: boolean
  themePreset: ThemingPresetProps
}

type PaymentMethod = 'pix' | 'credit_card' | 'boleto'

const PagarmeCheckoutForm = (props: CheckoutFormProps) => {
  const {
    checkout,
    form,
    confirm,
    loading,
    loadingLabel,
    disabled,
  } = props

  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod>('pix')
  const [pixQrCode, setPixQrCode] = useState<string | null>(null)
  const [showPixModal, setShowPixModal] = useState(false)

  const handleSubmit = form.handleSubmit(async (data) => {
    try {
      const confirmData = {
        ...data,
        payment_method: paymentMethod,
      }

      const result = await confirm(confirmData, null, null)

      // Se for PIX, mostrar QR Code
      if (paymentMethod === 'pix' && result.payment_processor_metadata?.pix_qr_code) {
        setPixQrCode(result.payment_processor_metadata.pix_qr_code)
        setShowPixModal(true)
      }

      return result
    } catch (error) {
      console.error('Erro ao confirmar pagamento:', error)
      throw error
    }
  })

  const PaymentMethodSelector = () => (
    <div className="space-y-3 mb-6">
      <h3 className="text-lg font-medium text-gray-900">Forma de Pagamento</h3>
      
      <div className="grid grid-cols-1 gap-2">
        <button
          type="button"
          onClick={() => setPaymentMethod('pix')}
          className={`p-4 border rounded-lg text-left transition-colors ${
            paymentMethod === 'pix'
              ? 'border-green-500 bg-green-50 text-green-900'
              : 'border-gray-300 hover:border-gray-400'
          }`}
        >
          <div className="flex items-center space-x-3">
            <span className="text-2xl">⚡</span>
            <div>
              <div className="font-medium">PIX</div>
              <div className="text-sm text-gray-600">Aprovação instantânea</div>
            </div>
            {paymentMethod === 'pix' && (
              <span className="ml-auto text-green-600">✓</span>
            )}
          </div>
        </button>

        <button
          type="button"
          onClick={() => setPaymentMethod('credit_card')}
          className={`p-4 border rounded-lg text-left transition-colors ${
            paymentMethod === 'credit_card'
              ? 'border-blue-500 bg-blue-50 text-blue-900'
              : 'border-gray-300 hover:border-gray-400'
          }`}
        >
          <div className="flex items-center space-x-3">
            <span className="text-2xl">💳</span>
            <div>
              <div className="font-medium">Cartão de Crédito</div>
              <div className="text-sm text-gray-600">Visa, Mastercard, Elo</div>
            </div>
            {paymentMethod === 'credit_card' && (
              <span className="ml-auto text-blue-600">✓</span>
            )}
          </div>
        </button>

        <button
          type="button"
          onClick={() => setPaymentMethod('boleto')}
          className={`p-4 border rounded-lg text-left transition-colors ${
            paymentMethod === 'boleto'
              ? 'border-orange-500 bg-orange-50 text-orange-900'
              : 'border-gray-300 hover:border-gray-400'
          }`}
        >
          <div className="flex items-center space-x-3">
            <span className="text-2xl">🧾</span>
            <div>
              <div className="font-medium">Boleto Bancário</div>
              <div className="text-sm text-gray-600">Vence em 3 dias úteis</div>
            </div>
            {paymentMethod === 'boleto' && (
              <span className="ml-auto text-orange-600">✓</span>
            )}
          </div>
        </button>
      </div>
    </div>
  )

  const CreditCardForm = () => (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Número do Cartão
        </label>
        <input
          type="text"
          placeholder="1234 5678 9012 3456"
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>
      
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Validade
          </label>
          <input
            type="text"
            placeholder="MM/AA"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            CVV
          </label>
          <input
            type="text"
            placeholder="123"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      </div>
    </div>
  )

  const PixModal = () => {
    if (!showPixModal || !pixQrCode) return null

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white p-6 rounded-lg max-w-md w-full mx-4">
          <h3 className="text-lg font-medium mb-4">Pagamento PIX</h3>
          
          <div className="text-center mb-4">
            <div className="bg-gray-100 p-4 rounded-lg mb-4">
              <div className="text-6xl mb-2">📱</div>
              <p className="text-sm text-gray-600">
                Escaneie o QR Code com seu app do banco
              </p>
            </div>
            
            <div className="bg-white border-2 border-gray-300 p-4 rounded-lg">
              <div className="text-xs font-mono break-all bg-gray-50 p-2 rounded">
                {pixQrCode}
              </div>
            </div>
          </div>
          
          <div className="flex space-x-3">
            <button
              onClick={() => navigator.clipboard.writeText(pixQrCode)}
              className="flex-1 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
            >
              Copiar Código PIX
            </button>
            <button
              onClick={() => setShowPixModal(false)}
              className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
            >
              Fechar
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <PaymentMethodSelector />

      {paymentMethod === 'credit_card' && <CreditCardForm />}

      {paymentMethod === 'pix' && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <span className="text-green-600">⚡</span>
            <span className="text-sm text-green-800">
              Após confirmar, você receberá o QR Code do PIX
            </span>
          </div>
        </div>
      )}

      {paymentMethod === 'boleto' && (
        <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <span className="text-orange-600">🧾</span>
            <span className="text-sm text-orange-800">
              O boleto será gerado após a confirmação
            </span>
          </div>
        </div>
      )}

      <div className="pt-4">
        <button
          type="button"
          onClick={handleSubmit}
          disabled={disabled || loading}
          className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading ? (loadingLabel || 'Processando...') : 'Confirmar Pagamento'}
        </button>
      </div>

      <PixModal />
    </div>
  )
}

export default PagarmeCheckoutForm
