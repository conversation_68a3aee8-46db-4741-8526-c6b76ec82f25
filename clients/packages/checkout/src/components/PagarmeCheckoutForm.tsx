'use client'

import { useState } from 'react'
import type { CheckoutPublic } from '@polar-sh/sdk/models/components/checkoutpublic'
import type { CheckoutPublicConfirmed } from '@polar-sh/sdk/models/components/checkoutpublicconfirmed'
import type { CheckoutUpdatePublic } from '@polar-sh/sdk/models/components/checkoutupdatepublic'
import { UseFormReturn } from 'react-hook-form'
import { ThemingPresetProps } from '@polar-sh/ui/hooks/theming'
import Button from '@polar-sh/ui/components/atoms/Button'
import BrazilianCustomerForm from './BrazilianCustomerForm'
import PaymentMethodCards, { type PaymentMethod as PaymentMethodType } from './PaymentMethodCards'

interface CheckoutFormProps {
  form: UseFormReturn<CheckoutUpdatePublic>
  checkout: CheckoutPublic
  update: (data: CheckoutUpdatePublic) => Promise<CheckoutPublic>
  confirm: (
    data: any,
    stripe?: any,
    elements?: any,
  ) => Promise<CheckoutPublicConfirmed>
  loading: boolean
  loadingLabel: string | undefined
  disabled?: boolean
  isUpdatePending?: boolean
  theme?: 'light' | 'dark'
  themePreset: ThemingPresetProps
}



type PaymentMethod = PaymentMethodType

const PagarmeCheckoutForm = (props: CheckoutFormProps) => {
  const {
    form,
    confirm,
    loading,
    disabled,
  } = props

  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod>('credit_card')
  const [pixQrCode, setPixQrCode] = useState<string | null>(null)
  const [showPixModal, setShowPixModal] = useState(false)

  const handleSubmit = form.handleSubmit(async (data) => {
    try {
      const confirmData = {
        ...data,
        payment_method: paymentMethod,
      }

      const result = await confirm(confirmData, null, null)

      // Se for PIX, mostrar QR Code
      if (paymentMethod === 'pix' && result.paymentProcessorMetadata?.pix_qr_code) {
        setPixQrCode(result.paymentProcessorMetadata.pix_qr_code)
        setShowPixModal(true)
      }

      return result
    } catch (error) {
      console.error('Erro ao confirmar pagamento:', error)
      throw error
    }
  })

  const handlePaymentMethodSelect = (method: PaymentMethod) => {
    setPaymentMethod(method)
  }



  const PixModal = () => {
    if (!showPixModal || !pixQrCode) return null

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white p-6 rounded-lg max-w-md w-full mx-4 dark:bg-gray-800">
          <h3 className="text-lg font-medium mb-4 text-gray-900 dark:text-white">
            🎉 Pagamento PIX Gerado!
          </h3>

          <div className="text-center mb-6">
            <div className="bg-green-50 border border-green-200 p-4 rounded-lg mb-4 dark:bg-green-900/20 dark:border-green-800">
              <div className="text-4xl mb-2">⚡</div>
              <p className="text-sm text-green-800 dark:text-green-200 font-medium">
                Escaneie o QR Code com seu app do banco
              </p>
              <p className="text-xs text-green-600 dark:text-green-300 mt-1">
                Ou copie e cole o código PIX
              </p>
            </div>

            <div className="bg-gray-50 border border-gray-200 p-3 rounded-lg dark:bg-gray-700 dark:border-gray-600">
              <div className="text-xs font-mono break-all text-gray-700 dark:text-gray-300 max-h-20 overflow-y-auto">
                {pixQrCode}
              </div>
            </div>
          </div>

          <div className="flex space-x-3">
            <Button
              onClick={() => navigator.clipboard.writeText(pixQrCode)}
              className="flex-1 bg-green-600 hover:bg-green-700 text-white"
            >
              📋 Copiar Código PIX
            </Button>
            <Button
              onClick={() => setShowPixModal(false)}
              variant="outline"
            >
              Fechar
            </Button>
          </div>

          <div className="mt-4 text-center">
            <p className="text-xs text-gray-500 dark:text-gray-400">
              ⏱️ O pagamento será confirmado automaticamente
            </p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <BrazilianCustomerForm form={form} />

      <PaymentMethodCards
        selectedMethod={paymentMethod}
        onMethodSelect={handlePaymentMethodSelect}
        onSubmit={handleSubmit}
        loading={loading}
        disabled={disabled}
      />

      <PixModal />
    </div>
  )
}

export default PagarmeCheckoutForm
