'use client'

import { useState } from 'react'
import type { CheckoutPublic } from '@polar-sh/sdk/models/components/checkoutpublic'
import type { CheckoutPublicConfirmed } from '@polar-sh/sdk/models/components/checkoutpublicconfirmed'
import type { CheckoutUpdatePublic } from '@polar-sh/sdk/models/components/checkoutupdatepublic'
import { UseFormReturn } from 'react-hook-form'
import { ThemingPresetProps } from '@polar-sh/ui/hooks/theming'
import Button from '@polar-sh/ui/components/atoms/Button'
import Input from '@polar-sh/ui/components/atoms/Input'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@polar-sh/ui/components/atoms/Tabs'
import BrazilianCustomerForm from './BrazilianCustomerForm'

interface CheckoutFormProps {
  form: UseFormReturn<CheckoutUpdatePublic>
  checkout: CheckoutPublic
  update: (data: CheckoutUpdatePublic) => Promise<CheckoutPublic>
  confirm: (
    data: any,
    stripe?: any,
    elements?: any,
  ) => Promise<CheckoutPublicConfirmed>
  loading: boolean
  loadingLabel: string | undefined
  disabled?: boolean
  isUpdatePending?: boolean
  theme?: 'light' | 'dark'
  themePreset: ThemingPresetProps
}

interface BaseCheckoutFormProps {
  form: UseFormReturn<CheckoutUpdatePublic>
  checkout: CheckoutPublic
  confirm: (data: any) => Promise<CheckoutPublicConfirmed>
  update: (data: CheckoutUpdatePublic) => Promise<CheckoutPublic>
  loading: boolean
  loadingLabel: string | undefined
  disabled?: boolean
  isUpdatePending?: boolean
  themePreset: ThemingPresetProps
}

type PaymentMethod = 'pix' | 'credit_card' | 'boleto'

const PagarmeCheckoutForm = (props: CheckoutFormProps) => {
  const {
    checkout,
    form,
    confirm,
    loading,
    loadingLabel,
    disabled,
  } = props

  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod>('pix')
  const [pixQrCode, setPixQrCode] = useState<string | null>(null)
  const [showPixModal, setShowPixModal] = useState(false)

  const handleSubmit = form.handleSubmit(async (data) => {
    try {
      const confirmData = {
        ...data,
        payment_method: paymentMethod,
      }

      const result = await confirm(confirmData, null, null)

      // Se for PIX, mostrar QR Code
      if (paymentMethod === 'pix' && result.paymentProcessorMetadata?.pix_qr_code) {
        setPixQrCode(result.paymentProcessorMetadata.pix_qr_code)
        setShowPixModal(true)
      }

      return result
    } catch (error) {
      console.error('Erro ao confirmar pagamento:', error)
      throw error
    }
  })

  const PaymentMethodTabs = () => (
    <div className="mb-6">
      <Tabs value={paymentMethod} onValueChange={(value) => setPaymentMethod(value as PaymentMethod)}>
        <TabsList className="grid w-full grid-cols-3 mb-6">
          <TabsTrigger value="pix" className="flex items-center gap-2">
            <span>⚡</span>
            PIX
          </TabsTrigger>
          <TabsTrigger value="credit_card" className="flex items-center gap-2">
            <span>💳</span>
            Cartão
          </TabsTrigger>
          <TabsTrigger value="boleto" className="flex items-center gap-2">
            <span>🧾</span>
            Boleto
          </TabsTrigger>
        </TabsList>

        <TabsContent value="pix" className="space-y-4">
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center space-x-2">
              <span className="text-green-600">⚡</span>
              <div>
                <div className="font-medium text-green-800">PIX - Pagamento Instantâneo</div>
                <div className="text-sm text-green-700">
                  Aprovação imediata • Disponível 24h • Sem taxas extras
                </div>
              </div>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="credit_card" className="space-y-4">
          <CreditCardForm />
        </TabsContent>

        <TabsContent value="boleto" className="space-y-4">
          <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
            <div className="flex items-center space-x-2">
              <span className="text-orange-600">🧾</span>
              <div>
                <div className="font-medium text-orange-800">Boleto Bancário</div>
                <div className="text-sm text-orange-700">
                  Vencimento em 3 dias úteis • Pague em qualquer banco
                </div>
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )

  const CreditCardForm = () => (
    <div className="space-y-4">
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
        <div className="flex items-center space-x-2">
          <span className="text-blue-600">💳</span>
          <div>
            <div className="font-medium text-blue-800">Cartão de Crédito</div>
            <div className="text-sm text-blue-700">
              Visa, Mastercard, Elo • Parcelamento disponível
            </div>
          </div>
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Número do Cartão
        </label>
        <Input
          type="text"
          placeholder="1234 5678 9012 3456"
          className="w-full"
        />
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Validade
          </label>
          <Input
            type="text"
            placeholder="MM/AA"
            className="w-full"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            CVV
          </label>
          <Input
            type="text"
            placeholder="123"
            className="w-full"
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Nome no Cartão
        </label>
        <Input
          type="text"
          placeholder="João Silva"
          className="w-full"
        />
      </div>
    </div>
  )

  const PixModal = () => {
    if (!showPixModal || !pixQrCode) return null

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white p-6 rounded-lg max-w-md w-full mx-4 dark:bg-gray-800">
          <h3 className="text-lg font-medium mb-4 text-gray-900 dark:text-white">
            🎉 Pagamento PIX Gerado!
          </h3>

          <div className="text-center mb-6">
            <div className="bg-green-50 border border-green-200 p-4 rounded-lg mb-4 dark:bg-green-900/20 dark:border-green-800">
              <div className="text-4xl mb-2">⚡</div>
              <p className="text-sm text-green-800 dark:text-green-200 font-medium">
                Escaneie o QR Code com seu app do banco
              </p>
              <p className="text-xs text-green-600 dark:text-green-300 mt-1">
                Ou copie e cole o código PIX
              </p>
            </div>

            <div className="bg-gray-50 border border-gray-200 p-3 rounded-lg dark:bg-gray-700 dark:border-gray-600">
              <div className="text-xs font-mono break-all text-gray-700 dark:text-gray-300 max-h-20 overflow-y-auto">
                {pixQrCode}
              </div>
            </div>
          </div>

          <div className="flex space-x-3">
            <Button
              onClick={() => navigator.clipboard.writeText(pixQrCode)}
              className="flex-1 bg-green-600 hover:bg-green-700 text-white"
            >
              📋 Copiar Código PIX
            </Button>
            <Button
              onClick={() => setShowPixModal(false)}
              variant="outline"
            >
              Fechar
            </Button>
          </div>

          <div className="mt-4 text-center">
            <p className="text-xs text-gray-500 dark:text-gray-400">
              ⏱️ O pagamento será confirmado automaticamente
            </p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <BrazilianCustomerForm form={form} />

      <PaymentMethodTabs />

      <div className="pt-4">
        <Button
          onClick={handleSubmit}
          disabled={disabled || loading}
          loading={loading}
          fullWidth
          size="lg"
          className="bg-green-600 hover:bg-green-700 text-white"
        >
          {loading ? (loadingLabel || 'Processando...') : 'Confirmar Pagamento'}
        </Button>
      </div>

      <PixModal />
    </div>
  )
}

export default PagarmeCheckoutForm
