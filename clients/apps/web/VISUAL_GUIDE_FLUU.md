# Guia Visual - Landing Page Fluu

## 📐 Layout Completo

```
┌─────────────────────────────────────────┐
│         NAVEGAÇÃO (Fundo Preto)         │
│  Logo  Recursos  Comunidades  Preços    │
└─────────────────────────────────────────┘

┌─────────────────────────────────────────┐
│              HERO SECTION               │
│  Badge: Novo → Pagamentos brasileiros   │
│                                         │
│    Pagamentos brasileiros               │
│       simplificados.                    │
│                                         │
│  Ajudamos creators e negócios...        │
│                                         │
│         [<PERSON><PERSON><PERSON>]                   │
│                                         │
│  ┌────────┐  ┌────────┐  ┌────────┐   │
│  │Dashboard│  │  App   │  │ Cartão │   │
│  │         │  │ Mobile │  │  Fluu  │   │
│  └────────┘  └────────┘  └────────┘   │
└─────────────────────────────────────────┘

┌─────────────────────────────────────────┐
│         ESTATÍSTICAS - Stats            │
│    Sem assinatura obrigatória           │
│                                         │
│  R$ 1.958M    147.744    11.752M       │
│  Movimentado  Creators   Usuários      │
└─────────────────────────────────────────┘

┌─────────────────────────────────────────┐
│         FEATURES - 3 Cards              │
│                                         │
│  ┌──────────┐ ┌──────────┐ ┌─────────┐│
│  │ Cursos & │ │Comunidades││Pagamentos││
│  │ Coaching │ │  Pagas    ││Brasileiros│
│  │          │ │           ││          ││
│  │ PIX      │ │ Assinatura││ PIX ✓    ││
│  │ Cartão   │ │ Mensal    ││ R$ 297   ││
│  │ Boleto   │ │ Anual     ││ Taxa 2.7%││
│  └──────────┘ └──────────┘ └─────────┘│
└─────────────────────────────────────────┘

┌─────────────────────────────────────────┐
│         APPS FLUU - Grid 4x3            │
│  Construa seu produto com apps Fluu     │
│                                         │
│  [Chat]    [Lives]   [Fóruns]  [Cursos]│
│  [Rewards] [Arquivos][Agenda]  [Content]│
│  [Discord] [Telegram][Eventos]  ...    │
└─────────────────────────────────────────┘

┌─────────────────────────────────────────┐
│         USAGE - Tabs + Casos            │
│    Veja como escalar seu negócio        │
│                                         │
│  [Influenciador][Coach][Criador]       │
│                                         │
│  ┌──────────────────┬──────────────┐   │
│  │ Influenciador    │  // Código   │   │
│  │ Digital          │  exemplo PIX │   │
│  │                  │  Telegram    │   │
│  │ ✓ Grupos VIP     │  automação   │   │
│  │ ✓ Cursos         │              │   │
│  │ ✓ Mentorias      │              │   │
│  └──────────────────┴──────────────┘   │
└─────────────────────────────────────────┘

┌─────────────────────────────────────────┐
│      COMMUNITY HIGHLIGHT                │
│  Construa sua comunidade onde a         │
│  internet faz negócios no Brasil        │
│                                         │
│  [Feed] [Aprendizado] [Eventos] [Rank] │
│                                         │
│  ┌──────────────────────────────────┐  │
│  │ 👤 Lucas Santos  📌              │  │
│  │ Mentoria em Grupo ao Vivo!       │  │
│  │                                  │  │
│  │ Olá @todos - Adicionei novas...  │  │
│  │ Terça e Quinta: 20h (Brasília)   │  │
│  │                                  │  │
│  │ ❤️ 2   💬 0                      │  │
│  └──────────────────────────────────┘  │
└─────────────────────────────────────────┘

┌─────────────────────────────────────────┐
│           BENEFITS - Accordion          │
│  Todas as ferramentas que você precisa  │
│      para crescer, tudo em um app       │
│                                         │
│  ▼ Página de Loja                       │
│    Lance uma página de alta conversão   │
│                                         │
│  + Pagamentos                           │
│  + Afiliados                           │
│  + App Mobile                          │
│  + Compre Agora, Pague Depois          │
│  + Lutador de Disputas                 │
└─────────────────────────────────────────┘

┌─────────────────────────────────────────┐
│              PRICING                    │
│        Grátis para começar              │
│                                         │
│  ┌──────────────────────────────────┐  │
│  │     Plano Gratuito               │  │
│  │                                  │  │
│  │     2.7% + R$ 0,30               │  │
│  │     por transação                │  │
│  │                                  │  │
│  │  ✓ Pagamentos PIX, cartão, boleto│ │
│  │  ✓ Ofereça BNPL                 │  │
│  │  ✓ Hospede cursos, chats, lives │  │
│  │  ✓ Projete páginas de loja      │  │
│  │  ✓ Marketplace Fluu             │  │
│  │  ✓ Gerencie afiliados           │  │
│  │                                  │  │
│  │      [Começar Agora →]          │  │
│  └──────────────────────────────────┘  │
└─────────────────────────────────────────┘

┌─────────────────────────────────────────┐
│                FAQ                      │
│        Perguntas frequentes             │
│                                         │
│  ▼ O que posso vender na Fluu?         │
│    Você pode vender cursos, ebooks...   │
│                                         │
│  + Por que devo usar a Fluu?           │
│  + Como é diferente de outras...       │
│  + Como é diferente de redes...        │
│  + Desenvolvedores podem usar?         │
│  + A Fluu cobra taxa de assinatura?    │
│  + Como ajuda com distribuição?        │
└─────────────────────────────────────────┘

┌─────────────────────────────────────────┐
│              FOOTER                     │
│                                         │
│  Logo Fluu                              │
│  A plataforma completa para creators... │
│  © Fluu Digital                         │
│                                         │
│  [Recursos] [Empresa] [Comunidades]    │
│  [Suporte]                             │
└─────────────────────────────────────────┘
```

---

## 🎨 Paleta de Cores

```css
/* Cores Principais Fluu */
--fluu-green-light: #C3F53C;
--fluu-green-dark: #A8DC28;

/* Cores do Sistema */
--background-light: #F9FAFB;
--background-dark: #0A0A0A;
--card-light: #FFFFFF;
--card-dark: #18181B;

/* Textos */
--text-primary-light: #000000;
--text-primary-dark: #FFFFFF;
--text-secondary-light: #6B7280;
--text-secondary-dark: #9CA3AF;

/* Accent */
--success: #10B981;
--warning: #F59E0B;
--error: #EF4444;
```

---

## 📱 Componentes Detalhados

### 1. Hero Section

**Desktop:**
```
┌─────────────────────────────────────────────┐
│          [Novo] Pagamentos brasileiros →    │
│                                             │
│        Pagamentos brasileiros               │
│           simplificados.                    │
│                                             │
│  Ajudamos creators e negócios a receber     │
│  pagamentos no Brasil de forma segura       │
│                                             │
│            [Criar Conta]                    │
│                                             │
│    ┌─────────┐                             │
│    │Dashboard│  ┌──────┐                   │
│    │  Fluu   │  │ App  │   ┌────────┐     │
│    │         │  │Mobile│   │ Cartão │     │
│    └─────────┘  └──────┘   │  Fluu  │     │
│                             └────────┘     │
└─────────────────────────────────────────────┘
```

**Mobile:**
```
┌──────────────────┐
│ [Badge Verde]    │
│                  │
│  Pagamentos      │
│  brasileiros     │
│  simplificados.  │
│                  │
│  Ajudamos...     │
│                  │
│  [Criar Conta]   │
│                  │
│  Dashboard       │
│   + App          │
│   + Cartão       │
└──────────────────┘
```

---

### 2. Stats Card

```
┌─────────────────────────────────────────┐
│   Sem assinatura obrigatória            │
│                                         │
│  ┌──────────┐ ┌──────────┐ ┌─────────┐│
│  │R$ 1.958M │ │ 147.744  │ │11.752M  ││
│  │          │ │          │ │         ││
│  │Movimentado│ │Creators │ │Usuários ││
│  │por creators│ │na Fluu  │ │na Fluu ││
│  └──────────┘ └──────────┘ └─────────┘│
└─────────────────────────────────────────┘
```

---

### 3. Feature Card - Pagamentos

```
┌──────────────────────────────────┐
│  Pagamentos Brasileiros          │
│                                  │
│  Aceite PIX, cartão e boleto.    │
│  Sem taxas ocultas, sem          │
│  complicação.                    │
│                                  │
│  ┌──────────────────────────┐   │
│  │ Pagamento Recebido    ✓  │   │
│  ├──────────────────────────┤   │
│  │ PIX Instantâneo  R$ 297  │   │
│  │ Taxa Fluu (2.7%) R$ 8,02 │   │
│  ├──────────────────────────┤   │
│  │ Você Recebe   R$ 288,98  │   │
│  └──────────────────────────┘   │
└──────────────────────────────────┘
```

---

### 4. App Card (FluuApps)

```
┌────────────────┐
│   [Ícone Chat] │
│                │
│     Chat       │
│                │
│  Converse com  │
│  todos os      │
│  membros...    │
└────────────────┘
```

**Grid Completo (Desktop):**
```
┌───────┐ ┌───────┐ ┌───────┐ ┌───────┐
│ Chat  │ │ Lives │ │Fóruns │ │Cursos │
└───────┘ └───────┘ └───────┘ └───────┘
┌───────┐ ┌───────┐ ┌───────┐ ┌───────┐
│Rewards│ │Arquivo│ │Agenda │ │Content│
└───────┘ └───────┘ └───────┘ └───────┘
┌───────┐ ┌───────┐ ┌───────┐
│Discord│ │Telegram│ │Eventos│
└───────┘ └───────┘ └───────┘
```

---

### 5. Usage Tab

```
┌──────────────────────────────────────────┐
│  Veja como escalar seu negócio           │
│                                          │
│  [Influenciador][Coach][Criador]  [Ver+]│
└──────────────────────────────────────────┘

┌──────────────────────┬──────────────────┐
│ Influenciador Digital│ // Grupo VIP     │
│                      │ const grupoVIP = │
│ Monetize sua         │ {                │
│ audiência com grupos │   nome: "VIP",   │
│ VIP, cursos e        │   preco: 97.00   │
│ mentorias exclusivas │ };               │
│                      │                  │
│ ✓ Grupos VIP         │ function add() { │
│ ✓ Cursos ao vivo     │   telegram.add() │
│ ✓ Mentorias          │ }                │
│                      │                  │
│ [Saiba Mais →]      │                  │
└──────────────────────┴──────────────────┘
```

---

### 6. Community Post Preview

```
┌──────────────────────────────────────┐
│ 👤 Lucas Santos  📌                  │
│    Mentoria em Grupo ao Vivo!        │
│                                      │
│ há 3 dias • Discussão Geral          │
│                                      │
│ Olá @todos - Adicionei novas         │
│ mentorias em grupo ao calendário a   │
│ partir de amanhã. Serão às:          │
│ Terça e Quinta: 20h (horário de      │
│ Brasília). Vamos falar sobre...      │
│                                      │
├──────────────────────────────────────┤
│ ❤️ 2   💬 0                          │
└──────────────────────────────────────┘
```

---

### 7. Pricing Card

```
┌──────────────────────────────────────┐
│         Plano Gratuito               │
│                                      │
│          2.7%                        │
│     + R$ 0,30 por transação          │
│                                      │
│  ✓ Aceite pagamentos PIX, cartão     │
│    e boleto                          │
│  ✓ Ofereça BNPL (Buy Now, Pay Later)│
│  ✓ Hospede cursos, chats,            │
│    transmissões ao vivo e mais       │
│  ✓ Projete páginas de loja           │
│  ✓ Seja listado no Marketplace Fluu  │
│  ✓ Gerencie afiliados                │
│                                      │
│      [Começar Agora →]               │
└──────────────────────────────────────┘
```

---

### 8. FAQ Item

**Fechado:**
```
┌──────────────────────────────────────┐
│ O que posso vender na Fluu?      [+] │
└──────────────────────────────────────┘
```

**Aberto:**
```
┌──────────────────────────────────────┐
│ O que posso vender na Fluu?      [-] │
│                                      │
│ Você pode vender cursos, ebooks,     │
│ comunidades, softwares, serviços e   │
│ muito mais. A Fluu suporta           │
│ pagamentos PIX, cartão e boleto      │
│ para qualquer tipo de produto        │
│ digital.                             │
└──────────────────────────────────────┘
```

---

## 🎭 Estados de Hover

### Card Hover
```css
/* Normal */
transform: translateY(0);
box-shadow: 0 1px 3px rgba(0,0,0,0.1);

/* Hover */
transform: translateY(-4px);
box-shadow: 0 10px 20px rgba(0,0,0,0.1);
transition: all 0.3s ease;
```

### Button Hover
```css
/* Primary CTA */
background: linear-gradient(135deg, #C3F53C, #A8DC28);
transform: scale(1.02);

/* Secondary */
background: rgba(0,0,0,0.05);
```

---

## 📊 Responsividade

### Breakpoints
```css
/* Mobile */
@media (max-width: 767px) {
  grid-cols: 1;
  text: text-3xl;
  padding: px-4;
}

/* Tablet */
@media (min-width: 768px) and (max-width: 1023px) {
  grid-cols: 2;
  text: text-4xl;
  padding: px-8;
}

/* Desktop */
@media (min-width: 1024px) {
  grid-cols: 3-4;
  text: text-5xl;
  padding: px-16;
}
```

### Exemplo: Stats
```
Mobile:
┌──────────┐
│R$ 1.958M │
│Movimentado│
├──────────┤
│ 147.744  │
│ Creators │
├──────────┤
│ 11.752M  │
│ Usuários │
└──────────┘

Desktop:
┌──────────┬──────────┬─────────┐
│R$ 1.958M │ 147.744  │11.752M  │
│Movimentado│ Creators │Usuários │
└──────────┴──────────┴─────────┘
```

---

## ⚡ Animações

### Entrada de Seções
```javascript
// Stagger Children
containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
}

itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 }
}
```

### Timeline
```
0.0s: Hero fade in
0.2s: Stats fade in
0.4s: Feature 1
0.5s: Feature 2
0.6s: Feature 3
...
```

---

## 🔤 Tipografia

### Hierarquia
```
H1 (Hero):     text-5xl md:text-7xl  (48px → 72px)
H2 (Sections): text-3xl md:text-5xl  (30px → 48px)
H3 (Cards):    text-xl  md:text-2xl  (20px → 24px)
Body:          text-base md:text-lg  (16px → 18px)
Small:         text-sm   md:text-base(14px → 16px)
```

### Font Weights
```
Bold:     font-bold (700)    - Títulos
Semibold: font-semibold (600) - Subtítulos
Medium:   font-medium (500)   - Destaques
Normal:   font-normal (400)   - Corpo
```

---

## 🎯 Call-to-Actions

### Hierarchy
```
1. Primary:   "Criar Conta" (Hero)
2. Secondary: "Começar Agora" (Pricing)
3. Tertiary:  "Ver Mais", "Saiba Mais" (Cards)
```

### Estilos
```
Primary:
  bg-gradient verde-limão
  text-black
  rounded-full
  size-lg

Secondary:
  bg-white dark:bg-polar-800
  border border-gray-200
  rounded-full
  size-default

Tertiary:
  text-blue-500
  underline-offset-4
  hover:underline
```

---

## ✨ Resumo Visual

A landing page Fluu é caracterizada por:

✅ **Layout limpo e espaçoso** - Muito whitespace
✅ **Cards elevados** - Box shadows suaves
✅ **Verde-limão** - Cor de destaque consistente
✅ **Animações sutis** - Fade in e hover states
✅ **Tipografia clara** - Inter font, bem espaçada
✅ **Ícones ilustrativos** - Material Icons + Lucide
✅ **Exemplos reais** - Valores brasileiros, PIX
✅ **Mobile-first** - Responsivo em todos os breakpoints

**Tom Visual:** Profissional, moderno, confiável, brasileiro.

