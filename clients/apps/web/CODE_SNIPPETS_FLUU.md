# Code Snippets - Landing Page Fluu

## 📦 Componentes Criados

### 1. Stats Component

```typescript
// clients/apps/web/src/components/Landing/Stats.tsx
'use client'

import { motion } from 'framer-motion'

export const Stats = () => {
  const stats = [
    {
      value: 'R$ 1.958.761.593',
      description: 'Movimentado por creators na Fluu',
    },
    {
      value: '147.744',
      description: 'Creators na Fluu',
    },
    {
      value: '11.752.967',
      description: 'Usuários na Fluu',
    },
  ]

  return (
    <div className="flex w-full flex-col gap-y-12">
      <motion.h2
        className="text-center text-xl font-semibold md:text-2xl"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
      >
        Sem assinatura obrigatória
      </motion.h2>
      <motion.div
        className="grid w-full grid-cols-1 gap-8 md:grid-cols-3"
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
      >
        {stats.map((stat, index) => (
          <motion.div key={index} className="flex flex-col items-center gap-y-2">
            <span className="text-3xl font-bold md:text-5xl">
              {stat.value}
            </span>
            <span className="text-lg text-gray-500">
              {stat.description}
            </span>
          </motion.div>
        ))}
      </motion.div>
    </div>
  )
}
```

---

### 2. FluuApps Component

```typescript
// clients/apps/web/src/components/Landing/FluuApps.tsx
'use client'

import ChatOutlined from '@mui/icons-material/ChatOutlined'
import VideocamOutlined from '@mui/icons-material/VideocamOutlined'
import { motion } from 'framer-motion'

export const FluuApps = () => {
  const apps = [
    {
      icon: <ChatOutlined fontSize="inherit" />,
      name: 'Chat',
      description: 'Converse com todos os membros da sua comunidade.',
    },
    // ... mais apps
  ]

  return (
    <div className="flex w-full flex-col gap-y-12">
      <div className="flex flex-col items-center gap-y-6">
        <h1 className="text-3xl md:text-5xl">
          Construa seu produto com apps Fluu
        </h1>
        <p className="text-lg text-gray-500">
          Escolha entre milhares de apps...
        </p>
      </div>

      <motion.div
        className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"
        variants={containerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
      >
        {apps.map((app, index) => (
          <motion.div
            key={index}
            className="flex flex-col gap-y-3 rounded-2xl border bg-white p-6"
            variants={itemVariants}
          >
            <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-gray-100 text-2xl">
              {app.icon}
            </div>
            <div className="flex flex-col gap-y-1">
              <h3 className="font-semibold">{app.name}</h3>
              <p className="text-sm text-gray-500">{app.description}</p>
            </div>
          </motion.div>
        ))}
      </motion.div>
    </div>
  )
}
```

---

### 3. FAQ Component

```typescript
// clients/apps/web/src/components/Landing/FAQ.tsx
'use client'

import { motion } from 'framer-motion'
import { useState } from 'react'

interface FAQItemProps {
  question: string
  answer: string
  isOpen: boolean
  onClick: () => void
}

const FAQItem = ({ question, answer, isOpen, onClick }: FAQItemProps) => {
  return (
    <motion.button
      className="flex w-full flex-col items-start gap-y-3 border-b py-6 text-left"
      onClick={!isOpen ? onClick : undefined}
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
    >
      <div className="flex w-full items-center justify-between">
        <h3 className="text-lg font-semibold md:text-xl">
          {question}
        </h3>
        <span className="text-2xl">{isOpen ? '−' : '+'}</span>
      </div>
      {isOpen && (
        <motion.p
          className="pr-8 text-base leading-relaxed text-gray-600"
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
        >
          {answer}
        </motion.p>
      )}
    </motion.button>
  )
}

export const FAQ = () => {
  const [activeIndex, setActiveIndex] = useState<number | null>(0)

  const faqs = [
    {
      question: 'O que posso vender na Fluu?',
      answer: 'Você pode vender cursos, ebooks, comunidades...',
    },
    // ... mais FAQs
  ]

  return (
    <div className="flex w-full flex-col gap-y-12">
      <h1 className="text-center text-3xl md:text-5xl">
        Perguntas frequentes
      </h1>
      <div className="mx-auto w-full max-w-4xl">
        {faqs.map((faq, index) => (
          <FAQItem
            key={index}
            question={faq.question}
            answer={faq.answer}
            isOpen={activeIndex === index}
            onClick={() => setActiveIndex(index)}
          />
        ))}
      </div>
    </div>
  )
}
```

---

## 🎨 Padrões de Animação

### Container + Item Variants

```typescript
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      duration: 1,
    },
  },
}

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { 
    opacity: 1, 
    y: 0, 
    transition: { duration: 0.8 } 
  },
}

// Uso:
<motion.div
  variants={containerVariants}
  initial="hidden"
  whileInView="visible"
  viewport={{ once: true }}
>
  {items.map((item) => (
    <motion.div variants={itemVariants}>
      {/* conteúdo */}
    </motion.div>
  ))}
</motion.div>
```

---

### Fade In on Scroll

```typescript
<motion.div
  initial={{ opacity: 0, y: 20 }}
  whileInView={{ opacity: 1, y: 0 }}
  viewport={{ once: true }}
  transition={{ duration: 0.6 }}
>
  {/* conteúdo */}
</motion.div>
```

---

### Scale on Hover

```typescript
<motion.div
  whileHover={{ 
    scale: 1.02,
    translateY: -4,
    transition: { duration: 0.2 }
  }}
>
  {/* card */}
</motion.div>
```

---

## 🎯 Padrões de Layout

### Section Wrapper

```typescript
<Section className="flex flex-col gap-y-32 py-0 md:py-0">
  {/* componentes da seção */}
</Section>
```

---

### Centered Content

```typescript
<div className="flex flex-col items-center gap-y-6">
  <span className="text-lg text-gray-400">
    Seção Label
  </span>
  <h1 className="max-w-3xl text-center text-3xl md:text-5xl">
    Título da Seção
  </h1>
  <p className="max-w-3xl text-center text-lg text-gray-500">
    Descrição da seção
  </p>
</div>
```

---

### Responsive Grid

```typescript
// 1 coluna mobile, 2 tablet, 3/4 desktop
<div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
  {items.map((item) => (
    <div key={item.id}>
      {/* card */}
    </div>
  ))}
</div>
```

---

### Card Base

```typescript
<div className="
  dark:bg-polar-900 
  dark:border-polar-700 
  flex flex-col gap-y-3 
  rounded-2xl 
  border border-gray-200 
  bg-white 
  p-6 
  transition-transform 
  hover:translate-y-[-4px]
">
  {/* conteúdo do card */}
</div>
```

---

## 💰 Exemplo: Breakdown de Pagamento

```typescript
// Feature Card - Pagamentos Brasileiros
<div className="flex flex-col gap-y-2 rounded-lg border bg-gray-100 p-4">
  <div className="flex items-center justify-between">
    <span className="text-sm text-black">Pagamento Recebido</span>
    <span className="text-sm text-emerald-500">✓ Confirmado</span>
  </div>
  
  <div className="flex items-center justify-between border-t pt-2">
    <span className="text-sm text-gray-500">PIX Instantâneo</span>
    <span className="text-sm text-gray-500">R$ 297,00</span>
  </div>
  
  <div className="flex items-center justify-between">
    <span className="text-sm text-gray-500">Taxa Fluu (2.7%)</span>
    <span className="text-sm text-gray-500">R$ 8,02</span>
  </div>
  
  <div className="flex items-center justify-between border-t pt-2 font-semibold">
    <span className="text-sm text-black">Você Recebe</span>
    <span className="text-sm text-emerald-600">R$ 288,98</span>
  </div>
</div>
```

---

## 🎨 Exemplo: Pricing Card Completo

```typescript
<div className="flex w-full max-w-2xl flex-col gap-y-6 rounded-3xl border bg-white p-8 md:p-12">
  <div className="flex flex-col gap-y-4">
    <div className="flex flex-col gap-y-2">
      <h3 className="text-2xl font-bold text-black">
        Plano Gratuito
      </h3>
      <div className="flex items-baseline gap-x-2">
        <span className="text-4xl font-bold text-black">
          2.7%
        </span>
        <span className="text-lg text-gray-500">
          + R$ 0,30 por transação
        </span>
      </div>
    </div>

    <ul className="flex flex-col gap-y-3 pt-4">
      {features.map((feature, index) => (
        <li key={index} className="flex items-start gap-x-3">
          <svg className="mt-0.5 h-5 w-5 text-emerald-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
          <span className="text-gray-700">{feature}</span>
        </li>
      ))}
    </ul>
  </div>

  <Link href="/resources/pricing" className="w-full">
    <Button variant="default" className="w-full rounded-full" size="lg">
      <span>Começar Agora</span>
      <ArrowOutwardOutlined className="ml-2" />
    </Button>
  </Link>
</div>
```

---

## 📱 Exemplo: Community Post

```typescript
<div className="overflow-hidden rounded-2xl border bg-white">
  <Card className="border-0 shadow-none">
    <CardContent className="p-6">
      <div className="flex flex-col gap-4">
        {/* Header */}
        <div className="flex flex-row items-start justify-between">
          <div className="flex flex-row items-center gap-3">
            <div className="relative">
              <Avatar name="Lucas Santos" className="h-10 w-10" />
              <div className="absolute -bottom-1 -right-1 h-5 w-5 rounded-full bg-blue-500 flex items-center justify-center">
                <span className="text-xs text-white">3</span>
              </div>
            </div>
            <div className="flex flex-col">
              <div className="flex items-center gap-2">
                <span className="font-semibold">Lucas Santos</span>
                <Pin className="h-4 w-4 text-yellow-500" />
                <span className="font-medium">Mentoria em Grupo ao Vivo!</span>
              </div>
              <div className="flex items-center gap-2 text-sm text-gray-500">
                <span>há 3 dias</span>
                <span>•</span>
                <span>Discussão Geral</span>
              </div>
            </div>
          </div>
        </div>

        {/* Content */}
        <p className="text-gray-700 leading-relaxed">
          Olá @todos - Adicionei novas mentorias em grupo...
        </p>

        {/* Actions */}
        <div className="flex items-center gap-6 pt-2 border-t">
          <button className="flex items-center gap-2 text-gray-600 hover:text-blue-500">
            <Heart className="h-5 w-5" />
            <span>2</span>
          </button>
          <button className="flex items-center gap-2 text-gray-600 hover:text-blue-500">
            <MessageCircle className="h-5 w-5" />
            <span>0</span>
          </button>
        </div>
      </div>
    </CardContent>
  </Card>
</div>
```

---

## 🎭 Exemplo: Accordion Item

```typescript
<motion.button
  className={twMerge(
    'flex w-full flex-col items-start gap-y-1 py-4 text-left',
    isOpen ? 'cursor-default' : '',
  )}
  onClick={() => !isOpen && onClick(index)}
  variants={benefitItemVariants}
  initial="hidden"
  whileInView="visible"
  viewport={{ once: true }}
>
  <div className="flex w-full items-center justify-between text-lg">
    {title}
    <span className="text-2xl">{isOpen ? '-' : '+'}</span>
  </div>
  {isOpen && (
    <motion.p
      className="text-gray-500"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      {description}
    </motion.p>
  )}
</motion.button>
```

---

## 🎨 Tailwind Patterns

### Cores Dinâmicas (Light/Dark)

```typescript
// Texto
className="text-black dark:text-white"
className="text-gray-500 dark:text-polar-500"

// Backgrounds
className="bg-white dark:bg-polar-900"
className="bg-gray-100 dark:bg-polar-800"

// Borders
className="border-gray-200 dark:border-polar-700"
```

---

### Responsive Typography

```typescript
// Títulos
className="text-3xl md:text-5xl lg:text-7xl"

// Parágrafos
className="text-base md:text-lg lg:text-xl"

// Small text
className="text-sm md:text-base"
```

---

### Spacing Patterns

```typescript
// Gap
className="gap-y-6 md:gap-y-12"    // Vertical
className="gap-x-4 md:gap-x-8"     // Horizontal
className="gap-4 md:gap-8"         // Ambos

// Padding
className="p-4 md:p-8 lg:p-16"
className="px-6 py-4 md:px-12 md:py-8"

// Margin
className="mt-8 md:mt-16"
className="mb-4 md:mb-8"
```

---

### Rounded Corners

```typescript
// Small
className="rounded-lg"              // 8px

// Medium
className="rounded-xl"              // 12px

// Large
className="rounded-2xl"             // 16px

// Extra Large
className="rounded-3xl"             // 24px
className="rounded-4xl"             // 32px

// Full
className="rounded-full"            // 9999px
```

---

## 🔧 Utility Functions

### Format Currency (BRL)

```typescript
const formatCurrency = (value: number): string => {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL',
  }).format(value)
}

// Uso:
formatCurrency(297.00)  // "R$ 297,00"
formatCurrency(1958761593)  // "R$ 1.958.761.593,00"
```

---

### Format Number (Brazilian)

```typescript
const formatNumber = (value: number): string => {
  return new Intl.NumberFormat('pt-BR').format(value)
}

// Uso:
formatNumber(147744)      // "147.744"
formatNumber(11752967)    // "11.752.967"
```

---

### Relative Time (Portuguese)

```typescript
const getRelativeTime = (date: Date): string => {
  const rtf = new Intl.RelativeTimeFormat('pt-BR', { 
    numeric: 'auto' 
  })
  
  const days = Math.floor(
    (date.getTime() - Date.now()) / (1000 * 60 * 60 * 24)
  )
  
  return rtf.format(days, 'day')
}

// Uso:
getRelativeTime(new Date('2024-01-06'))  // "há 3 dias"
```

---

## 📦 Imports Necessários

```typescript
// Framer Motion
import { motion } from 'framer-motion'

// Material Icons
import ChatOutlined from '@mui/icons-material/ChatOutlined'
import VideocamOutlined from '@mui/icons-material/VideocamOutlined'
import ArrowOutwardOutlined from '@mui/icons-material/ArrowOutwardOutlined'

// Lucide Icons
import { Heart, MessageCircle, Pin } from 'lucide-react'

// Simple Icons
import { SiDiscord, SiTelegram } from '@icons-pack/react-simple-icons'

// Next.js
import Link from 'next/link'
import Image from 'next/image'

// React
import { useState } from 'react'

// Utils
import { twMerge } from 'tailwind-merge'

// UI Components (Polar)
import Button from '@polar-sh/ui/components/atoms/Button'
import Avatar from '@polar-sh/ui/components/atoms/Avatar'
import { Card, CardContent } from '@polar-sh/ui/components/atoms/Card'
```

---

## 🎯 TypeScript Types

```typescript
// Props básico
interface ComponentProps {
  className?: string
  children?: React.ReactNode
}

// Item com dados
interface StatItem {
  value: string
  description: string
}

interface AppItem {
  icon: React.ReactNode
  name: string
  description: string
}

interface FAQItem {
  question: string
  answer: string
}

// Props com handlers
interface AccordionItemProps {
  index: number
  title: string
  description: string
  isOpen: boolean
  onClick: (index: number) => void
}
```

---

## 🚀 Performance Tips

### 1. Lazy Loading Images
```typescript
<Image
  src="/path/to/image.jpg"
  alt="Description"
  loading="lazy"
  width={800}
  height={600}
  sizes="(max-width: 768px) 100vw, (max-width: 1280px) 75vw, 640px"
/>
```

### 2. Viewport-based Animations
```typescript
<motion.div
  initial="hidden"
  whileInView="visible"
  viewport={{ once: true, amount: 0.3 }}
>
  {/* conteúdo */}
</motion.div>
```

### 3. Memoization (quando necessário)
```typescript
import { useMemo } from 'react'

const ExpensiveComponent = () => {
  const expensiveValue = useMemo(() => {
    return computeExpensiveValue()
  }, [dependencies])
  
  return <div>{expensiveValue}</div>
}
```

---

## ✨ Conclusão

Estes snippets cobrem todos os padrões usados na landing page Fluu:

✅ **Componentes reutilizáveis** - Stats, Apps, FAQ
✅ **Animações consistentes** - Framer Motion patterns
✅ **Layout responsivo** - Mobile-first approach
✅ **Tipografia escalável** - Responsive text sizes
✅ **Temas light/dark** - Suporte completo
✅ **Localização BR** - Formato de moeda e datas
✅ **Performance** - Lazy loading e viewport optimization

Use estes padrões para manter consistência ao adicionar novos componentes!

