'use client'

import { HomeHeroV2 } from '@/components/Landing/Hero/HomeHeroV2'
import { MerchantOfRecord } from '@/components/Landing/MOR'
import { Testimonials } from '@/components/Landing/Testimonials'
import Avatar from '@polar-sh/ui/components/atoms/Avatar'
import Button from '@polar-sh/ui/components/atoms/Button'
import Image from 'next/image'
import Link from 'next/link'
import GetStartedButton from '../Auth/GetStartedButton'
import { Adapters } from './Adapters'
import { Benefits } from './Benefits'
import { Checkout } from './Checkout'
import { CommunityHighlight } from './CommunityHighlight'
import { Events } from './Events'
import { FAQ } from './FAQ'
import Features from './Features'
import { FluuApps } from './FluuApps'
import { Pricing } from './Pricing'
import SDKs from './SDKs'
import { Section } from './Section'
import { SeedRound } from './SeedRound'
import { Stats } from './Stats'
import { Usage } from './Usage'

export default function Page() {
  return (
    <div className="flex flex-col">
      <PageContent />
    </div>
  )
}

export const PageContent = () => {
  return (
    <>
      <Section className="flex flex-col gap-y-32 py-0 md:py-0">
        <HomeHeroV2
          title="Use PIX. Economize em taxas. Aumente conversão."
          description="Ajudamos creators e negócios a receber pagamentos no Brasil de forma segura, sem taxas bancárias ocultas."
        >
          <GetStartedButton size="lg" text="Criar Conta" />
        </HomeHeroV2>
        <Stats />
        <Features />
        <FluuApps />
        <Usage />
      </Section>
      <Section className="flex flex-col gap-y-24">
        <CommunityHighlight />
        <Benefits />
      </Section>
      <Section className="flex flex-col gap-y-24">
        <Pricing />
        <FAQ />
      </Section>
    </>
  )
}
