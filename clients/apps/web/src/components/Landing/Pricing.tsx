import ArrowOutwardOutlined from '@mui/icons-material/ArrowOutwardOutlined'
import Button from '@polar-sh/ui/components/atoms/Button'
import Link from 'next/link'

export const Pricing = () => {
  return (
    <div id="pricing" className="flex w-full flex-col gap-y-12">
      <div className="flex flex-col items-center gap-y-8">
        <span className="dark:text-polar-500 text-lg text-gray-400">
          Preços Fluu
        </span>
        <h1 className="w-fit max-w-3xl pt-2 text-center text-3xl text-pretty md:text-5xl md:leading-normal">
          G<PERSON><PERSON><PERSON> para começar
        </h1>
        
        <div className="dark:bg-polar-900 dark:border-polar-700 flex w-full max-w-2xl flex-col gap-y-6 rounded-3xl border border-gray-200 bg-white p-8 md:p-12">
          <div className="flex flex-col gap-y-4">
            <div className="flex flex-col gap-y-2">
              <h3 className="text-2xl font-bold text-black dark:text-white">
                Plano Gratuito
              </h3>
              <div className="flex items-baseline gap-x-2">
                <span className="text-4xl font-bold text-black dark:text-white">
                  2.7%
                </span>
                <span className="dark:text-polar-500 text-lg text-gray-500">
                  + R$ 0,30 por transação
                </span>
              </div>
            </div>

            <ul className="flex flex-col gap-y-3 pt-4">
              {[
                'Aceite pagamentos PIX, cartão e boleto',
                'Ofereça BNPL (Buy Now, Pay Later)',
                'Hospede cursos, chats, transmissões ao vivo e mais',
                'Projete páginas de loja',
                'Seja listado no Marketplace Fluu',
                'Gerencie afiliados',
              ].map((feature, index) => (
                <li key={index} className="flex items-start gap-x-3">
                  <svg
                    className="mt-0.5 h-5 w-5 flex-shrink-0 text-emerald-500"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                  <span className="dark:text-polar-300 text-gray-700">
                    {feature}
                  </span>
                </li>
              ))}
            </ul>
          </div>

          <Link href="/resources/pricing" target="_blank" className="w-full">
            <Button variant="default" className="w-full rounded-full" size="lg">
              <span>Começar Agora</span>
              <ArrowOutwardOutlined className="ml-2" />
            </Button>
          </Link>
        </div>

        <Link href="/resources/pricing" target="_blank">
          <Button variant="secondary" className="rounded-full">
            <span>Ver Todos os Benefícios</span>
            <ArrowOutwardOutlined className="ml-2" />
          </Button>
        </Link>
      </div>
    </div>
  )
}
