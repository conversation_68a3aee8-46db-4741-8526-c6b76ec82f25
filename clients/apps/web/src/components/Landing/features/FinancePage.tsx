'use client'

import GetStartedButton from '@/components/Auth/GetStartedButton'
import AccountBalanceOutlined from '@mui/icons-material/AccountBalanceOutlined'
import ArrowOutwardOutlined from '@mui/icons-material/ArrowOutwardOutlined'
import CheckOutlined from '@mui/icons-material/CheckOutlined'
import PaymentsOutlined from '@mui/icons-material/PaymentsOutlined'
import ReceiptLongOutlined from '@mui/icons-material/ReceiptLongOutlined'
import Button from '@polar-sh/ui/components/atoms/Button'
import { motion } from 'framer-motion'
import Image from 'next/image'
import Link from 'next/link'
import { Hero } from '../Hero/Hero'
import { Section } from '../Section'

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
}

const itemVariants = {
  hidden: { opacity: 0 },
  visible: { opacity: 1, transition: { duration: 1 } },
}

export const FinancePage = () => {
  return (
    <div className="flex flex-col">
      <Section className="flex flex-col gap-y-32 pt-0 md:pt-0">
        <Hero
          title="PIX Automático, Cartão e Boleto para o Brasil"
          description="Receba pagamentos PIX em tempo real, cartão com parcelamento e boleto bancário. Entrega automática após cada pagamento aprovado."
        >
          <GetStartedButton size="lg" text="Começar Agora" />
          <Link href="/features/finance">
            <Button variant="secondary" className="rounded-full" size="lg">
              Ver Documentação
              <ArrowOutwardOutlined className="ml-2" />
            </Button>
          </Link>
        </Hero>

        <motion.div
          className="dark:bg-polar-900 flex w-full flex-col overflow-hidden rounded-2xl bg-white md:flex-row-reverse md:items-stretch"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={containerVariants}
        >
          <div className="flex flex-1 grow flex-col gap-y-10 p-8 md:p-16">
            <div className="flex flex-col gap-y-4">
              <div className="flex items-center gap-x-3">
                <h2 className="text-2xl leading-normal! md:text-3xl">
                  Relatórios Financeiros Transparentes
                </h2>
              </div>
              <p className="dark:text-polar-500 text-lg text-gray-500">
                Visibilidade completa da sua receita, recebimentos e impostos. Acompanhe cada transação PIX, cartão e boleto com relatórios financeiros detalhados.
              </p>
            </div>
            <motion.ul
              className="dark:divide-polar-700 dark:border-polar-700 flex flex-col divide-y divide-gray-200 border-y border-gray-200"
              variants={containerVariants}
            >
              {[
                'Recebimento instantâneo via PIX',
                'Parcelamento automático no cartão',
                'Alta conversão com boleto bancário',
                'Entrega automática após pagamento aprovado',
              ].map((item, i) => (
                <motion.li
                  key={i}
                  className="flex items-start gap-x-3 py-2"
                  variants={itemVariants}
                >
                  <CheckOutlined
                    className="mt-0.5 text-emerald-500"
                    fontSize="small"
                  />
                  <span>{item}</span>
                </motion.li>
              ))}
            </motion.ul>
          </div>
          <div className="dark:bg-polar-800 relative flex flex-1 items-center justify-center p-8 md:p-16">
            <motion.div
              className="dark:bg-polar-900 dark:border-polar-700 z-10 flex w-full max-w-xs flex-col gap-y-4 rounded-lg border border-gray-200 bg-white p-6 shadow-sm"
              variants={itemVariants}
            >
              <div className="flex flex-row items-center justify-between gap-x-2">
                <span className="text-sm font-medium text-black dark:text-white">
                  Resumo de Recebimentos
                </span>
                <AccountBalanceOutlined
                  className="text-emerald-500"
                  fontSize="small"
                />
              </div>
              <div className="flex flex-col gap-y-1">
                <span className="text-3xl text-black dark:text-white">
                  R$ 12.450
                </span>
                <span className="dark:text-polar-500 text-sm text-gray-500">
                  Disponível para saque
                </span>
              </div>
              <div className="dark:border-polar-700 flex items-center justify-between border-t border-gray-200 pt-4">
                <div className="flex flex-col">
                  <span className="dark:text-polar-500 text-xs text-gray-500">
                    Receita
                  </span>
                  <span className="font-medium text-black dark:text-white">
                    R$ 15.000
                  </span>
                </div>
                <div className="flex flex-col items-end">
                  <span className="dark:text-polar-500 text-xs text-gray-500">
                    Taxas
                  </span>
                  <span className="font-medium text-black dark:text-white">
                    R$ 602
                  </span>
                </div>
              </div>
            </motion.div>
            <Image
              src="/assets/landing/abstract_07.jpg"
              alt="Finance"
              className="absolute inset-0 h-full w-full object-cover"
              width={500}
              height={500}
            />
          </div>
        </motion.div>

        <div className="flex flex-col gap-y-12">
          <div className="flex flex-col items-center gap-y-4 text-center">
            <h2 className="text-3xl font-bold md:text-4xl">
              3 Métodos de Pagamento Brasileiros
            </h2>
            <p className="dark:text-polar-500 max-w-2xl text-lg text-gray-500">
              PIX tem 40% mais conversão que cartão. Ofereça todos os métodos que seus clientes preferem.
            </p>
          </div>
          <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
            {[
              {
                icon: <PaymentsOutlined fontSize="large" />,
                title: 'PIX Automático',
                description:
                  'Receba pagamentos PIX em tempo real e entregue produtos automaticamente em segundos. Sem intervenção manual.',
              },
              {
                icon: <ReceiptLongOutlined fontSize="large" />,
                title: 'Cartão com Parcelamento',
                description:
                  'Aceite cartão de crédito com parcelamento automático e entrega imediata após aprovação.',
              },
              {
                icon: <AccountBalanceOutlined fontSize="large" />,
                title: 'Boleto Bancário',
                description:
                  'Alta conversão com boleto. Entrega automática após compensação bancária confirmada.',
              },
            ].map((feature, i) => (
              <motion.div
                key={i}
                className="dark:bg-polar-900 flex flex-col items-center gap-y-8 rounded-xl bg-white px-6 py-12 text-center"
                initial="hidden"
                whileInView="visible"
                viewport={{ once: true }}
                variants={itemVariants}
              >
                <div className="flex flex-row gap-x-2 text-blue-600 dark:text-blue-400">{feature.icon}</div>
                <div className="flex flex-col gap-y-4">
                  <h3 className="text-2xl font-semibold">{feature.title}</h3>
                  <p className="dark:text-polar-400 text-balance text-gray-600">
                    {feature.description}
                  </p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </Section>

      <Section className="flex flex-col gap-y-24">
        <motion.div
          className="flex flex-col items-center gap-y-8 text-center"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={containerVariants}
        >
          <motion.h2 className="text-2xl md:text-3xl" variants={itemVariants}>
            Pronto para receber com PIX, cartão e boleto?
          </motion.h2>
          <motion.p
            className="dark:text-polar-500 text-lg text-gray-500 md:w-[480px]"
            variants={itemVariants}
          >
            Junte-se a criadores usando a Fluu para receber pagamentos e entregar produtos automaticamente.
          </motion.p>
          <motion.div variants={itemVariants}>
            <GetStartedButton size="lg" text="Começar Agora" />
          </motion.div>
        </motion.div>
      </Section>
    </div>
  )
}
