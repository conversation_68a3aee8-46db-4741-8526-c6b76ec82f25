'use client'

import { motion } from 'framer-motion'
import { PropsWithChildren } from 'react'
import { twMerge } from 'tailwind-merge'
import Image from 'next/image'

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
}

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { 
    opacity: 1, 
    y: 0,
    transition: { 
      duration: 0.5,
      ease: [0.22, 1, 0.36, 1] as const
    } 
  },
}

export type HomeHeroProps = PropsWithChildren<{
  className?: string
  title: string
  description: string
}>

export const HomeHero = ({
  className,
  title,
  description,
  children,
}: HomeHeroProps) => {

  return (
    <div className="relative overflow-hidden">
      {/* Hero Section */}
      <div className="relative flex flex-col items-center justify-center px-4 py-20 md:py-20">
        <div className="mx-auto w-full lg:max-w-7xl">
          <div className="grid grid-cols-1 gap-12 md:grid-cols-2 md:items-center md:gap-16">
            {/* Left Side - Content */}
            <motion.div
              className={twMerge(
                'relative flex w-full flex-col gap-6',
                className,
              )}
              variants={containerVariants}
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
            >
              {/* Badge */}
              <motion.div
                className="w-fit"
                variants={itemVariants}
              >
                <div className="inline-flex items-center gap-2 rounded-2xl bg-green-100 px-4 py-2 dark:bg-green-900/30">
                  <span className="text-sm font-medium text-green-800 dark:text-green-300">
                    Novo
                  </span>
                  <span className="text-sm text-green-700 dark:text-green-400">
                    Pagamentos brasileiros otimizados
                  </span>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="text-green-700 dark:text-green-400"
                  >
                    <line x1="5" y1="12" x2="19" y2="12"></line>
                    <polyline points="12 5 19 12 12 19"></polyline>
                  </svg>
                </div>
              </motion.div>

              <motion.h1
                className="text-4xl font-bold leading-tight tracking-tight text-balance md:text-6xl"
                variants={itemVariants}
              >
                <span className="text-gray-900 dark:text-white">
                  Pagamentos brasileiros{' '}
                </span>
                <span className="text-primary">simplificados.</span>
              </motion.h1>
              
              <motion.p
                className="max-w-xl text-lg leading-relaxed text-balance text-gray-600 dark:text-gray-300 md:text-xl"
                variants={itemVariants}
              >
                {description}
              </motion.p>

              <motion.div
                className="flex flex-col items-start gap-4 sm:flex-row sm:items-center"
                variants={itemVariants}
              >
                {children}
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Plataforma <span className="font-semibold text-gray-900 dark:text-white">100% Brasileira</span>
                </p>
              </motion.div>
            </motion.div>

            {/* Right Side - Mockups */}
            <motion.div
              className="relative flex min-h-[400px] items-center justify-center md:min-h-[500px]"
              variants={itemVariants}
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
            >
              <div className="relative flex items-center justify-end w-full h-full pr-0 md:pr-4 lg:pr-8">
                {/* Card Mockup - Below phone, closer */}
                <motion.div
                  className="absolute z-0 right-8 md:right-12 lg:right-16 top-[calc(50%+140px)] md:top-[calc(50%+220px)]"
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                >
                  <div className="relative h-[130px] w-[204px] rounded-2xl shadow-2xl md:h-[200px] md:w-[320px]">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 272 173"
                      className="h-full w-full"
                      preserveAspectRatio="none"
                    >
                      <defs>
                        <linearGradient id="cardGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                          <stop offset="0%" stopColor="rgb(195, 245, 60)" />
                          <stop offset="100%" stopColor="rgb(168, 220, 40)" />
                        </linearGradient>
                      </defs>
                      <rect
                        width="272"
                        height="173"
                        rx="16"
                        fill="url(#cardGradient)"
                      />
                      {/* Card Chip */}
                      <rect
                        x="20"
                        y="30"
                        width="32"
                        height="24"
                        rx="4"
                        fill="rgba(0, 44, 21, 0.2)"
                      />
                      {/* Card Number */}
                      <text
                        x="20"
                        y="90"
                        fontSize="20"
                        fontWeight="600"
                        fill="rgb(0, 44, 21)"
                        fontFamily="Inter, sans-serif"
                        letterSpacing="2"
                      >
                        •••• •••• •••• 1234
                      </text>
                      {/* Card Holder */}
                      <text
                        x="20"
                        y="120"
                        fontSize="14"
                        fill="rgba(0, 44, 21, 0.7)"
                        fontFamily="Inter, sans-serif"
                      >
                        NOME DO PORTADOR
                      </text>
                      {/* Card Expiry */}
                      <text
                        x="20"
                        y="145"
                        fontSize="14"
                        fill="rgba(0, 44, 21, 0.7)"
                        fontFamily="Inter, sans-serif"
                      >
                        12/25
                      </text>
                      {/* Card Logo/Brand */}
                      <circle
                        cx="240"
                        cy="40"
                        r="20"
                        fill="rgba(0, 44, 21, 0.1)"
                      />
                    </svg>
                  </div>
                </motion.div>

                {/* Phone Mockup - On top, right aligned */}
                <motion.div
                  className="relative z-10 flex items-center justify-end"
                  initial={{ opacity: 0, x: 50 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: 0.4 }}
                >
                  <div className="relative h-[300px] w-[165px] md:h-[500px] md:w-[280px]">
                    <Image
                      src="https://framerusercontent.com/images/LzLdm5sQTXI8LvWrlOMzdr0c8c.png?scale-down-to=2048"
                      alt="App mobile"
                      fill
                      className="object-contain"
                      priority
                    />
                  </div>
                </motion.div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Dashboard Image Section */}
      <motion.div
        className="relative w-full overflow-hidden"
        variants={itemVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
      >
        <div className="mx-auto w-full lg:max-w-7xl px-4 pb-20 md:pb-32">
          <div className="overflow-hidden rounded-2xl border border-gray-800 bg-gray-900 shadow-2xl">
            <picture className="flex h-full w-full">
              <source
                media="(prefers-color-scheme: dark)"
                srcSet="/assets/landing/transactions_dark.png"
              />
              <Image
                className="h-full w-full object-cover"
                src="/assets/landing/transactions_light.png"
                alt="Dashboard"
                width={1400}
                height={800}
                priority
              />
            </picture>
          </div>
        </div>
      </motion.div>
    </div>
  )
}

