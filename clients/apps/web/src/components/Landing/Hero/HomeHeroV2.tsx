'use client'

import { motion } from 'framer-motion'
import { PropsWithChildren } from 'react'
import { twMerge } from 'tailwind-merge'
import Image from 'next/image'

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
}

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { 
    opacity: 1, 
    y: 0,
    transition: { 
      duration: 0.5,
      ease: [0.22, 1, 0.36, 1] as const
    } 
  },
}

export type HomeHeroV2Props = PropsWithChildren<{
  className?: string
  title: string
  description: string
}>

export const HomeHeroV2 = ({
  className,
  title,
  description,
  children,
}: HomeHeroV2Props) => {

  return (
    <div className={twMerge('relative overflow-hidden', className)}>
      {/* Hero Section - Centered Layout */}
      <div className="relative flex flex-col items-center justify-center px-4 py-12 md:py-20">
        <div className="mx-auto w-full lg:max-w-7xl">
          {/* Content Section - Centered */}
          <motion.div
            className="relative flex w-full flex-col items-center gap-6 text-center"
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
          >
            {/* Badge */}
            <motion.div
              className="w-fit"
              variants={itemVariants}
            >
              <div className="inline-flex items-center gap-2 rounded-2xl bg-green-100 px-4 py-2 dark:bg-green-900/30">
                <span className="text-sm font-medium text-green-800 dark:text-green-300">
                  Novo
                </span>
                <span className="text-sm text-green-700 dark:text-green-400">
                  Pagamentos brasileiros otimizados →
                </span>
              </div>
            </motion.div>

            {/* Title */}
            <motion.h1
              className="text-4xl font-bold leading-tight tracking-tight text-balance md:text-6xl lg:text-7xl"
              variants={itemVariants}
            >
              <span className="text-gray-900 dark:text-white">
                {title}
              </span>
            </motion.h1>
            
            {/* Description */}
            <motion.p
              className="max-w-3xl text-lg leading-relaxed text-balance text-gray-600 dark:text-gray-300 md:text-xl"
              variants={itemVariants}
            >
              {description}
            </motion.p>

            {/* CTA Buttons */}
            <motion.div
              className="mt-6 flex flex-col items-center gap-4 sm:flex-row sm:justify-center"
              variants={itemVariants}
            >
              {children}
            </motion.div>
          </motion.div>

          {/* Visual Showcase Section - 3 Mockups Overlapping */}
          <motion.div
            className="relative mt-16 flex min-h-[500px] items-center justify-center md:mt-24 md:min-h-[700px]"
            variants={itemVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
          >
            <div className="relative flex items-center justify-center w-full h-full">
              {/* Desktop Dashboard - Central, Largest, Slightly Tilted */}
              <motion.div
                className="relative z-0"
                initial={{ opacity: 0, scale: 0.9 }}
                whileInView={{ opacity: 1, scale: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8, delay: 0.2 }}
                style={{
                  transform: 'rotate(-2deg)',
                }}
              >
                <div className="relative w-full max-w-6xl rounded-2xl border-2 border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 shadow-2xl overflow-hidden">
                  <Image
                    src="https://framerusercontent.com/images/Q8eKPMebxqVmUw14plKDEAF1yo.png?scale-down-to=1024"
                    alt="Dashboard"
                    width={1400}
                    height={900}
                    className="w-full h-auto object-contain"
                    priority
                  />
                </div>
              </motion.div>

              {/* Smartphone - Overlapping Right Side */}
              <motion.div
                className="absolute z-10"
                style={{
                  right: '5%',
                  top: '10%',
                  transform: 'rotate(8deg)',
                }}
                initial={{ opacity: 0, x: 50, y: -50 }}
                whileInView={{ opacity: 1, x: 0, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8, delay: 0.4 }}
              >
                <div className="relative h-[400px] w-[220px] md:h-[500px] md:w-[280px] drop-shadow-2xl">
                  <Image
                    src="https://framerusercontent.com/images/LzLdm5sQTXI8LvWrlOMzdr0c8c.png?scale-down-to=2048"
                    alt="App mobile"
                    fill
                    className="object-contain"
                    priority
                  />
                </div>
              </motion.div>

              {/* Payment Card - Overlapping Bottom Left */}
              <motion.div
                className="absolute z-20"
                style={{
                  left: '5%',
                  bottom: '10%',
                  transform: 'rotate(-12deg)',
                }}
                initial={{ opacity: 0, scale: 0.8, y: 20 }}
                whileInView={{ opacity: 1, scale: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8, delay: 0.6 }}
              >
                <div className="relative h-[130px] w-[204px] rounded-2xl shadow-2xl md:h-[200px] md:w-[320px]">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 272 173"
                    className="h-full w-full"
                    preserveAspectRatio="none"
                  >
                    <defs>
                      <linearGradient id="cardGradientV2" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" stopColor="rgb(195, 245, 60)" />
                        <stop offset="100%" stopColor="rgb(168, 220, 40)" />
                      </linearGradient>
                    </defs>
                    <rect
                      width="272"
                      height="173"
                      rx="16"
                      fill="url(#cardGradientV2)"
                    />
                    {/* Card Chip */}
                    <rect
                      x="20"
                      y="30"
                      width="32"
                      height="24"
                      rx="4"
                      fill="rgba(0, 44, 21, 0.2)"
                    />
                    {/* Card Number */}
                    <text
                      x="20"
                      y="90"
                      fontSize="20"
                      fontWeight="600"
                      fill="rgb(0, 44, 21)"
                      fontFamily="Inter, sans-serif"
                      letterSpacing="2"
                    >
                      •••• •••• •••• 1234
                    </text>
                    {/* Card Holder */}
                    <text
                      x="20"
                      y="120"
                      fontSize="14"
                      fill="rgba(0, 44, 21, 0.7)"
                      fontFamily="Inter, sans-serif"
                    >
                      NOME DO PORTADOR
                    </text>
                    {/* Card Expiry */}
                    <text
                      x="20"
                      y="145"
                      fontSize="14"
                      fill="rgba(0, 44, 21, 0.7)"
                      fontFamily="Inter, sans-serif"
                    >
                      12/25
                    </text>
                    {/* Card Logo/Brand */}
                    <circle
                      cx="240"
                      cy="40"
                      r="20"
                      fill="rgba(0, 44, 21, 0.1)"
                    />
                  </svg>
                </div>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  )
}

