'use client'

import { motion } from 'framer-motion'

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      duration: 1,
    },
  },
}

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.8 } },
}

export const Stats = () => {
  const stats = [
    {
      value: 'R$ 1.958.761.593',
      description: 'Movimentado por creators na Fluu',
    },
    {
      value: '147.744',
      description: 'Creators na Fluu',
    },
    {
      value: '11.752.967',
      description: 'Usu<PERSON>rios na Fluu',
    },
  ]

  return (
    <div className="flex w-full flex-col gap-y-12">
      <div className="flex flex-col items-center gap-y-6">
        <motion.h2
          className="text-center text-xl font-semibold md:text-2xl"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          Sem assinatura obrigatória
        </motion.h2>
        <motion.div
          className="grid w-full grid-cols-1 gap-8 md:grid-cols-3 md:gap-12"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          {stats.map((stat, index) => (
            <motion.div
              key={index}
              className="flex flex-col items-center gap-y-2 text-center"
              variants={itemVariants}
            >
              <span className="text-3xl font-bold text-black dark:text-white md:text-3xl">
                {stat.value}
              </span>
              <span className="dark:text-polar-500 max-w-xs text-lg text-gray-500">
                {stat.description}
              </span>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </div>
  )
}

