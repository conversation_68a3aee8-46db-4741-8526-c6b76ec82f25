'use client'

import ChatOutlined from '@mui/icons-material/ChatOutlined'
import VideocamOutlined from '@mui/icons-material/VideocamOutlined'
import ForumOutlined from '@mui/icons-material/ForumOutlined'
import SchoolOutlined from '@mui/icons-material/SchoolOutlined'
import CardGiftcardOutlined from '@mui/icons-material/CardGiftcardOutlined'
import FolderOutlined from '@mui/icons-material/FolderOutlined'
import CalendarTodayOutlined from '@mui/icons-material/CalendarTodayOutlined'
import ArticleOutlined from '@mui/icons-material/ArticleOutlined'
import EventNoteOutlined from '@mui/icons-material/EventNoteOutlined'
import GroupsOutlined from '@mui/icons-material/GroupsOutlined'
import SendOutlined from '@mui/icons-material/SendOutlined'
import { motion } from 'framer-motion'

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.05,
      duration: 1,
    },
  },
}

const itemVariants = {
  hidden: { opacity: 0, scale: 0.9 },
  visible: { opacity: 1, scale: 1, transition: { duration: 0.5 } },
}

export const FluuApps = () => {
  const apps = [
    {
      icon: <ChatOutlined fontSize="inherit" />,
      name: 'Chat',
      description: 'Converse com todos os membros da sua comunidade em um chat em grupo ao vivo.',
    },
    {
      icon: <VideocamOutlined fontSize="inherit" />,
      name: 'Transmissões ao Vivo',
      description: 'Faça transmissões ao vivo para sua comunidade.',
    },
    {
      icon: <ForumOutlined fontSize="inherit" />,
      name: 'Fóruns',
      description: 'Crie fóruns de discussão para sua comunidade.',
    },
    {
      icon: <SchoolOutlined fontSize="inherit" />,
      name: 'Cursos',
      description: 'Organize e venda cursos online com conteúdo estruturado.',
    },
    {
      icon: <CardGiftcardOutlined fontSize="inherit" />,
      name: 'Recompensas de Conteúdo',
      description: 'Recompense membros com conteúdo exclusivo.',
    },
    {
      icon: <FolderOutlined fontSize="inherit" />,
      name: 'Arquivos',
      description: 'Compartilhe arquivos e documentos com sua comunidade.',
    },
    {
      icon: <CalendarTodayOutlined fontSize="inherit" />,
      name: 'Agendamento',
      description: 'Permita que clientes agendem consultas e sessões.',
    },
    {
      icon: <ArticleOutlined fontSize="inherit" />,
      name: 'Conteúdo',
      description: 'Organize e compartilhe conteúdo exclusivo.',
    },
    {
      icon: <GroupsOutlined fontSize="inherit" />,
      name: 'Discord',
      description: 'Integre com Discord para comunidades maiores.',
    },
    {
      icon: <SendOutlined fontSize="inherit" />,
      name: 'Telegram',
      description: 'Integre com Telegram para comunicação instantânea.',
    },
    {
      icon: <EventNoteOutlined fontSize="inherit" />,
      name: 'Eventos',
      description: 'Crie e gerencie eventos para sua comunidade.',
    },
  ]

  return (
    <div className="flex w-full flex-col gap-y-12">
      <div className="flex flex-col items-center gap-y-6">
        <span className="dark:text-polar-500 text-lg text-gray-400">
          Apps Disponíveis
        </span>
        <h1 className="w-fit max-w-3xl text-center text-3xl text-pretty md:text-5xl md:leading-normal">
          Construa seu produto com apps Fluu
        </h1>
        <p className="dark:text-polar-400 max-w-3xl text-center text-lg text-gray-500">
          Escolha entre milhares de apps, como chat, cursos, transmissões ao vivo e jogos — e instantaneamente dê aos seus clientes uma base completa.
        </p>
      </div>

      <motion.div
        className="grid w-full grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"
        variants={containerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
      >
        {apps.map((app, index) => (
          <motion.div
            key={index}
            className="dark:bg-polar-900 dark:border-polar-700 flex flex-col gap-y-3 rounded-2xl border border-gray-200 bg-white p-6 transition-transform hover:translate-y-[-4px]"
            variants={itemVariants}
          >
            <div className="dark:bg-polar-800 flex h-12 w-12 items-center justify-center rounded-lg bg-gray-100 text-2xl text-blue-500">
              {app.icon}
            </div>
            <div className="flex flex-col gap-y-1">
              <h3 className="font-semibold text-black dark:text-white">
                {app.name}
              </h3>
              <p className="dark:text-polar-500 text-sm text-gray-500">
                {app.description}
              </p>
            </div>
          </motion.div>
        ))}
      </motion.div>
    </div>
  )
}

