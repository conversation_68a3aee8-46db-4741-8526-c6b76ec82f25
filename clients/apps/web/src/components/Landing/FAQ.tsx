'use client'

import { motion } from 'framer-motion'
import { useState } from 'react'
import { twMerge } from 'tailwind-merge'

interface FAQItemProps {
  question: string
  answer: string
  isOpen: boolean
  onClick: () => void
}

const FAQItem = ({ question, answer, isOpen, onClick }: FAQItemProps) => {
  return (
    <motion.button
      className={twMerge(
        'flex w-full flex-col items-start gap-y-3 border-b border-gray-200 py-6 text-left dark:border-polar-700',
        isOpen ? 'cursor-default' : '',
      )}
      onClick={!isOpen ? onClick : undefined}
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5 }}
    >
      <div className="flex w-full flex-row items-center justify-between">
        <h3 className="text-lg font-semibold text-black dark:text-white md:text-xl">
          {question}
        </h3>
        <span className="text-2xl text-gray-500 dark:text-polar-400">
          {isOpen ? '−' : '+'}
        </span>
      </div>
      {isOpen && (
        <motion.p
          className="dark:text-polar-400 pr-8 text-base leading-relaxed text-gray-600 md:text-lg"
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          transition={{ duration: 0.3 }}
        >
          {answer}
        </motion.p>
      )}
    </motion.button>
  )
}

export const FAQ = () => {
  const [activeIndex, setActiveIndex] = useState<number | null>(0)

  const faqs = [
    {
      question: 'O que posso vender na Fluu?',
      answer:
        'Você pode vender cursos, ebooks, comunidades, softwares, serviços e muito mais. A Fluu suporta pagamentos PIX, cartão e boleto para qualquer tipo de produto digital.',
    },
    {
      question: 'Por que devo usar a Fluu?',
      answer:
        'A Fluu é a única plataforma que combina pagamentos brasileiros (PIX, cartão, boleto), gestão de comunidades e entrega automática de produtos em um só lugar.',
    },
    {
      question: 'Como a Fluu é diferente de outras plataformas de pagamento?',
      answer:
        'A Fluu é focada no mercado brasileiro, com suporte nativo para PIX, taxas transparentes e entrega automática de produtos após pagamento.',
    },
    {
      question: 'Como a Fluu é diferente de outras redes sociais?',
      answer:
        'A Fluu é uma plataforma de negócios completa, não apenas uma rede social. Você pode vender produtos, gerenciar comunidades e receber pagamentos tudo em um lugar.',
    },
    {
      question: 'Desenvolvedores de software podem usar a Fluu?',
      answer:
        'Sim! A Fluu oferece APIs completas para desenvolvedores integrarem pagamentos e gestão de produtos em seus softwares.',
    },
    {
      question: 'A Fluu cobra taxa de assinatura?',
      answer:
        'Não! A Fluu é gratuita para começar. Cobramos apenas 2.7% + R$ 0,30 por transação.',
    },
    {
      question: 'Como a Fluu ajuda com distribuição?',
      answer:
        'A Fluu oferece um marketplace onde creators podem listar seus produtos, além de ferramentas de afiliados e marketing para aumentar o alcance.',
    },
  ]

  return (
    <div className="flex w-full flex-col gap-y-12">
      <div className="flex flex-col items-center gap-y-6">
        <span className="dark:text-polar-500 text-lg text-gray-400">
          Perguntas Frequentes
        </span>
        <h1 className="w-fit max-w-3xl text-center text-3xl text-pretty md:text-5xl md:leading-normal">
          Perguntas frequentes
        </h1>
      </div>

      <div className="mx-auto w-full max-w-4xl">
        {faqs.map((faq, index) => (
          <FAQItem
            key={index}
            question={faq.question}
            answer={faq.answer}
            isOpen={activeIndex === index}
            onClick={() => setActiveIndex(index)}
          />
        ))}
      </div>
    </div>
  )
}

