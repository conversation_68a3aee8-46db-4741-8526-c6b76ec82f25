'use client'

import { LoginCodeError, useSendLoginCode } from '@/hooks/loginCode'
import { usePostHog, type EventName } from '@/hooks/posthog'
import { setValidationErrors } from '@/utils/api/errors'
import { isValidationError, schemas } from '@polar-sh/client'
import Button from '@polar-sh/ui/components/atoms/Button'
import Input from '@polar-sh/ui/components/atoms/Input'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@polar-sh/ui/components/ui/form'
import { useState } from 'react'
import { SubmitHandler, useForm } from 'react-hook-form'

interface LoginCodeFormProps {
  returnTo?: string
  signup?: schemas['UserSignupAttribution']
}

const LoginCodeForm = ({ returnTo, signup }: LoginCodeFormProps) => {
  const form = useForm<{ email: string }>()
  const { control, handleSubmit, setError, formState: { errors } } = form
  const [loading, setLoading] = useState(false)
  const [genericError, setGenericError] = useState<string | null>(null)
  const sendLoginCode = useSendLoginCode()
  const posthog = usePostHog()

  const onSubmit: SubmitHandler<{ email: string }> = async ({ email }) => {
    setLoading(true)
    setGenericError(null)
    let eventName: EventName = 'global:user:login:submit'
    if (signup) {
      eventName = 'global:user:signup:submit'
    }

    posthog.capture(eventName, {
      method: 'code',
    })

    try {
      await sendLoginCode(email, returnTo, signup)
    } catch (e) {
      if (e instanceof LoginCodeError && e.error) {
        if (isValidationError(e.error)) {
          // Handle validation errors (array)
          setValidationErrors(e.error, setError)
        } else {
          // Handle generic errors (string)
          const errorMessage = typeof e.error === 'string' 
            ? e.error 
            : 'An error occurred. Please try again.'
          setGenericError(errorMessage)
          setError('root', {
            type: 'server',
            message: errorMessage,
          })
        }
      } else {
        // Handle unexpected errors
        setGenericError('An unexpected error occurred. Please try again.')
        setError('root', {
          type: 'server',
          message: 'An unexpected error occurred. Please try again.',
        })
      }
    } finally {
      setLoading(false)
    }
  }

  return (
    <Form {...form}>
      <form className="flex w-full flex-col" onSubmit={handleSubmit(onSubmit)}>
        <FormField
          control={control}
          name="email"
          render={({ field }) => {
            return (
              <FormItem>
                <FormControl className="w-full">
                  <div className="flex w-full flex-row gap-2">
                    <Input
                      type="email"
                      required
                      placeholder="Email"
                      autoComplete="off"
                      data-1p-ignore
                      {...field}
                    />
                    <Button
                      type="submit"
                      variant="secondary"
                      loading={loading}
                      disabled={loading}
                    >
                      Login
                    </Button>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )
          }}
        />
        {genericError && (
          <div className="text-destructive-foreground text-sm mt-2">
            {genericError}
          </div>
        )}
        {errors.root && (
          <div className="text-destructive-foreground text-sm mt-2">
            {errors.root.message}
          </div>
        )}
      </form>
    </Form>
  )
}

export default LoginCodeForm
