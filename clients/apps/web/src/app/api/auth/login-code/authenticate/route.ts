import { NextRequest, NextResponse } from 'next/server'

const POLAR_AUTH_COOKIE_KEY =
  process.env.POLAR_AUTH_COOKIE_KEY || 'fluu_session'

export async function POST(request: NextRequest) {
  try {
    // Get query parameters
    const { searchParams } = new URL(request.url)
    const return_to = searchParams.get('return_to') || '/dashboard'
    const email = searchParams.get('email')

    // Get form data
    const formData = await request.formData()
    const code = formData.get('code')

    if (!code || !email) {
      return NextResponse.json(
        { detail: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Forward request to backend
    const backendUrl = new URL(
      `/v1/login-code/authenticate`,
      process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8000'
    )
    backendUrl.searchParams.set('return_to', return_to)
    backendUrl.searchParams.set('email', email)

    const backendFormData = new FormData()
    backendFormData.append('code', code.toString())

    const backendResponse = await fetch(backendUrl.toString(), {
      method: 'POST',
      body: backendFormData,
      redirect: 'manual', // Don't follow redirects
      headers: {
        // Forward important headers
        'User-Agent':
          request.headers.get('user-agent') || 'Fluu-Frontend-Proxy',
      },
    })

    // Handle backend responses
    if (backendResponse.status === 303) {
      // Success! Backend set a cookie, we need to forward it to the client
      const response = NextResponse.json(
        { success: true, redirect: return_to },
        { status: 200 }
      )

      // Extract Set-Cookie headers from backend response
      // Note: When using fetch with redirect: 'manual', headers might not include Set-Cookie
      // We need to extract it from the raw headers
      const rawSetCookieHeaders = backendResponse.headers.getSetCookie?.() || []
      
      // Forward the session cookie
      if (rawSetCookieHeaders.length > 0) {
        for (const cookieHeader of rawSetCookieHeaders) {
          const cookieValue = extractCookieValue(cookieHeader, POLAR_AUTH_COOKIE_KEY)
          if (cookieValue) {
            response.cookies.set({
              name: POLAR_AUTH_COOKIE_KEY,
              value: cookieValue,
              httpOnly: true,
              secure: process.env.NODE_ENV === 'production',
              sameSite: 'lax',
              path: '/',
              maxAge: 60 * 60 * 24 * 31, // 31 days
            })
            break
          }
        }
      }

      return response
    } else if (backendResponse.status === 400 || backendResponse.status === 422) {
      const errorData = await backendResponse.json().catch(() => ({
        detail: 'Invalid or expired code',
      }))
      return NextResponse.json(errorData, { status: backendResponse.status })
    } else {
      return NextResponse.json(
        { detail: 'Authentication failed' },
        { status: backendResponse.status }
      )
    }
  } catch (error) {
    console.error('Authentication proxy error:', error)
    return NextResponse.json(
      { detail: 'An error occurred during authentication' },
      { status: 500 }
    )
  }
}

/**
 * Extract cookie value from a single Set-Cookie header
 */
function extractCookieValue(setCookieHeader: string, cookieName: string): string | null {
  if (setCookieHeader.startsWith(`${cookieName}=`)) {
    const parts = setCookieHeader.split(';')
    const valuePart = parts[0]
    const value = valuePart.split('=')[1]
    return value
  }
  
  return null
}

