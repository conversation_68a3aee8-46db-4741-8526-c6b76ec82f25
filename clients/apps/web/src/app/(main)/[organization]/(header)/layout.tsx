import PublicLayout from '@/components/Layout/PublicLayout'
import { CommunityHeader } from '@/components/Community/CommunityHeader'
import CommunityFooter from '@/components/Community/CommunityFooter'
import { getServerSideAPI } from '@/utils/client/serverside'
import { getStorefront } from '@/utils/storefront'
import { getAuthenticatedUser } from '@/utils/user'
import { mockPublicCommunities } from '@/components/Community/mockPublicCommunities'
import { schemas } from '@polar-sh/client'
import React from 'react'

// Helper function to get organization (real or mock)
async function getOrganizationOrMock(slug: string) {
  try {
    const api = await getServerSideAPI()
    const storefront = await getStorefront(api, slug)

    if (storefront) {
      return storefront.organization
    }
  } catch (error) {
    // If API call fails, silently fall back to mock data
    // Don't log as error to avoid Next.js treating it as unhandled
    // The error is expected when the storefront doesn't exist or API is unavailable
  }

  // Try to find in mock data
  const mockCommunity = mockPublicCommunities.find((c) => c.slug === slug)
  if (mockCommunity) {
    // Create a minimal mock organization object
    const mockOrganization = {
      id: `mock-${slug}`,
      slug: mockCommunity.slug,
      name: mockCommunity.name,
      avatar_url: mockCommunity.avatar_url || null,
      bio: mockCommunity.description,
      email: null,
      website: null,
      socials: [],
      status: 'active' as const,
      details_submitted_at: null,
      feature_settings: null,
      subscription_settings: {
        allow_multiple_subscriptions: false,
        allow_customer_updates: false,
        proration_behavior: 'invoice' as const,
        benefit_revocation_grace_period: 0,
      },
      notification_settings: {
        new_order: false,
        new_subscription: false,
      },
      customer_email_settings: {
        order_confirmation: false,
        subscription_cancellation: false,
        subscription_confirmation: false,
        subscription_cycled: false,
        subscription_past_due: false,
        subscription_revoked: false,
        subscription_uncanceled: false,
        subscription_updated: false,
      },
      created_at: new Date().toISOString(),
      modified_at: new Date().toISOString(),
    } as schemas['Organization']
    return mockOrganization
  }

  return null
}

export default async function Layout(props: {
  params: Promise<{ organization: string }>
  children: React.ReactNode
}) {
  const params = await props.params

  const { children } = props

  const organization = await getOrganizationOrMock(params.organization)
  const authenticatedUser = await getAuthenticatedUser()

  if (!organization) {
    return (
      <PublicLayout className="gap-y-0 py-6 md:py-12" wide>
        <div className="flex flex-col items-center justify-center min-h-screen">
          <h1 className="text-2xl font-bold">Organization Not Found</h1>
          <p className="text-gray-600 dark:text-polar-400">
            The organization you are looking for does not exist
          </p>
        </div>
      </PublicLayout>
    )
  }

  return (
    <div className="flex flex-col min-h-screen">
      <CommunityHeader
        organization={organization}
        authenticatedUser={authenticatedUser}
      />
      <main className="flex-1">{children}</main>
      <CommunityFooter />
    </div>
  )
}
