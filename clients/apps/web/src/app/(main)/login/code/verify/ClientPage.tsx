'use client'

import { CONFIG } from '@/utils/config'
import Button from '@polar-sh/ui/components/atoms/Button'
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from '@polar-sh/ui/components/atoms/InputOTP'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@polar-sh/ui/components/ui/form'
import { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'

const ClientPage = ({
  return_to,
  error,
  email,
}: {
  return_to?: string
  error?: string
  email?: string
}) => {
  const form = useForm<{ code: string }>()
  const { control, setError } = form

  // Set error from urlparams
  useEffect(() => {
    if (error) {
      setError('code', { message: error })
    }
  }, [error, setError])

  const [loading, setLoading] = useState(false)
  
  const handleSubmit = async (code: string) => {
    setLoading(true)
    try {
      const urlSearchParams = new URLSearchParams({
        ...(return_to && { return_to }),
        ...(email && { email }),
      })

      const formData = new FormData()
      formData.append('code', code)

      // Use Next.js API route proxy to handle cookies correctly
      const response = await fetch(
        `/api/auth/login-code/authenticate?${urlSearchParams.toString()}`,
        {
          method: 'POST',
          credentials: 'include',
          body: formData,
        }
      )

      const data = await response.json().catch(() => null)

      if (response.ok && data?.success) {
        // Success! Cookie was set by the API route, now redirect
        window.location.href = data.redirect || return_to || '/dashboard'
      } else if (response.status >= 400) {
        // Error response
        setError('code', { message: data?.detail || 'Invalid or expired code' })
        setLoading(false)
      }
    } catch (err) {
      console.error('Authentication error:', err)
      setError('code', { message: 'An error occurred. Please try again.' })
      setLoading(false)
    }
  }

  const onSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    const code = form.getValues('code')
    if (code && code.length === 6) {
      handleSubmit(code)
    }
  }

  return (
    <Form {...form}>
      <form
        className="flex w-full flex-col items-center gap-y-6"
        onSubmit={onSubmit}
      >
        <FormField
          control={control}
          name="code"
          render={({ field }) => {
            return (
              <FormItem>
                <FormControl>
                  <InputOTP
                    maxLength={6}
                    pattern="^[a-zA-Z0-9]+$"
                    inputMode="text"
                    {...field}
                    onChange={(value) => field.onChange(value.toUpperCase())}
                    onComplete={(value) => {
                      if (value.length === 6) {
                        handleSubmit(value)
                      }
                    }}
                  >
                    <InputOTPGroup>
                      {Array.from({ length: 6 }).map((_, index) => (
                        <InputOTPSlot
                          key={index}
                          index={index}
                          className="dark:border-polar-600 h-12 w-12 border-gray-300 text-xl md:h-16 md:w-16 md:text-2xl"
                        />
                      ))}
                    </InputOTPGroup>
                  </InputOTP>
                </FormControl>
                <FormMessage />
              </FormItem>
            )
          }}
        />
        <Button type="submit" size="lg" className="w-full" loading={loading}>
          Sign in
        </Button>
      </form>
    </Form>
  )
}

export default ClientPage
