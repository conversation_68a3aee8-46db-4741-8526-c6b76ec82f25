#!/bin/bash

# Script de teste para verificar se Stripe é opcional na criação de produtos
# Uso: ./test_stripe_optional.sh

set -e

echo "🧪 Testando criação de produtos com Stripe opcional"
echo ""

# Cores para output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Verificar se servidor está rodando
if ! curl -s http://localhost:8000/health > /dev/null 2>&1; then
    echo -e "${RED}❌ Servidor não está rodando em http://localhost:8000${NC}"
    echo "Execute: cd server && uv run task api"
    exit 1
fi

echo -e "${GREEN}✅ Servidor está rodando${NC}"
echo ""

# Verificar se token está configurado
if [ -z "$POLAR_API_TOKEN" ]; then
    echo -e "${YELLOW}⚠️  POLAR_API_TOKEN não definido${NC}"
    echo "Execute: export POLAR_API_TOKEN='seu_token_aqui'"
    echo ""
    read -p "Digite seu token de API: " token
    export POLAR_API_TOKEN="$token"
fi

# Verificar se organization_id está configurado
if [ -z "$POLAR_ORG_ID" ]; then
    echo -e "${YELLOW}⚠️  POLAR_ORG_ID não definido${NC}"
    echo ""
    read -p "Digite o ID da organização: " org_id
    export POLAR_ORG_ID="$org_id"
fi

echo ""
echo "================================================"
echo "TESTE 1: Criar produto SEM Stripe configurado"
echo "================================================"
echo ""
echo "⚠️  Certifique-se de que POLAR_STRIPE_SECRET_KEY está comentada/removida do .env"
echo ""
read -p "Pressione Enter para continuar..."

RESPONSE=$(curl -s -w "\n%{http_code}" -X POST http://localhost:8000/v1/products/ \
  -H "Authorization: Bearer $POLAR_API_TOKEN" \
  -H "Content-Type: application/json" \
  -d "{
    \"name\": \"Test Product - $(date +%s)\",
    \"description\": \"Testing Stripe optional\",
    \"organization_id\": \"$POLAR_ORG_ID\",
    \"prices\": [{
      \"type\": \"one_time\",
      \"price_amount\": 1000,
      \"price_currency\": \"brl\"
    }]
  }")

HTTP_CODE=$(echo "$RESPONSE" | tail -n1)
BODY=$(echo "$RESPONSE" | head -n -1)

echo ""
echo "Status HTTP: $HTTP_CODE"
echo ""

if [ "$HTTP_CODE" = "201" ]; then
    echo -e "${GREEN}✅ SUCESSO: Produto criado com HTTP 201${NC}"
    echo ""
    
    # Verificar se stripe_product_id é null
    STRIPE_ID=$(echo "$BODY" | python3 -c "import sys, json; data=json.load(sys.stdin); print(data.get('stripe_product_id', 'null'))" 2>/dev/null || echo "null")
    
    if [ "$STRIPE_ID" = "null" ] || [ "$STRIPE_ID" = "None" ]; then
        echo -e "${GREEN}✅ CORRETO: stripe_product_id é null${NC}"
    else
        echo -e "${YELLOW}⚠️  stripe_product_id = $STRIPE_ID (esperado: null)${NC}"
    fi
    
    # Mostrar produto criado
    echo ""
    echo "Produto criado:"
    echo "$BODY" | python3 -m json.tool 2>/dev/null || echo "$BODY"
    
elif [ "$HTTP_CODE" = "500" ]; then
    echo -e "${RED}❌ FALHA: Erro 500 (Stripe ainda está obrigatório)${NC}"
    echo ""
    echo "Resposta:"
    echo "$BODY"
    exit 1
else
    echo -e "${YELLOW}⚠️  Resposta inesperada: HTTP $HTTP_CODE${NC}"
    echo ""
    echo "Resposta:"
    echo "$BODY"
    exit 1
fi

echo ""
echo "================================================"
echo "TESTE 2: Verificar logs do servidor"
echo "================================================"
echo ""
echo "Procure no terminal do servidor por:"
echo "  - ${GREEN}'Stripe not configured, skipping Stripe product creation'${NC}"
echo "  - ou '${GREEN}Product created in Stripe${NC}' (se Stripe estiver configurado)"
echo "  - ou '${YELLOW}Failed to create product in Stripe, continuing${NC}' (se Stripe falhar)"
echo ""

read -p "Pressione Enter quando tiver verificado os logs..."

echo ""
echo "================================================"
echo "✅ TESTES CONCLUÍDOS"
echo "================================================"
echo ""
echo "Resumo:"
echo "  ✅ Servidor responde"
echo "  ✅ Produto criado sem erro 500"
echo "  ✅ stripe_product_id pode ser null"
echo ""
echo "Próximos passos:"
echo "  1. Configurar Stripe (descomentar POLAR_STRIPE_SECRET_KEY)"
echo "  2. Reiniciar servidor"
echo "  3. Criar produto novamente - deve popular stripe_product_id"
echo "  4. Verificar que produtos existentes continuam funcionando"
echo ""

