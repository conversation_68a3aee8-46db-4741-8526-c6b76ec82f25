/**
 * Script de teste para validar o envio de email com código de acesso
 * 
 * Este script testa:
 * 1. Request de login code
 * 2. Verificação de resposta
 * 3. Validação de que o email foi enfileirado
 */

const API_URL = 'http://127.0.0.1:8000';
const ORIGIN = 'http://127.0.0.1:3000';
const TEST_EMAIL = '<EMAIL>';

async function testEmailLogin() {
  console.log('📧 Testando Envio de Email com Código de Acesso...\n');

  // Aguardar um pouco para evitar rate limiting
  console.log('⏳ Aguardando 5 segundos para evitar rate limiting...');
  await new Promise(resolve => setTimeout(resolve, 5000));

  // Teste: Request de login code
  console.log('1️⃣ Testando Request de Login Code...');
  try {
    const response = await fetch(`${API_URL}/v1/login-code/request`, {
      method: 'POST',
      headers: {
        'Origin': ORIGIN,
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify({
        email: TEST_EMAIL,
      }),
    });

    console.log(`   Status: ${response.status}`);
    console.log(`   Headers:`);
    console.log(`     - Access-Control-Allow-Origin: ${response.headers.get('access-control-allow-origin')}`);
    console.log(`     - Access-Control-Allow-Credentials: ${response.headers.get('access-control-allow-credentials')}`);
    console.log(`     - Content-Type: ${response.headers.get('content-type')}`);
    
    if (response.status === 202) {
      console.log('   ✅ Login code request aceito (202 Accepted)');
      console.log('   📧 Email enfileirado para envio');
      console.log('   💡 Verifique os logs do worker para confirmar o envio\n');
    } else if (response.status === 429) {
      const retryAfter = response.headers.get('retry-after');
      console.log(`   ⚠️  Rate limiting ativo (429 Too Many Requests)`);
      console.log(`   ⏳ Aguarde ${retryAfter} segundos antes de tentar novamente\n`);
    } else if (response.status === 500) {
      const data = await response.json().catch(() => ({}));
      console.log(`   ⚠️  Erro 500: ${data.detail || 'Erro interno'}`);
      console.log('   📝 Verifique os logs do servidor para identificar o erro\n');
    } else {
      const data = await response.json().catch(() => ({}));
      console.log(`   ⚠️  Status inesperado: ${response.status}`);
      console.log(`   Response:`, JSON.stringify(data, null, 2));
    }
  } catch (error) {
    console.log(`   ❌ Erro na requisição: ${error.message}\n`);
    if (error.message.includes('CORS')) {
      console.log('   ⚠️  Erro de CORS detectado!\n');
    }
  }

  console.log('✅ Teste concluído!');
  console.log('\n📋 Próximos passos:');
  console.log('   1. Verifique os logs do worker para confirmar o envio do email');
  console.log('   2. Em modo de desenvolvimento (EMAIL_SENDER=logger), o email será logado');
  console.log('   3. Em modo de produção (EMAIL_SENDER=resend), o email será enviado via Resend');
}

// Executar testes
testEmailLogin().catch(console.error);



