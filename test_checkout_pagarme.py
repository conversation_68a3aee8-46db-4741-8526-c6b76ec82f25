#!/usr/bin/env python3
"""
Script de teste simples para verificar se os arquivos foram modificados corretamente
"""

import os
import re
from pathlib import Path


def test_env_config():
    """Testa se as configurações do .env estão corretas"""
    print("🔧 Testando configurações do .env...")

    env_file = Path("server/.env")
    if not env_file.exists():
        print("❌ Arquivo server/.env não encontrado")
        return False

    content = env_file.read_text()

    if "POLAR_ENABLE_PAGARME=true" in content:
        print("✅ POLAR_ENABLE_PAGARME=true encontrado")
    else:
        print("❌ POLAR_ENABLE_PAGARME=true não encontrado")
        return False

    if "POLAR_PAGARME_SECRET_KEY=" in content:
        print("✅ POLAR_PAGARME_SECRET_KEY configurado")
    else:
        print("❌ POLAR_PAGARME_SECRET_KEY não configurado")
        return False

    if "POLAR_PAGARME_PUBLISHABLE_KEY=" in content:
        print("✅ POLAR_PAGARME_PUBLISHABLE_KEY configurado")
    else:
        print("❌ POLAR_PAGARME_PUBLISHABLE_KEY não configurado")
        return False

    return True


def test_payment_processor_enum():
    """Testa se o enum PaymentProcessor tem pagarme"""
    print("\n🔧 Testando enum PaymentProcessor...")

    enums_file = Path("server/polar/enums.py")
    if not enums_file.exists():
        print("❌ Arquivo server/polar/enums.py não encontrado")
        return False

    content = enums_file.read_text()

    if 'pagarme = "pagarme"' in content:
        print("✅ PaymentProcessor.pagarme encontrado")
        return True
    else:
        print("❌ PaymentProcessor.pagarme não encontrado")
        return False


def test_checkout_service_methods():
    """Testa se os métodos do CheckoutService existem"""
    print("\n🔧 Testando métodos do CheckoutService...")

    service_file = Path("server/polar/checkout/service.py")
    if not service_file.exists():
        print("❌ Arquivo server/polar/checkout/service.py não encontrado")
        return False

    content = service_file.read_text()

    methods_found = 0

    if "def _get_default_payment_processor(" in content:
        print("✅ _get_default_payment_processor encontrado")
        methods_found += 1
    else:
        print("❌ _get_default_payment_processor não encontrado")

    if "def _prepare_payment_processor_metadata(" in content:
        print("✅ _prepare_payment_processor_metadata encontrado")
        methods_found += 1
    else:
        print("❌ _prepare_payment_processor_metadata não encontrado")

    if "def confirm_pagarme(" in content:
        print("✅ confirm_pagarme encontrado")
        methods_found += 1
    else:
        print("❌ confirm_pagarme não encontrado")

    return methods_found == 3


def test_checkout_modifications():
    """Testa se as modificações no checkout foram feitas"""
    print("\n🔧 Testando modificações no checkout...")

    service_file = Path("server/polar/checkout/service.py")
    content = service_file.read_text()

    modifications_found = 0

    # Verificar se payment_processor hardcoded foi substituído
    if "self._get_default_payment_processor(" in content:
        print("✅ Chamadas para _get_default_payment_processor encontradas")
        modifications_found += 1
    else:
        print("❌ Chamadas para _get_default_payment_processor não encontradas")

    # Verificar se metadata preparation foi substituído
    if "self._prepare_payment_processor_metadata(" in content:
        print("✅ Chamadas para _prepare_payment_processor_metadata encontradas")
        modifications_found += 1
    else:
        print("❌ Chamadas para _prepare_payment_processor_metadata não encontradas")

    return modifications_found == 2


def test_frontend_integration():
    """Testa se a integração no frontend foi feita"""
    print("\n🔧 Testando integração no frontend...")

    checkout_form_file = Path("clients/packages/checkout/src/components/CheckoutForm.tsx")
    if not checkout_form_file.exists():
        print("❌ Arquivo CheckoutForm.tsx não encontrado")
        return False

    content = checkout_form_file.read_text()

    if "PagarmeCheckoutForm" in content:
        print("✅ PagarmeCheckoutForm importado")
    else:
        print("❌ PagarmeCheckoutForm não importado")
        return False

    if "paymentProcessor === 'pagarme'" in content:
        print("✅ Roteamento para pagarme encontrado")
    else:
        print("❌ Roteamento para pagarme não encontrado")
        return False

    pagarme_form_file = Path("clients/packages/checkout/src/components/PagarmeCheckoutForm.tsx")
    if pagarme_form_file.exists():
        print("✅ PagarmeCheckoutForm.tsx criado")
        return True
    else:
        print("❌ PagarmeCheckoutForm.tsx não encontrado")
        return False


def main():
    """Executa todos os testes"""
    print("🚀 Iniciando testes do checkout Pagar.me...\n")

    tests = [
        ("Configurações .env", test_env_config),
        ("Enum PaymentProcessor", test_payment_processor_enum),
        ("Métodos CheckoutService", test_checkout_service_methods),
        ("Modificações Checkout", test_checkout_modifications),
        ("Integração Frontend", test_frontend_integration),
    ]

    results = []
    for test_name, test_func in tests:
        result = test_func()
        results.append((test_name, result))

    print("\n" + "="*50)
    print("📊 RESUMO DOS TESTES")
    print("="*50)

    passed = 0
    total = len(results)

    for test_name, result in results:
        status = "✅ PASSOU" if result else "❌ FALHOU"
        print(f"{test_name}: {status}")
        if result:
            passed += 1

    print(f"\nResultado: {passed}/{total} testes passaram")

    if passed == total:
        print("🎉 Todos os testes passaram! A implementação está funcionando.")
        print("\n📋 PRÓXIMOS PASSOS:")
        print("1. Reiniciar o servidor backend")
        print("2. Reiniciar o servidor frontend")
        print("3. Criar um checkout e verificar se usa 'pagarme' como payment_processor")
        print("4. Testar o formulário de pagamento PIX/Boleto/Cartão")
        return True
    else:
        print("⚠️  Alguns testes falharam. Verifique a implementação.")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
