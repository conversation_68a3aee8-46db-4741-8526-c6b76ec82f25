# 📊 ANÁLISE COMPLETA: Checkout Multi-Gateway e Adaptação para Brasil

**Data**: 10 de Novembro de 2025  
**Objetivo**: Remover prefixo `polar_`, desacoplar Stripe, usar Pagar.me como default, adaptar para Brasil

---

## 🔍 1. FLUXO ATUAL DO CHECKOUT

### 1.1 Geração de Checkout Link

```
Cliente acessa: /dashboard/org/products/checkout-links
     ↓
Cria Checkout Link
     ↓
Gera client_secret: "polar_cl_" + 37 caracteres + checksum
     ↓
URL gerada: {BASE_URL}/v1/checkout-links/{client_secret}/redirect
```

**Arquivo**: `server/polar/checkout_link/service.py:38`
```python
CHECKOUT_LINK_CLIENT_SECRET_PREFIX = "polar_cl_"
```

### 1.2 Fluxo de Compra

```
1. Cliente clica no link
   → GET /v1/checkout-links/{polar_cl_xxx}/redirect
   
2. Backend cria Checkout Session
   → Gera client_secret: "polar_c_" + 37 caracteres + checksum
   → Redireciona para: /checkout/{polar_c_xxx}
   
3. Frontend renderiza página de checkout
   → Carrega dados via GET /v1/checkouts/client/{polar_c_xxx}
   → Detecta payment_processor (atualmente sempre "stripe")
   → Renderiza Stripe Elements no form
   
4. Cliente preenche dados
   → Update via PATCH /v1/checkouts/client/{polar_c_xxx}
   
5. Cliente confirma pagamento
   → POST /v1/checkouts/client/{polar_c_xxx}/confirm
   → Cria confirmationToken no Stripe (frontend)
   → Backend processa pagamento no Stripe
   → Confirma pedido
```

**Arquivo**: `server/polar/checkout/service.py:201`
```python
CHECKOUT_CLIENT_SECRET_PREFIX = "polar_c_"
```

---

## 🚨 2. PROBLEMAS IDENTIFICADOS

### 2.1 Prefixo "polar_" Hardcoded

**Problema**: Prefixos são constantes hardcoded e apareem toda URL pública.

**Localizações**:
- `server/polar/checkout_link/service.py:38` - `CHECKOUT_LINK_CLIENT_SECRET_PREFIX`
- `server/polar/checkout/service.py:201` - `CHECKOUT_CLIENT_SECRET_PREFIX`

**Impacto**:
- ❌ Branding incorreto ("Polar" no lugar de "Fluu")
- ❌ URLs longas e confusas
- ❌ Cliente vê referência a outra plataforma

**Solução**: Mudar para prefixos da marca Fluu

---

### 2.2 Stripe Hardcoded como Payment Processor

**Problema**: Payment processor é SEMPRE definido como Stripe na criação do checkout.

**Localizações Críticas**:

#### a) `server/polar/checkout/service.py`

**Linha 438** - Checkout via Dashboard:
```python
checkout = Checkout(
    payment_processor=PaymentProcessor.stripe,  # ❌ HARDCODED
    ...
)
```

**Linha 615** - Checkout Público:
```python
checkout = Checkout(
    payment_processor=PaymentProcessor.stripe,  # ❌ HARDCODED
    ...
)
```

**Linha 771** - Checkout via Link:
```python
checkout = Checkout(
    payment_processor=checkout_link.payment_processor,  # ✅ Do CheckoutLink
    ...
)
```

**Linhas 630, 836, 1010** - Condicional Stripe:
```python
if checkout.payment_processor == PaymentProcessor.stripe:
    checkout.payment_processor_metadata = {
        "publishable_key": settings.STRIPE_PUBLISHABLE_KEY,
    }
```

**Impacto**:
- ❌ Impossível usar Pagar.me sem checkout link pré-configurado
- ❌ Checkout direto sempre usa Stripe
- ❌ Lógica específica do Stripe misturada no service

---

### 2.3 Frontend Totalmente Acoplado ao Stripe

**Problema**: Frontend assume Stripe Elements como única opção de pagamento.

**Localizações Críticas**:

#### a) `clients/packages/checkout/src/components/CheckoutForm.tsx`

**Linhas 1055-1064** - Switch de Payment Processor:
```typescript
const CheckoutForm = (props: CheckoutFormProps) => {
  const { checkout: { paymentProcessor } } = props

  if (paymentProcessor === 'stripe') {
    return <StripeCheckoutForm {...props} />
  }
  return <DummyCheckoutForm {...props} />  // ❌ Outros gateways são "dummy"
}
```

**Linhas 943-1037** - StripeCheckoutForm:
```typescript
const StripeCheckoutForm = (props) => {
  const stripePromise = loadStripe(publishable_key)  // Stripe SDK
  
  return (
    <Elements stripe={stripePromise} options={elementsOptions}>
      <PaymentElement />  {/* Stripe Elements */}
    </Elements>
  )
}
```

**Linhas 1039-1053** - DummyCheckoutForm:
```typescript
const DummyCheckoutForm = (props) => {
  return (
    <BaseCheckoutForm
      {...props}
      confirm={async () => ({ ...checkout, status: 'confirmed' })}
      disabled={true}  // ❌ Sempre desabilitado
    />
  )
}
```

**Impacto**:
- ❌ Pagar.me seria tratado como "dummy" (desabilitado)
- ❌ Não há componente para renderizar PIX QR Code
- ❌ Não há componente para Boleto
- ❌ Lógica de pagamento totalmente dependente de Stripe.js

---

### 2.4 Erros do Stripe no Console

**Erro 1**: API Stripe não autorizada (401)
```
GET https://api.stripe.com/v1/elements/sessions?...&key=pk_test_51QSqveP2dJiJQgz8YourStripePublicKeyHere
401 (Unauthorized)
```

**Causa**: Chave pública do Stripe (`pk_test_51QSqveP2dJiJQgz8YourStripePublicKeyHere`) é placeholder ou inválida.

**Localização**: `deploy/cloud-run/env.yaml:82`
```yaml
POLAR_STRIPE_PUBLISHABLE_KEY: pk_test_51QSqveP2dJiJQgz8YourStripePublicKeyHere
```

**Impacto**:
- ❌ Stripe Elements não carrega
- ❌ Form de pagamento fica quebrado
- ❌ Checkout não funciona

---

**Erro 2**: PATCH 500 no checkout update
```
PATCH https://api.fluu.digital/v1/checkouts/client/polar_c_xxx 500 (Internal Server Error)
```

**Causa Provável**: 
1. Tentativa de criar PaymentIntent no Stripe com chave inválida
2. Tentativa de calcular impostos com Stripe Tax
3. Erro ao criar customer no Stripe

**Localização Provável**: `server/polar/checkout/service.py` (múltiplas chamadas Stripe)

---

## 📋 3. ARQUITETURA NECESSÁRIA

### 3.1 Estratégia de Seleção de Gateway

```python
def get_payment_processor(
    product: Product,
    checkout_link: CheckoutLink | None,
    customer: Customer | None,
    organization: Organization
) -> PaymentProcessor:
    """
    Prioridade de seleção:
    1. CheckoutLink.payment_processor (se definido)
    2. Product.preferred_payment_processor (se definido)
    3. Customer.country == "BR" → Pagar.me
    4. settings.ENABLE_PAGARME == True → Pagar.me
    5. Fallback → Stripe
    """
```

**Aplicar em**:
- `checkout_service.create()` (linha 438)
- `checkout_service.client_create()` (linha 615)
- `checkout_link_service.create()` (definir no link)

---

### 3.2 Frontend: Componentes por Gateway

```typescript
// clients/packages/checkout/src/components/CheckoutForm.tsx
const CheckoutForm = (props) => {
  const { checkout: { paymentProcessor } } = props

  switch (paymentProcessor) {
    case 'stripe':
      return <StripeCheckoutForm {...props} />
    
    case 'pagarme':
      return <PagarmeCheckoutForm {...props} />  // 🆕 NOVO
    
    default:
      return <GenericCheckoutForm {...props} />
  }
}
```

**PagarmeCheckoutForm** deve incluir:
- ✅ Formulário de cartão (campo próprio, não Pagar.me.js por enquanto)
- ✅ Botão PIX (mostra QR Code após confirm)
- ✅ Campo CPF/CNPJ obrigatório para Brasil
- ✅ Campo para gerar Boleto

---

### 3.3 Backend: Conditional Gateway Logic

```python
# server/polar/checkout/service.py

async def _prepare_payment_metadata(
    self, checkout: Checkout
) -> dict[str, Any]:
    """Prepara metadata específico do gateway"""
    
    if checkout.payment_processor == PaymentProcessor.stripe:
        return {
            "publishable_key": settings.STRIPE_PUBLISHABLE_KEY,
        }
    
    elif checkout.payment_processor == PaymentProcessor.pagarme:
        return {
            "publishable_key": settings.PAGARME_PUBLISHABLE_KEY,
            "pix_enabled": True,
            "boleto_enabled": True,
        }
    
    return {}
```

**Aplicar em todas as criações de checkout**:
- `create()` - linha 485
- `client_create()` - linha 630
- `checkout_link_create()` - linha 836

---

## 🎯 4. MUDANÇAS NECESSÁRIAS (SEM CÓDIGO AINDA)

### 4.1 Remover Prefixo "polar_"

**Opção A**: Usar "fluu_"
```python
CHECKOUT_LINK_CLIENT_SECRET_PREFIX = "fluu_cl_"
CHECKOUT_CLIENT_SECRET_PREFIX = "fluu_c_"
```

**Opção B**: Remover prefixo completamente
```python
CHECKOUT_LINK_CLIENT_SECRET_PREFIX = ""
CHECKOUT_CLIENT_SECRET_PREFIX = ""
```

**Recomendação**: **Opção B** - Prefixo não agrega valor, apenas aumenta URL.

**Arquivos a modificar**:
- `server/polar/checkout_link/service.py:38`
- `server/polar/checkout/service.py:201`
- `server/tests/checkout_link/test_endpoints.py:7` (testes)

---

### 4.2 Desacoplar Stripe do Backend

#### a) Criar método `get_default_payment_processor()`

```python
# server/polar/checkout/service.py

def _get_default_payment_processor(
    self,
    product: Product | None = None,
    customer: Customer | None = None,
    checkout_link: CheckoutLink | None = None
) -> PaymentProcessor:
    """Determina qual gateway usar baseado em configuração e contexto"""
    
    # 1. CheckoutLink tem prioridade
    if checkout_link and checkout_link.payment_processor:
        return checkout_link.payment_processor
    
    # 2. Produto pode ter preferência
    if product and product.preferred_payment_processor:
        return product.preferred_payment_processor
    
    # 3. Cliente brasileiro → Pagar.me
    if customer and customer.billing_address:
        if customer.billing_address.country == "BR" and settings.ENABLE_PAGARME:
            return PaymentProcessor.pagarme
    
    # 4. Default do sistema (Pagar.me se habilitado)
    if settings.ENABLE_PAGARME:
        return PaymentProcessor.pagarme
    
    # 5. Fallback para Stripe
    return PaymentProcessor.stripe
```

#### b) Abstrair metadata do gateway

```python
async def _prepare_payment_processor_metadata(
    self, checkout: Checkout
) -> dict[str, Any]:
    """Prepara metadata específico do gateway"""
    
    if checkout.payment_processor == PaymentProcessor.stripe:
        metadata = {"publishable_key": settings.STRIPE_PUBLISHABLE_KEY}
        
        # Stripe Customer Session (se cliente existir)
        if checkout.customer and checkout.customer.stripe_customer_id:
            session = await stripe_service.create_customer_session(
                checkout.customer.stripe_customer_id
            )
            metadata["customer_session_client_secret"] = session.client_secret
        
        return metadata
    
    elif checkout.payment_processor == PaymentProcessor.pagarme:
        return {
            "publishable_key": settings.PAGARME_PUBLISHABLE_KEY,
            "pix_enabled": True,
            "boleto_enabled": True,
            "credit_card_enabled": True,
        }
    
    return {}
```

#### c) Aplicar em todas as criações

Substituir nas linhas:
- **438**: `create()` - mudar de `PaymentProcessor.stripe` para `self._get_default_payment_processor(product)`
- **615**: `client_create()` - mudar de `PaymentProcessor.stripe` para `self._get_default_payment_processor(product, customer)`
- **771**: `checkout_link_create()` - já usa `checkout_link.payment_processor` ✅
- **485, 630, 836**: Substituir lógica condicional por `_prepare_payment_processor_metadata()`

---

### 4.3 Frontend: Componente Pagar.me

#### a) Criar `PagarmeCheckoutForm.tsx`

```typescript
// clients/packages/checkout/src/components/PagarmeCheckoutForm.tsx

interface PagarmeCheckoutFormProps extends BaseCheckoutFormProps {
  // ... mesmas props
}

const PagarmeCheckoutForm = (props: PagarmeCheckoutFormProps) => {
  const { checkout, confirm } = props
  const [paymentMethod, setPaymentMethod] = useState<'pix' | 'boleto' | 'credit_card'>('pix')
  const [pixQrCode, setPixQrCode] = useState<string | null>(null)
  
  const handleConfirm = async (data: CheckoutConfirmPagarme) => {
    const result = await confirm({
      ...data,
      payment_method: paymentMethod,
    })
    
    // Se PIX, mostrar QR Code
    if (paymentMethod === 'pix' && result.paymentProcessorMetadata.pix_qr_code) {
      setPixQrCode(result.paymentProcessorMetadata.pix_qr_code)
    }
    
    return result
  }
  
  return (
    <BaseCheckoutForm {...props} confirm={handleConfirm}>
      {/* Seletor de método de pagamento */}
      <PaymentMethodSelector 
        value={paymentMethod} 
        onChange={setPaymentMethod}
        options={['pix', 'boleto', 'credit_card']}
      />
      
      {/* Campo CPF/CNPJ obrigatório */}
      <TaxIdInput required country="BR" />
      
      {/* Form específico do método */}
      {paymentMethod === 'credit_card' && <CreditCardFields />}
      {paymentMethod === 'pix' && <PixInstructions />}
      {paymentMethod === 'boleto' && <BoletoInstructions />}
      
      {/* Modal com QR Code PIX */}
      {pixQrCode && (
        <PixQrCodeModal qrCode={pixQrCode} onClose={() => setPixQrCode(null)} />
      )}
    </BaseCheckoutForm>
  )
}
```

#### b) Componentes auxiliares necessários

1. **`PaymentMethodSelector`**: Radio buttons com ícones
   - 💳 Cartão de Crédito
   - 📱 PIX (destacado para Brasil)
   - 🧾 Boleto

2. **`TaxIdInput`**: Campo CPF/CNPJ com validação
   - Máscara automática
   - Validação de dígitos verificadores
   - Obrigatório para Brasil

3. **`CreditCardFields`**: Campos de cartão
   - Número do cartão (com máscara)
   - Validade (MM/YY)
   - CVV
   - Nome no cartão

4. **`PixQrCodeModal`**: Modal com QR Code
   - QR Code renderizado via biblioteca
   - Código PIX copiável (copia e cola)
   - Timer de expiração (10 minutos)
   - Instrução de como pagar

5. **`PixInstructions`**: Instruções antes do pagamento
   ```
   ⚡ Pagamento Instantâneo via PIX
   
   1. Clique em "Confirmar"
   2. Escaneie o QR Code com o app do seu banco
   3. Confirme o pagamento
   4. Pronto! Acesso liberado em segundos
   ```

6. **`BoletoInstructions`**: Instruções do Boleto
   ```
   🧾 Boleto Bancário
   
   - Vencimento: 3 dias úteis
   - Pode levar até 2 dias úteis para compensar
   - Após o pagamento, você receberá acesso por email
   ```

---

### 4.4 Backend: Endpoint de Confirmação

#### Modificar `POST /v1/checkouts/client/{client_secret}/confirm`

```python
# server/polar/checkout/endpoints.py

@router.post(
    "/client/{client_secret}/confirm",
    response_model=CheckoutPublicConfirmed,
)
async def client_confirm(
    client_secret: CheckoutClientSecret,
    checkout_confirm: CheckoutConfirm,  # Schema flexível
    locker: Locker,
    session: AsyncSession,
) -> CheckoutPublicConfirmed:
    checkout = await checkout_service.get_by_client_secret(session, client_secret)
    
    if checkout is None:
        raise ResourceNotFound()
    
    # Roteamento por gateway
    if checkout.payment_processor == PaymentProcessor.stripe:
        return await checkout_service.confirm_stripe(
            session, locker, checkout, checkout_confirm
        )
    
    elif checkout.payment_processor == PaymentProcessor.pagarme:
        return await checkout_service.confirm_pagarme(
            session, locker, checkout, checkout_confirm
        )
    
    raise BadRequest("Unsupported payment processor")
```

#### Novo método `confirm_pagarme()`

```python
# server/polar/checkout/service.py

async def confirm_pagarme(
    self,
    session: AsyncSession,
    locker: Locker,
    checkout: Checkout,
    checkout_confirm: CheckoutConfirmPagarme,
) -> CheckoutPublicConfirmed:
    """Confirma checkout com Pagar.me"""
    
    # 1. Obter provider
    provider = PaymentProviderRegistry.get(PaymentProcessor.pagarme)
    
    # 2. Criar/obter customer
    customer = await self._ensure_customer(session, checkout)
    
    # 3. Criar pagamento baseado no método
    if checkout_confirm.payment_method == "pix":
        payment = await provider.create_pix_payment(
            customer=customer,
            amount=checkout.total_amount,
            currency=checkout.currency,
            metadata={
                "checkout_id": str(checkout.id),
                "product_id": str(checkout.product_id),
            }
        )
        
        # Retornar QR Code nos metadata
        checkout.payment_processor_metadata = {
            **checkout.payment_processor_metadata,
            "pix_qr_code": payment.pix_qr_code,
            "pix_qr_code_url": payment.pix_qr_code_url,
            "pix_expires_at": payment.expires_at.isoformat(),
        }
    
    elif checkout_confirm.payment_method == "boleto":
        payment = await provider.create_boleto_payment(...)
        
    elif checkout_confirm.payment_method == "credit_card":
        payment = await provider.create_card_payment(...)
    
    # 4. Atualizar checkout
    checkout.status = CheckoutStatus.confirmed
    checkout.payment_processor_metadata["payment_id"] = payment.id
    
    session.add(checkout)
    await session.commit()
    
    return CheckoutPublicConfirmed.model_validate(checkout)
```

---

### 4.5 Configurações de Ambiente

#### Adicionar ao `deploy/cloud-run/env.yaml`

```yaml
# ============================================
# PAGAR.ME (Gateway Padrão para Brasil)
# ============================================
POLAR_ENABLE_PAGARME: "true"
POLAR_PAGARME_SECRET_KEY: sk_test_SUAS_CHAVES_AQUI
POLAR_PAGARME_PUBLISHABLE_KEY: pk_test_SUAS_CHAVES_AQUI
POLAR_PAGARME_WEBHOOK_SECRET: whsec_SUAS_CHAVES_AQUI

# ============================================
# STRIPE (Gateway Secundário)
# ============================================
# ATENÇÃO: Substituir com chaves válidas ou deixar vazio
POLAR_STRIPE_SECRET_KEY: ""
POLAR_STRIPE_PUBLISHABLE_KEY: ""
POLAR_STRIPE_WEBHOOK_SECRET: ""
```

#### Tornar Stripe Opcional no `config.py`

```python
# server/polar/config.py

# Stripe (OPCIONAL)
STRIPE_SECRET_KEY: str = ""
STRIPE_PUBLISHABLE_KEY: str = ""
STRIPE_WEBHOOK_SECRET: str = ""

# Pagar.me (PREFERENCIAL para Brasil)
PAGARME_SECRET_KEY: str = Field(default="")
PAGARME_PUBLISHABLE_KEY: str = Field(default="")
PAGARME_WEBHOOK_SECRET: str = Field(default="")
ENABLE_PAGARME: bool = Field(default=True)  # True por padrão
```

---

## 🇧🇷 5. ADAPTAÇÕES ESPECÍFICAS PARA BRASIL

### 5.1 PIX como Método Principal

**Prioridade Visual**:
```typescript
// Ordem de exibição no checkout
const paymentMethods = [
  { id: 'pix', label: 'PIX', icon: '⚡', recommended: true },
  { id: 'credit_card', label: 'Cartão de Crédito', icon: '💳' },
  { id: 'boleto', label: 'Boleto', icon: '🧾' },
]
```

**Banner destaque**:
```tsx
{checkout.currency === 'brl' && (
  <div className="bg-green-100 p-4 rounded-lg">
    <div className="flex items-center gap-2">
      <span className="text-2xl">⚡</span>
      <div>
        <div className="font-bold text-green-800">PIX Instantâneo</div>
        <div className="text-sm text-green-700">
          Confirmação em segundos! Sem taxas, aprovação automática.
        </div>
      </div>
    </div>
  </div>
)}
```

---

### 5.2 CPF/CNPJ Obrigatório

**Validação no Frontend**:
```typescript
const validateTaxId = (value: string, country: string) => {
  if (country !== 'BR') return true
  
  const cleaned = value.replace(/\D/g, '')
  
  if (cleaned.length === 11) {
    return validateCPF(cleaned)
  } else if (cleaned.length === 14) {
    return validateCNPJ(cleaned)
  }
  
  return 'CPF ou CNPJ inválido'
}
```

**Campo Obrigatório**:
```tsx
<FormField
  control={control}
  name="customerTaxId"
  rules={{
    required: checkout.currency === 'brl' ? 'CPF/CNPJ obrigatório' : false,
    validate: (value) => validateTaxId(value, checkout.currency)
  }}
  render={({ field }) => (
    <FormItem>
      <FormLabel>CPF/CNPJ {checkout.currency === 'brl' && '*'}</FormLabel>
      <Input
        {...field}
        placeholder={isBusinessCustomer ? '00.000.000/0000-00' : '000.000.000-00'}
        mask={isBusinessCustomer ? 'cnpj' : 'cpf'}
      />
    </FormItem>
  )}
/>
```

---

### 5.3 Moeda BRL por Padrão

**Backend**: Detectar país e definir moeda

```python
# server/polar/checkout/service.py

async def _determine_currency(
    self,
    customer: Customer | None,
    ip_geolocation: dict | None
) -> str:
    """Determina moeda baseada em localização"""
    
    # 1. Cliente com endereço brasileiro
    if customer and customer.billing_address:
        if customer.billing_address.country == "BR":
            return "brl"
    
    # 2. IP brasileiro
    if ip_geolocation and ip_geolocation.get("country_code") == "BR":
        return "brl"
    
    # 3. Default USD
    return "usd"
```

---

### 5.4 Textos em Português

**Substituir textos hardcoded**:

```typescript
// ANTES
'Subscribe' → 'Assinar'
'Pay' → 'Pagar'
'Total' → 'Total'
'Cardholder name' → 'Nome no cartão'
'Billing address' → 'Endereço de cobrança'
'Postal code' → 'CEP'
'Tax ID' → 'CPF/CNPJ'

// Mensagens de erro
'Payment failed' → 'Pagamento falhou'
'This field is required' → 'Este campo é obrigatório'
'Invalid email' → 'Email inválido'
```

**Criar arquivo de tradução**:
```typescript
// clients/packages/checkout/src/i18n/pt-BR.ts
export const ptBR = {
  checkout: {
    subscribe: 'Assinar',
    pay: 'Pagar',
    submit: 'Confirmar',
    total: 'Total',
    subtotal: 'Subtotal',
    taxes: 'Impostos',
    discount: 'Desconto',
  },
  fields: {
    email: 'Email',
    name: 'Nome',
    cardholderName: 'Nome no cartão',
    billingAddress: 'Endereço de cobrança',
    postalCode: 'CEP',
    city: 'Cidade',
    state: 'Estado',
    country: 'País',
    taxId: 'CPF/CNPJ',
  },
  errors: {
    required: 'Este campo é obrigatório',
    invalidEmail: 'Email inválido',
    invalidTaxId: 'CPF/CNPJ inválido',
    paymentFailed: 'Pagamento falhou. Tente novamente.',
  },
  payment: {
    pix: 'PIX',
    creditCard: 'Cartão de Crédito',
    boleto: 'Boleto Bancário',
    processing: 'Processando pagamento...',
    success: 'Pagamento aprovado!',
  }
}
```

---

## 📊 6. RESUMO DAS MUDANÇAS

### Backend (Python/FastAPI)

| Arquivo | Mudança | Prioridade |
|---------|---------|------------|
| `server/polar/checkout_link/service.py` | Remover prefixo `polar_cl_` | 🟡 Média |
| `server/polar/checkout/service.py` | Remover prefixo `polar_c_` | 🟡 Média |
| `server/polar/checkout/service.py` | Adicionar `_get_default_payment_processor()` | 🔴 Alta |
| `server/polar/checkout/service.py` | Adicionar `_prepare_payment_processor_metadata()` | 🔴 Alta |
| `server/polar/checkout/service.py` | Modificar `create()` linha 438 | 🔴 Alta |
| `server/polar/checkout/service.py` | Modificar `client_create()` linha 615 | 🔴 Alta |
| `server/polar/checkout/service.py` | Adicionar `confirm_pagarme()` | 🔴 Alta |
| `server/polar/checkout/service.py` | Adicionar `_determine_currency()` | 🟢 Baixa |
| `server/polar/checkout/endpoints.py` | Modificar `client_confirm()` roteamento | 🔴 Alta |
| `server/polar/config.py` | Tornar Stripe opcional | 🟡 Média |
| `deploy/cloud-run/env.yaml` | Adicionar chaves Pagar.me válidas | 🔴 Alta |
| `deploy/cloud-run/env.yaml` | Remover/corrigir chaves Stripe | 🔴 Alta |

### Frontend (TypeScript/React)

| Arquivo | Mudança | Prioridade |
|---------|---------|------------|
| `packages/checkout/src/components/CheckoutForm.tsx` | Adicionar switch para Pagar.me | 🔴 Alta |
| `packages/checkout/src/components/PagarmeCheckoutForm.tsx` | **CRIAR NOVO** | 🔴 Alta |
| `packages/checkout/src/components/PaymentMethodSelector.tsx` | **CRIAR NOVO** | 🔴 Alta |
| `packages/checkout/src/components/TaxIdInput.tsx` | **CRIAR NOVO** | 🟡 Média |
| `packages/checkout/src/components/PixQrCodeModal.tsx` | **CRIAR NOVO** | 🔴 Alta |
| `packages/checkout/src/components/PixInstructions.tsx` | **CRIAR NOVO** | 🟢 Baixa |
| `packages/checkout/src/i18n/pt-BR.ts` | **CRIAR NOVO** (traduções) | 🟡 Média |
| `apps/web/src/components/Checkout/FluuCheckout.tsx` | Atualizar banners para PIX | 🟢 Baixa |

---

## 🎯 7. ROADMAP DE IMPLEMENTAÇÃO

### Fase 1: Corrigir Erros Críticos (1-2 dias)
1. ✅ Adicionar `POLAR_CHECKOUT_BASE_URL` (FEITO)
2. ⏳ Corrigir chaves Stripe em `env.yaml` (ou remover se não usar)
3. ⏳ Adicionar chaves Pagar.me válidas em `env.yaml`
4. ⏳ Testar checkout atual e identificar erro 500

### Fase 2: Desacoplar Stripe (3-4 dias)
1. ⏳ Implementar `_get_default_payment_processor()`
2. ⏳ Implementar `_prepare_payment_processor_metadata()`
3. ⏳ Modificar `create()` e `client_create()`
4. ⏳ Testar checkout com Pagar.me via checkout link

### Fase 3: Frontend Pagar.me (5-7 dias)
1. ⏳ Criar `PagarmeCheckoutForm.tsx`
2. ⏳ Criar `PaymentMethodSelector.tsx`
3. ⏳ Criar `TaxIdInput.tsx` com validação CPF/CNPJ
4. ⏳ Criar `PixQrCodeModal.tsx`
5. ⏳ Implementar `confirm_pagarme()` no backend
6. ⏳ Testar fluxo completo PIX

### Fase 4: Refinamentos Brasil (2-3 dias)
1. ⏳ Adicionar traduções pt-BR
2. ⏳ Banners PIX em destaque
3. ⏳ Moeda BRL por padrão para Brasil
4. ⏳ Testar cartão de crédito e Boleto

### Fase 5: Remover Prefixo "polar_" (1 dia)
1. ⏳ Atualizar constantes de prefixo
2. ⏳ Atualizar testes
3. ⏳ Deploy e validação

---

## ✅ 8. CHECKLIST DE VALIDAÇÃO

Antes de considerar concluído:

### Backend
- [ ] Checkout com Pagar.me funciona sem Stripe configurado
- [ ] PIX gera QR Code válido
- [ ] Boleto gera PDF/código de barras
- [ ] Cartão Pagar.me processa pagamento
- [ ] Webhooks Pagar.me funcionam
- [ ] Moeda BRL é default para Brasil
- [ ] CPF/CNPJ é obrigatório e validado

### Frontend
- [ ] Checkout renderiza form Pagar.me corretamente
- [ ] PIX mostra QR Code após confirmar
- [ ] QR Code é escaneável (testar com app banco)
- [ ] CPF/CNPJ tem máscara e validação
- [ ] Textos estão em português
- [ ] Banner PIX aparece para BRL
- [ ] Form de cartão funciona
- [ ] Instruções de Boleto são claras

### Geral
- [ ] Prefixo "polar_" foi removido (opcional)
- [ ] URLs de checkout são limpas
- [ ] Checkout funciona sem Stripe configurado
- [ ] Stripe ainda funciona se configurado
- [ ] Documentação atualizada

---

## 📝 9. NOTAS IMPORTANTES

### Compatibilidade
- ✅ Manter Stripe funcionando (não remover, só desacoplar)
- ✅ CheckoutLinks antigos continuam funcionando
- ✅ Clientes atuais não são afetados

### Segurança
- ⚠️ Nunca expor `PAGARME_SECRET_KEY` no frontend
- ⚠️ Validar CPF/CNPJ no backend também
- ⚠️ Validar webhooks com `PAGARME_WEBHOOK_SECRET`

### Performance
- 💡 QR Code PIX expira em 10 minutos (configurável)
- 💡 Boleto expira em 3 dias úteis
- 💡 Cachear publicable keys no frontend

### UX Brasil
- 🇧🇷 PIX sempre em primeiro lugar
- 🇧🇷 CPF/CNPJ com máscara automática
- 🇧🇷 CEP com máscara (00000-000)
- 🇧🇷 Valores formatados em R$

---

## 🎓 10. REFERÊNCIAS

- Documentação Pagar.me: https://docs.pagar.me/
- PIX QR Code Spec: https://www.bcb.gov.br/estabilidadefinanceira/pix
- Validação CPF/CNPJ: https://www.geradorcpf.com/javascript-validar-cpf.htm
- Stripe Elements (referência): https://stripe.com/docs/payments/elements

---

**FIM DA ANÁLISE**

