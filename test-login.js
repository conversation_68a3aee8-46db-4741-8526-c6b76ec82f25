/**
 * <PERSON>ript de teste para validar o login code
 * 
 * Este script testa:
 * 1. Preflight CORS (OPTIONS)
 * 2. Request de login code (POST)
 * 3. Validação de headers CORS
 * 4. Tratamento de erros
 */

const API_URL = 'http://127.0.0.1:8000';
const ORIGIN = 'http://127.0.0.1:3000';
const TEST_EMAIL = '<EMAIL>';

async function testLoginCode() {
  console.log('🧪 Testando Login Code...\n');

  // Teste 1: Preflight CORS
  console.log('1️⃣ Testando Preflight CORS (OPTIONS)...');
  try {
    const preflightResponse = await fetch(`${API_URL}/v1/login-code/request`, {
      method: 'OPTIONS',
      headers: {
        'Origin': ORIGIN,
        'Access-Control-Request-Method': 'POST',
        'Access-Control-Request-Headers': 'content-type',
      },
    });

    console.log(`   Status: ${preflightResponse.status}`);
    console.log(`   Headers:`);
    console.log(`     - Access-Control-Allow-Origin: ${preflightResponse.headers.get('access-control-allow-origin')}`);
    console.log(`     - Access-Control-Allow-Credentials: ${preflightResponse.headers.get('access-control-allow-credentials')}`);
    console.log(`     - Access-Control-Allow-Methods: ${preflightResponse.headers.get('access-control-allow-methods')}`);
    
    if (preflightResponse.status === 200) {
      console.log('   ✅ Preflight CORS OK\n');
    } else {
      console.log('   ❌ Preflight CORS falhou\n');
    }
  } catch (error) {
    console.log(`   ❌ Erro no preflight: ${error.message}\n`);
  }

  // Teste 2: Request de login code
  console.log('2️⃣ Testando Request de Login Code (POST)...');
  try {
    const response = await fetch(`${API_URL}/v1/login-code/request`, {
      method: 'POST',
      headers: {
        'Origin': ORIGIN,
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify({
        email: TEST_EMAIL,
      }),
    });

    console.log(`   Status: ${response.status}`);
    console.log(`   Headers:`);
    console.log(`     - Access-Control-Allow-Origin: ${response.headers.get('access-control-allow-origin')}`);
    console.log(`     - Access-Control-Allow-Credentials: ${response.headers.get('access-control-allow-credentials')}`);
    console.log(`     - Content-Type: ${response.headers.get('content-type')}`);
    
    const data = await response.json();
    console.log(`   Response:`, JSON.stringify(data, null, 2));
    
    if (response.status === 202) {
      console.log('   ✅ Login code request OK\n');
    } else if (response.status === 500) {
      console.log('   ⚠️  Erro 500 (mas CORS está funcionando)\n');
      console.log('   📝 Verifique os logs do servidor para identificar o erro\n');
    } else {
      console.log(`   ⚠️  Status inesperado: ${response.status}\n`);
    }
  } catch (error) {
    console.log(`   ❌ Erro na requisição: ${error.message}\n`);
    if (error.message.includes('CORS')) {
      console.log('   ⚠️  Erro de CORS detectado!\n');
    }
  }

  // Teste 3: Validação de email inválido
  console.log('3️⃣ Testando validação de email inválido...');
  try {
    const response = await fetch(`${API_URL}/v1/login-code/request`, {
      method: 'POST',
      headers: {
        'Origin': ORIGIN,
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify({
        email: 'invalid-email',
      }),
    });

    const data = await response.json();
    console.log(`   Status: ${response.status}`);
    console.log(`   Response:`, JSON.stringify(data, null, 2));
    
    if (response.status === 422 && data.error === 'RequestValidationError') {
      console.log('   ✅ Validação de email funcionando\n');
    } else {
      console.log('   ⚠️  Validação de email não funcionou como esperado\n');
    }
  } catch (error) {
    console.log(`   ❌ Erro na validação: ${error.message}\n`);
  }

  console.log('✅ Testes concluídos!');
}

// Executar testes
testLoginCode().catch(console.error);

