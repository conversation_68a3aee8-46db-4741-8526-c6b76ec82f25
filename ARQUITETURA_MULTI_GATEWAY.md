# 🏗️ Arquitetura Multi-Gateway - Fluu

## 🎯 Visão Geral

O Fluu é um fork do Polar.sh adaptado para o mercado brasileiro, com foco em suportar **múltiplos gateways de pagamento** e métodos de pagamento locais, especialmente **PIX**.

### Diferencial Competitivo

- ✅ **Multi-Gateway**: Suporte simultâneo a Stripe, Pagar.me, e outros gateways
- ✅ **PIX**: Pagamento instantâneo brasileiro nativo
- ✅ **Gateways Brasileiros**: Pagar.me, Mercado Pago, etc.
- ✅ **Gateway Agnóstico**: Produtos existem independente de gateway

---

## 📊 Arquitetura Atual

### 1. Payment Provider Registry

Todos os gateways são registrados em um registry centralizado:

```python
# server/polar/integrations/payment_providers/registry.py

class PaymentProviderRegistry:
    _providers: Dict[PaymentProcessor, PaymentProvider] = {}
    
    @classmethod
    def register(cls, processor: PaymentProcessor, provider: PaymentProvider):
        cls._providers[processor] = provider
    
    @classmethod
    def get(cls, processor: PaymentProcessor) -> PaymentProvider:
        if processor not in cls._providers:
            raise ValueError(f"No provider registered for {processor}")
        return cls._providers[processor]
```

**Localização**: `server/polar/integrations/payment_providers/registry.py`

### 2. Payment Providers

Cada gateway implementa a interface `PaymentProvider`:

```python
# server/polar/integrations/payment_providers/base.py

class PaymentProvider(ABC):
    @property
    @abstractmethod
    def processor(self) -> PaymentProcessor:
        """Retorna o PaymentProcessor deste provider."""
        pass
    
    @abstractmethod
    async def create_customer(self, email: str, name: str, metadata: dict) -> str:
        """Cria customer no gateway."""
        pass
    
    @abstractmethod
    async def create_payment_intent(
        self, 
        amount: int, 
        currency: str, 
        customer_id: str,
        ...
    ) -> dict:
        """Cria intenção de pagamento."""
        pass
    
    @abstractmethod
    async def handle_webhook(self, event_type: str, event_data: dict):
        """Processa webhook do gateway."""
        pass
```

### 3. Gateways Implementados

#### Stripe (Original)
- **Localização**: `server/polar/integrations/stripe/`
- **Status**: ✅ Totalmente integrado
- **Métodos**: Cartão de crédito, débito, wallets
- **Região**: Global

#### Pagar.me (Novo)
- **Localização**: `server/polar/integrations/payment_providers/pagarme/`
- **Status**: ✅ Implementado (necessita completar PIX)
- **Métodos**: Cartão de crédito, PIX, boleto
- **Região**: Brasil

---

## 🔄 Fluxo de Checkout Multi-Gateway

### 1. Seleção do Gateway

```
┌─────────────────────────────────────┐
│   Usuário cria Checkout             │
└────────────┬────────────────────────┘
             │
             ▼
┌─────────────────────────────────────┐
│  Determinar Gateway Preferencial    │
│                                     │
│  1. ENABLE_PAGARME configurado?     │
│     SIM → Pagar.me (prioridade BR)  │
│                                     │
│  2. Stripe configurado?             │
│     SIM → Stripe (fallback global)  │
│                                     │
│  3. Nenhum configurado              │
│     ERRO → Gateway não disponível   │
└────────────┬────────────────────────┘
             │
             ▼
┌─────────────────────────────────────┐
│   Criar Checkout com Gateway        │
│   payment_processor = <gateway>     │
└─────────────────────────────────────┘
```

### 2. Processamento de Pagamento

```
┌─────────────────────────────────────┐
│   Cliente confirma Checkout         │
└────────────┬────────────────────────┘
             │
             ▼
┌─────────────────────────────────────┐
│  Registry.get(payment_processor)    │
└────────────┬────────────────────────┘
             │
             ├─────────┐
             │         │
             ▼         ▼
    ┌────────────┐  ┌──────────────┐
    │  Stripe    │  │  Pagar.me    │
    │  Provider  │  │  Provider    │
    └────────────┘  └──────────────┘
```

### 3. Webhooks

```
POST /integrations/stripe/webhook     → Stripe Provider
POST /integrations/pagarme/webhook    → Pagar.me Provider
```

Cada provider processa seus próprios webhooks e atualiza o status do pagamento.

---

## 💾 Modelo de Dados Multi-Gateway

### Produto (Product)

```python
class Product(RecordModel):
    # Campos existentes
    stripe_product_id: Mapped[str | None]
    
    # Novos campos (FASE 2)
    pagarme_product_id: Mapped[str | None]
    pagarme_plan_id: Mapped[str | None]  # Para recorrência
    preferred_payment_processor: Mapped[PaymentProcessor | None]
```

**Estratégia**:
- Produto pode ter IDs em **múltiplos gateways** simultaneamente
- `preferred_payment_processor` indica gateway preferencial
- Se NULL, usa gateway configurado globalmente

### Checkout

```python
class Checkout(RecordModel):
    # Campo que determina qual gateway usar
    payment_processor: Mapped[PaymentProcessor]
    
    # Metadata específico do gateway
    payment_processor_metadata: Mapped[dict | None]
```

**payment_processor_metadata** exemplos:

```python
# Stripe
{
    "publishable_key": "pk_test_xxx",
    "customer_session_client_secret": "cs_xxx"
}

# Pagar.me
{
    "publishable_key": "pk_test_xxx",
    "pix_qr_code": "00020126...",
    "pix_qr_code_url": "https://..."
}
```

### Payment

```python
class Payment(RecordModel):
    processor: Mapped[PaymentProcessor]
    processor_id: Mapped[str]  # ID no gateway
    status: Mapped[PaymentStatus]
    method: Mapped[str]  # "credit_card", "pix", "boleto"
```

---

## 🔧 Implementação: Suporte ao PIX

### 1. Adicionar método PIX ao PagarmeProvider

```python
# server/polar/integrations/payment_providers/pagarme/provider.py

async def create_pix_payment(
    self,
    amount: int,
    customer_id: str,
    description: str,
    metadata: dict,
) -> dict:
    """Cria pagamento PIX no Pagar.me."""
    payload = {
        "customer_id": customer_id,
        "items": [{
            "amount": amount,
            "description": description,
            "quantity": 1,
        }],
        "payments": [{
            "payment_method": "pix",
            "pix": {
                "expires_in": 3600,  # 1 hora
            }
        }],
        "metadata": metadata,
    }
    
    response = await self.client.post("/orders", json=payload)
    order_data = response.json()
    
    # Extrair dados do PIX
    charge = order_data["charges"][0]
    pix_qr_code = charge["last_transaction"]["qr_code"]
    pix_qr_code_url = charge["last_transaction"]["qr_code_url"]
    
    return {
        "id": order_data["id"],
        "charge_id": charge["id"],
        "status": "pending",
        "qr_code": pix_qr_code,
        "qr_code_url": pix_qr_code_url,
        "expires_at": charge["last_transaction"]["expires_at"],
    }
```

### 2. Atualizar CheckoutService

```python
# server/polar/checkout/service.py

def _determine_payment_processor(
    self,
    product: Product,
) -> PaymentProcessor:
    """Determina qual gateway usar para o checkout."""
    
    # 1. Preferência do produto (se configurado)
    if product.preferred_payment_processor:
        if PaymentProviderRegistry.is_supported(
            product.preferred_payment_processor
        ):
            return product.preferred_payment_processor
    
    # 2. Pagar.me tem prioridade (mercado brasileiro)
    if settings.ENABLE_PAGARME and PaymentProviderRegistry.is_supported(
        PaymentProcessor.pagarme
    ):
        return PaymentProcessor.pagarme
    
    # 3. Fallback para Stripe
    if settings.STRIPE_SECRET_KEY:
        return PaymentProcessor.stripe
    
    # 4. Nenhum gateway disponível
    raise PaymentProviderError("No payment gateway configured")
```

### 3. Modificar criação de Checkout

```python
# server/polar/checkout/service.py

async def client_create(...):
    # ... código existente ...
    
    # Determinar gateway a usar
    payment_processor = self._determine_payment_processor(product)
    
    checkout = Checkout(
        payment_processor=payment_processor,  # ✅ Dinâmico!
        client_secret=generate_token(prefix=CHECKOUT_CLIENT_SECRET_PREFIX),
        # ... resto dos campos ...
    )
    
    # Configurar metadata específico do gateway
    if checkout.payment_processor == PaymentProcessor.pagarme:
        checkout.payment_processor_metadata = {
            "publishable_key": settings.PAGARME_PUBLISHABLE_KEY,
        }
    elif checkout.payment_processor == PaymentProcessor.stripe:
        checkout.payment_processor_metadata = {
            "publishable_key": settings.STRIPE_PUBLISHABLE_KEY,
        }
    
    # ... resto do código ...
```

---

## 🌐 Configuração de Gateways

### Variáveis de Ambiente

```bash
# Stripe (Global)
POLAR_STRIPE_SECRET_KEY=sk_test_xxx
POLAR_STRIPE_PUBLISHABLE_KEY=pk_test_xxx

# Pagar.me (Brasil)
POLAR_PAGARME_SECRET_KEY=sk_test_xxx
POLAR_PAGARME_PUBLISHABLE_KEY=pk_test_xxx
ENABLE_PAGARME=true

# Prioridade
# Se ENABLE_PAGARME=true, Pagar.me tem prioridade
# Senão, usa Stripe como fallback
```

### Registro dos Providers

```python
# server/polar/app.py ou startup

from polar.integrations.payment_providers.registry import PaymentProviderRegistry
from polar.integrations.payment_providers.pagarme.provider import PagarmeProvider
from polar.integrations.stripe.service import StripeProvider  # Hipotético

# Registrar providers
if settings.STRIPE_SECRET_KEY:
    PaymentProviderRegistry.register(
        PaymentProcessor.stripe,
        StripeProvider()
    )

if settings.ENABLE_PAGARME:
    PaymentProviderRegistry.register(
        PaymentProcessor.pagarme,
        PagarmeProvider()
    )
```

---

## 📱 Frontend: Suporte Multi-Gateway

### 1. Checkout Component

```typescript
// clients/apps/web/src/components/Checkout/Checkout.tsx

interface CheckoutProps {
  checkout: Checkout;
}

const Checkout: React.FC<CheckoutProps> = ({ checkout }) => {
  // Renderizar componente específico do gateway
  switch (checkout.payment_processor) {
    case 'stripe':
      return <StripeCheckout checkout={checkout} />;
    
    case 'pagarme':
      return <PagarmeCheckout checkout={checkout} />;
    
    default:
      return <div>Gateway não suportado</div>;
  }
};
```

### 2. PIX Component

```typescript
// clients/apps/web/src/components/Checkout/PIXCheckout.tsx

const PIXCheckout: React.FC<{ checkout: Checkout }> = ({ checkout }) => {
  const [pixData, setPixData] = useState<PIXData | null>(null);
  
  useEffect(() => {
    // Criar pagamento PIX
    createPIXPayment(checkout.id).then(setPixData);
  }, [checkout.id]);
  
  if (!pixData) return <Loading />;
  
  return (
    <div>
      <h2>Pagar com PIX</h2>
      
      {/* QR Code */}
      <QRCodeSVG value={pixData.qr_code} size={200} />
      
      {/* Código PIX copiável */}
      <CopyButton text={pixData.qr_code}>
        Copiar código PIX
      </CopyButton>
      
      {/* Timer de expiração */}
      <Timer expiresAt={pixData.expires_at} />
      
      {/* Polling para verificar pagamento */}
      <PaymentStatusPoller checkoutId={checkout.id} />
    </div>
  );
};
```

---

## 🔄 Lazy Creation (FASE 2)

Quando produto não tem ID no gateway, criar sob demanda:

```python
# server/polar/checkout/service.py

async def _ensure_product_in_gateway(
    self,
    session: AsyncSession,
    product: Product,
    gateway: PaymentProcessor,
) -> None:
    """Garante que produto existe no gateway (lazy creation)."""
    
    if gateway == PaymentProcessor.pagarme:
        if product.pagarme_product_id is None:
            # Criar produto no Pagar.me
            provider = PaymentProviderRegistry.get(PaymentProcessor.pagarme)
            # TODO: Implementar create_product no provider
            pagarme_product = await provider.create_product(product)
            product.pagarme_product_id = pagarme_product["id"]
            session.add(product)
            await session.flush()
    
    elif gateway == PaymentProcessor.stripe:
        if product.stripe_product_id is None:
            # Criar produto no Stripe
            # ... lógica similar ...
            pass
```

---

## 📈 Roadmap de Implementação

### ✅ FASE 1: Stripe Opcional (CONCLUÍDA)
- [x] Tornar Stripe opcional na criação de produtos
- [x] Tratamento de erros robusto
- [x] Logs estruturados

### 🔄 FASE 2: Pagar.me + PIX (EM PROGRESSO)
- [ ] Adicionar campos `pagarme_product_id`, `pagarme_plan_id` ao modelo Product
- [ ] Criar migration
- [ ] Implementar método `create_pix_payment()` no PagarmeProvider
- [ ] Implementar `_determine_payment_processor()` no CheckoutService
- [ ] Modificar criação de checkout para ser dinâmica
- [ ] Implementar lazy creation de produtos
- [ ] Testar checkout com Pagar.me sandbox
- [ ] Documentar

### 🚀 FASE 3: Frontend PIX
- [ ] Componente PIXCheckout
- [ ] QR Code display
- [ ] Copiar código PIX
- [ ] Polling de status
- [ ] Feedback visual

### 🌟 FASE 4: Mais Gateways
- [ ] Mercado Pago
- [ ] PicPay
- [ ] Outros gateways brasileiros

---

## 🎯 Estratégia de Negócio

### Por que Múltiplos Gateways?

1. **Redundância**: Se um gateway cair, outros continuam funcionando
2. **Otimização de Cusas**: Escolher gateway com menor taxa por transação
3. **Cobertura Regional**: Gateways locais têm melhores taxas e métodos de pagamento
4. **Compliance**: Alguns mercados exigem gateways locais

### Por que Pagar.me?

1. **PIX Nativo**: Suporte completo ao PIX (pagamento instantâneo)
2. **Taxas Competitivas**: Taxas menores que Stripe para Brasil
3. **Boleto Bancário**: Método de pagamento popular no Brasil
4. **Recorrência Local**: Melhor suporte para assinaturas no Brasil
5. **Compliance BR**: Totalmente regulado no Brasil

### Por que PIX?

1. **Instantâneo**: Pagamento confirmado em segundos
2. **Zero Taxa** para o comprador
3. **24/7**: Funciona em finais de semana e feriados
4. **Adoção Massiva**: 140+ milhões de brasileiros usam PIX
5. **Conversão**: Maior taxa de conversão que cartão de crédito

---

## 📊 Métricas de Sucesso

### KPIs Técnicos
- [ ] Checkout funciona com Pagar.me
- [ ] Checkout funciona com Stripe
- [ ] PIX gera QR Code corretamente
- [ ] Webhooks processam pagamentos
- [ ] Tempo de resposta < 500ms
- [ ] Uptime > 99.9%

### KPIs de Negócio
- [ ] Taxa de conversão com PIX > cartão de crédito
- [ ] Custo por transação < Stripe
- [ ] Tempo de liquidação < 2 dias úteis
- [ ] Chargebacks < 0.5%

---

## 📚 Referências

- **Pagar.me API**: https://docs.pagar.me/reference/api-reference
- **PIX Specification**: https://www.bcb.gov.br/estabilidadefinanceira/pix
- **Stripe API**: https://docs.stripe.com/api
- **Payment Provider Pattern**: https://martinfowler.com/eaaCatalog/gateway.html

---

**Versão**: 1.0  
**Data**: 2025-11-10  
**Status**: 🔄 Em Desenvolvimento - FASE 2  
**Objetivo**: Tornar Fluu a plataforma líder de pagamentos para SaaS no Brasil

