# Variáveis de ambiente para Frontend Next.js no Google Cloud Run
# IMPORTANTE: Substitua os valores abaixo com suas credenciais reais

# ============================================
# CONFIGURAÇÕES BÁSICAS
# ============================================
NODE_ENV: production
NEXT_PUBLIC_ENVIRONMENT: production
NEXT_PUBLIC_VERCEL_ENV: production
NEXT_TELEMETRY_DISABLED: "1"

# ============================================
# URL DO BACKEND API
# ============================================
# IMPORTANTE: Atualizar após deploy do backend
NEXT_PUBLIC_API_URL: https://api.fluu.digital

# ============================================
# URL DO FRONTEND
# ============================================
# IMPORTANTE: Usar domínio customizado
NEXT_PUBLIC_FRONTEND_BASE_URL: https://fluu.digital

# ============================================
# AUTENTICAÇÃO
# ============================================
# Atualizado em: 2025-11-10 - Correção do nome do cookie
POLAR_AUTH_COOKIE_KEY: fluu_session
POLAR_AUTH_MCP_COOKIE_KEY: polar_mcp_session
NEXT_PUBLIC_LOGIN_PATH: /login

# ============================================
# GITHUB (se necessário)
# ============================================
NEXT_PUBLIC_GITHUB_APP_NAMESPACE: polar-sh
NEXT_PUBLIC_GITHUB_BADGE_EMBED_DEFAULT_LABEL: Fund

# ============================================
# MONITORAMENTO (opcional)
# ============================================
# NEXT_PUBLIC_SENTRY_DSN: YOUR_SENTRY_DSN
# NEXT_PUBLIC_POSTHOG_TOKEN: YOUR_POSTHOG_TOKEN

# ============================================
# STRIPE (se necessário)
# ============================================
# NEXT_PUBLIC_STRIPE_KEY: YOUR_STRIPE_PUBLIC_KEY

# ============================================
# S3 (se necessário)
# ============================================
# S3_PUBLIC_IMAGES_BUCKET_HOSTNAME: YOUR_S3_HOSTNAME
# S3_PUBLIC_IMAGES_BUCKET_PROTOCOL: https
# S3_PUBLIC_IMAGES_BUCKET_PORT: ""
# S3_PUBLIC_IMAGES_BUCKET_PATHNAME: "**"

# ============================================
# CHECKOUT (se necessário)
# ============================================
# NEXT_PUBLIC_CHECKOUT_EMBED_SCRIPT_SRC: node_modules/@polar-sh/checkout/dist/embed.global.js

# ============================================
# APPLE PAY (se necessário)
# ============================================
# NEXT_PUBLIC_APPLE_DOMAIN_ASSOCIATION: YOUR_APPLE_DOMAIN_ASSOCIATION

