#!/bin/bash
set -e

echo "🔧 =============================================="
echo "   DEPLOY DE CORREÇÕES DE AUTENTICAÇÃO"
echo "   - Google OAuth configurado"
echo "   - <PERSON><PERSON> session corrigido (fluu_session)"
echo "   - API Route proxy criada"
echo "=============================================="
echo ""

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Verificar se estamos no diretório correto
if [ ! -f "deploy/cloud-run/env.yaml" ]; then
    echo -e "${RED}❌ Erro: Execute este script a partir da raiz do projeto${NC}"
    exit 1
fi

echo -e "${BLUE}📋 Verificando arquivos de configuração...${NC}"

# Verificar variáveis críticas nos arquivos env
echo ""
echo "🔍 Validando env.yaml (Backend)..."
if grep -q "POLAR_USER_SESSION_COOKIE_KEY: fluu_session" deploy/cloud-run/env.yaml; then
    echo -e "${GREEN}  ✅ POLAR_USER_SESSION_COOKIE_KEY: fluu_session${NC}"
else
    echo -e "${RED}  ❌ POLAR_USER_SESSION_COOKIE_KEY não está configurado corretamente${NC}"
    exit 1
fi

if grep -q "POLAR_USER_SESSION_COOKIE_DOMAIN: .fluu.digital" deploy/cloud-run/env.yaml; then
    echo -e "${GREEN}  ✅ POLAR_USER_SESSION_COOKIE_DOMAIN: .fluu.digital${NC}"
else
    echo -e "${RED}  ❌ POLAR_USER_SESSION_COOKIE_DOMAIN não está configurado corretamente${NC}"
    exit 1
fi

if grep -q "POLAR_GOOGLE_CLIENT_ID: 923457232981" deploy/cloud-run/env.yaml; then
    echo -e "${GREEN}  ✅ POLAR_GOOGLE_CLIENT_ID configurado${NC}"
else
    echo -e "${RED}  ❌ POLAR_GOOGLE_CLIENT_ID não encontrado${NC}"
    exit 1
fi

if grep -q "POLAR_GOOGLE_CLIENT_SECRET: 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" deploy/cloud-run/env.yaml; then
    echo -e "${GREEN}  ✅ POLAR_GOOGLE_CLIENT_SECRET atualizado${NC}"
else
    echo -e "${YELLOW}  ⚠️  POLAR_GOOGLE_CLIENT_SECRET pode estar desatualizado${NC}"
fi

echo ""
echo "🔍 Validando env-worker.yaml (Worker)..."
if grep -q "POLAR_USER_SESSION_COOKIE_KEY: fluu_session" deploy/cloud-run/env-worker.yaml; then
    echo -e "${GREEN}  ✅ Worker: POLAR_USER_SESSION_COOKIE_KEY configurado${NC}"
else
    echo -e "${RED}  ❌ Worker não tem POLAR_USER_SESSION_COOKIE_KEY${NC}"
    exit 1
fi

if grep -q "POLAR_GOOGLE_CLIENT_ID: 923457232981" deploy/cloud-run/env-worker.yaml; then
    echo -e "${GREEN}  ✅ Worker: POLAR_GOOGLE_CLIENT_ID configurado${NC}"
else
    echo -e "${RED}  ❌ Worker não tem POLAR_GOOGLE_CLIENT_ID${NC}"
    exit 1
fi

echo ""
echo "🔍 Validando env-frontend.yaml (Frontend)..."
if grep -q "POLAR_AUTH_COOKIE_KEY: fluu_session" deploy/cloud-run/env-frontend.yaml; then
    echo -e "${GREEN}  ✅ Frontend: POLAR_AUTH_COOKIE_KEY: fluu_session${NC}"
else
    echo -e "${RED}  ❌ Frontend não tem POLAR_AUTH_COOKIE_KEY correto${NC}"
    exit 1
fi

echo ""
echo -e "${GREEN}✅ Todas as variáveis de ambiente estão configuradas corretamente!${NC}"
echo ""

# Menu de opções
echo -e "${BLUE}📦 O que você quer deployar?${NC}"
echo "  1) Backend apenas"
echo "  2) Worker apenas"
echo "  3) Frontend apenas"
echo "  4) Backend + Worker"
echo "  5) TUDO (Backend + Worker + Frontend)"
echo "  6) Apenas atualizar variáveis de ambiente (sem rebuild)"
echo ""
read -p "Escolha uma opção (1-6): " OPCAO

case $OPCAO in
    1)
        echo -e "${BLUE}🚀 Deployando Backend...${NC}"
        ./deploy/cloud-run/deploy-backend.sh
        ;;
    2)
        echo -e "${BLUE}🚀 Deployando Worker...${NC}"
        ./deploy/cloud-run/deploy-worker.sh
        ;;
    3)
        echo -e "${BLUE}🚀 Deployando Frontend...${NC}"
        ./deploy/cloud-run/deploy-frontend.sh
        ;;
    4)
        echo -e "${BLUE}🚀 Deployando Backend + Worker...${NC}"
        ./deploy/cloud-run/deploy-backend.sh
        echo ""
        echo -e "${GREEN}✅ Backend concluído! Iniciando Worker...${NC}"
        echo ""
        ./deploy/cloud-run/deploy-worker.sh
        ;;
    5)
        echo -e "${BLUE}🚀 Deployando TUDO...${NC}"
        ./deploy/cloud-run/deploy-backend.sh
        echo ""
        echo -e "${GREEN}✅ Backend concluído! Iniciando Worker...${NC}"
        echo ""
        ./deploy/cloud-run/deploy-worker.sh
        echo ""
        echo -e "${GREEN}✅ Worker concluído! Iniciando Frontend...${NC}"
        echo ""
        ./deploy/cloud-run/deploy-frontend.sh
        ;;
    6)
        echo -e "${BLUE}🔄 Atualizando apenas variáveis de ambiente...${NC}"
        echo ""
        
        echo "📝 Atualizando Backend..."
        gcloud run services update fluu-api \
          --region us-east1 \
          --env-vars-file=deploy/cloud-run/env.yaml
        
        echo ""
        echo "📝 Atualizando Worker..."
        gcloud run services update fluu-worker \
          --region us-east1 \
          --env-vars-file=deploy/cloud-run/env-worker.yaml
        
        echo ""
        echo "📝 Atualizando Frontend..."
        gcloud run services update fluu-web \
          --region us-east1 \
          --env-vars-file=deploy/cloud-run/env-frontend.yaml
        
        echo ""
        echo -e "${GREEN}✅ Variáveis de ambiente atualizadas em todos os serviços!${NC}"
        ;;
    *)
        echo -e "${RED}❌ Opção inválida${NC}"
        exit 1
        ;;
esac

echo ""
echo -e "${GREEN}✅ Deploy concluído!${NC}"
echo ""
echo -e "${YELLOW}📝 PRÓXIMOS PASSOS:${NC}"
echo ""
echo "1. Configure as URLs no Google Cloud Console:"
echo "   https://console.cloud.google.com/apis/credentials"
echo ""
echo "   Redirect URIs autorizadas:"
echo "   - https://api.fluu.digital/v1/integrations/google/callback"
echo ""
echo "   Origens JavaScript autorizadas:"
echo "   - https://fluu.digital"
echo "   - https://api.fluu.digital"
echo ""
echo "2. Teste o login:"
echo "   - https://fluu.digital/login"
echo "   - Teste login com email + OTP"
echo "   - Teste login com Google"
echo ""
echo "3. Verifique os logs se houver problemas:"
echo "   - Backend:  gcloud run services logs read fluu-api --region us-east1 --limit 50"
echo "   - Worker:   gcloud run services logs read fluu-worker --region us-east1 --limit 50"
echo "   - Frontend: gcloud run services logs read fluu-web --region us-east1 --limit 50"
echo ""


