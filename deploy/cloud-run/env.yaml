# Variáveis de ambiente para Google Cloud Run
# IMPORTANTE: Substitua os valores abaixo com suas credenciais reais

# ============================================
# CONFIGURAÇÕES BÁSICAS
# ============================================
POLAR_ENV: production
POLAR_LOG_LEVEL: INFO
POLAR_TESTING: "0"
POLAR_DEBUG: "0"

# ============================================
# DATABASE (Neon PostgreSQL)
# ============================================
# URL original: postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require
POLAR_POSTGRES_USER: neondb_owner
POLAR_POSTGRES_PWD: npg_iX2kVBloh1YT
POLAR_POSTGRES_HOST: ep-dry-cell-achqzu4j-pooler.sa-east-1.aws.neon.tech
POLAR_POSTGRES_PORT: "5432"
POLAR_POSTGRES_DATABASE: neondb
POLAR_POSTGRES_SSLMODE: require

# ============================================
# REDIS (Upstash)
# ============================================
# IMPORTANTE: Upstash requer TLS, então usamos rediss://
# URL original: redis://default:<EMAIL>:6379
# Para TLS, precisamos usar: rediss://
POLAR_REDIS_HOST: joint-hookworm-6489.upstash.io
POLAR_REDIS_PORT: "6379"
POLAR_REDIS_DB: "0"
POLAR_REDIS_PASSWORD: ARlZAAImcDI5MzliMzdlMTQwZGQ0YWRjYWZlMWI4NWJkNDI1Y2RjNXAyNjQ4OQ
POLAR_REDIS_USERNAME: default
POLAR_REDIS_SSL: "true"

# ============================================
# APLICAÇÃO
# ============================================
# IMPORTANTE: Usar domínios customizados como URLs principais
POLAR_BASE_URL: https://api.fluu.digital
POLAR_FRONTEND_BASE_URL: https://fluu.digital
POLAR_CHECKOUT_BASE_URL: https://api.fluu.digital/v1/checkout-links/{client_secret}/redirect
POLAR_CORS_ORIGINS: '["https://fluu.digital", "https://api.fluu.digital", "https://fluu-web-iuu5qv6jja-ue.a.run.app", "https://fluu-api-iuu5qv6jja-ue.a.run.app"]'
POLAR_ALLOWED_HOSTS: '["fluu.digital", "api.fluu.digital", "fluu-api-iuu5qv6jja-ue.a.run.app", "fluu-web-iuu5qv6jja-ue.a.run.app"]'

# ============================================
# SEGURANÇA (OBRIGATÓRIO - GERE NOVOS VALORES)
# ============================================
POLAR_SECRET: 5HixFq0des4UY6YO1TMFthD_I0Ari3dAfmkh_54C_S8
POLAR_CURRENT_JWK_KID: production
POLAR_JWKS: ./.jwks.json
POLAR_USER_SESSION_COOKIE_DOMAIN: .fluu.digital
POLAR_USER_SESSION_COOKIE_KEY: fluu_session

# ============================================
# EMAIL (Resend ou outro provider)
# ============================================
POLAR_EMAIL_SENDER: resend
POLAR_RESEND_API_KEY: re_DWjnMKB5_8Hs6xJ6GF5DtCFgCWXTfGT4S
POLAR_EMAIL_FROM_NAME: Fluu
POLAR_EMAIL_FROM_DOMAIN: fluu.digital
POLAR_EMAIL_FROM_LOCAL: hello

# ============================================
# AWS S3 (para armazenamento de arquivos)
# ============================================
POLAR_AWS_ACCESS_KEY_ID: YOUR_AWS_ACCESS_KEY
POLAR_AWS_SECRET_ACCESS_KEY: YOUR_AWS_SECRET_KEY
POLAR_AWS_REGION: sa-east-1
POLAR_AWS_SIGNATURE_VERSION: v4
POLAR_S3_FILES_BUCKET_NAME: polar-files
POLAR_S3_FILES_PUBLIC_BUCKET_NAME: polar-public-files
POLAR_S3_FILES_DOWNLOAD_SALT: na8nJcdXC5n312-SnsFO-Q
POLAR_S3_FILES_DOWNLOAD_SECRET: na8nJcdXC5n312-SnsFO-Q
POLAR_S3_FILES_PRESIGN_TTL: "600"

# ============================================
# STRIPE (OBRIGATÓRIO - usando chave de teste)
# ============================================
# IMPORTANTE: Substitua com suas chaves reais do Stripe quando tiver
# Por enquanto, usando chave de teste do Stripe para desenvolvimento
POLAR_STRIPE_SECRET_KEY: sk_test_51QSqveP2dJiJQgz8YourStripeTestKeyHere
POLAR_STRIPE_PUBLISHABLE_KEY: pk_test_51QSqveP2dJiJQgz8YourStripePublicKeyHere
POLAR_STRIPE_WEBHOOK_SECRET: whsec_YourWebhookSecretHere

# ============================================
# GITHUB (se estiver usando)
# ============================================
POLAR_GITHUB_CLIENT_ID: YOUR_GITHUB_CLIENT_ID
POLAR_GITHUB_CLIENT_SECRET: YOUR_GITHUB_CLIENT_SECRET

# ============================================
# GOOGLE OAUTH (Login com Google)
# ============================================
# Atualizado em: 2025-11-10
# Redirect URI configurado no Google Cloud: https://api.fluu.digital/v1/integrations/google/callback
# Documentação completa: GOOGLE_OAUTH_SETUP.md
POLAR_GOOGLE_CLIENT_ID: 923457232981-aj399ub5qflffahehklunc9sudu93eeu.apps.googleusercontent.com
POLAR_GOOGLE_CLIENT_SECRET: 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

# ============================================
# PERFORMANCE
# ============================================
POLAR_DATABASE_POOL_SIZE: "10"
POLAR_DATABASE_POOL_RECYCLE_SECONDS: "600"
POLAR_DATABASE_COMMAND_TIMEOUT_SECONDS: "30.0"

# ============================================
# MONITORAMENTO (opcional)
# ============================================
# POLAR_SENTRY_DSN: YOUR_SENTRY_DSN
# POLAR_POSTHOG_PROJECT_API_KEY: YOUR_POSTHOG_KEY
# POLAR_LOGFIRE_TOKEN: YOUR_LOGFIRE_TOKEN

