# 🔧 Guia de Implementação de Novos Gateways

## 📋 Sumário

1. [<PERSON><PERSON><PERSON>eral](#visão-geral)
2. [Pré-requisitos](#pré-requisitos)
3. [Passo a Passo](#passo-a-passo)
4. [Checklist de Implementação](#checklist-de-implementação)
5. [Exemplos de Código](#exemplos-de-código)
6. [Testes](#testes)
7. [Deploy](#deploy)

---

## 🎯 Visão Geral

Este guia ensina como adicionar um novo gateway de pagamento ao Fluu (fork do Polar.sh), seguindo a arquitetura multi-gateway já estabelecida.

**Tempo estimado**: 4-8 horas (desenvolvedor experiente)

---

## 📚 Pré-requisitos

### Conhecimentos Necessários
- Python 3.11+ e FastAPI
- Async/await patterns
- HTTP/REST APIs
- Webhooks
- Git e GitHub

### Acesso Necessário
- Conta no gateway de pagamento (sandbox)
- API keys (secret e publishable)
- Documentação da API do gateway
- Webhook endpoint configurável

---

## 🚀 Passo a Passo

### Passo 1: Adicionar Enum do Processor

**Arquivo**: `server/polar/enums.py`

```python
class PaymentProcessor(StrEnum):
    stripe = "stripe"
    pagarme = "pagarme"
    mercadopago = "mercadopago"  # ← NOVO
```

**Commit**: `feat: add mercadopago to PaymentProcessor enum`

---

### Passo 2: Adicionar Configuração

**Arquivo**: `server/polar/config.py`

```python
class Settings(BaseSettings):
    # ... configurações existentes ...
    
    # Mercado Pago (NOVO)
    MERCADOPAGO_ACCESS_TOKEN: str = Field(default="")
    MERCADOPAGO_PUBLIC_KEY: str = Field(default="")
    ENABLE_MERCADOPAGO: bool = Field(default=False)
    MERCADOPAGO_WEBHOOK_SECRET: str | None = Field(default=None)
```

**Variáveis de ambiente** (`.env`):
```bash
POLAR_MERCADOPAGO_ACCESS_TOKEN=APP-xxx
POLAR_MERCADOPAGO_PUBLIC_KEY=APP-xxx
ENABLE_MERCADOPAGO=true
```

**Commit**: `feat: add mercadopago configuration settings`

---

### Passo 3: Criar Provider

**Diretório**: `server/polar/integrations/payment_providers/mercadopago/`

#### 3.1 Criar estrutura

```bash
cd server/polar/integrations/payment_providers
mkdir mercadopago
cd mercadopago
touch __init__.py provider.py schemas.py webhooks.py
```

#### 3.2 Implementar Provider

**Arquivo**: `server/polar/integrations/payment_providers/mercadopago/provider.py`

```python
"""Provider do Mercado Pago."""

import httpx
import structlog
from typing import Any, Optional

from polar.config import settings
from polar.enums import PaymentProcessor
from polar.models.payment import PaymentStatus

from ..base import PaymentProvider, PaymentProviderError

log = structlog.get_logger()


class MercadoPagoProvider(PaymentProvider):
    """Implementação do provider Mercado Pago."""

    def __init__(self):
        """Inicializa o provider."""
        self.base_url = "https://api.mercadopago.com"
        
        self.client = httpx.AsyncClient(
            base_url=self.base_url,
            headers={
                "Authorization": f"Bearer {settings.MERCADOPAGO_ACCESS_TOKEN}",
                "Content-Type": "application/json",
            },
            timeout=30.0,
        )

    @property
    def processor(self) -> PaymentProcessor:
        """Retorna o PaymentProcessor deste provider."""
        return PaymentProcessor.mercadopago

    async def create_customer(
        self,
        email: str,
        name: Optional[str] = None,
        metadata: Optional[dict[str, str]] = None,
    ) -> str:
        """Cria customer no Mercado Pago."""
        try:
            payload = {
                "email": email,
            }
            
            if name:
                name_parts = name.split(" ", 1)
                payload["first_name"] = name_parts[0]
                if len(name_parts) > 1:
                    payload["last_name"] = name_parts[1]
            
            response = await self.client.post("/v1/customers", json=payload)
            response.raise_for_status()
            customer_data = response.json()
            
            log.info(
                "Mercado Pago customer created",
                customer_id=customer_data["id"],
                email=email,
            )
            
            return str(customer_data["id"])
            
        except httpx.HTTPStatusError as e:
            error_msg = e.response.text if e.response else str(e)
            log.error(
                "Failed to create Mercado Pago customer",
                email=email,
                error=error_msg,
            )
            raise PaymentProviderError(
                f"Failed to create Mercado Pago customer: {error_msg}"
            ) from e

    async def create_payment_intent(
        self,
        amount: int,
        currency: str,
        customer_id: str,
        payment_method_id: Optional[str] = None,
        metadata: Optional[dict[str, str]] = None,
        description: Optional[str] = None,
        confirm: bool = False,
        off_session: bool = False,
        payment_method: str = "credit_card",
    ) -> dict[str, Any]:
        """Cria preferência de pagamento no Mercado Pago."""
        try:
            # Mercado Pago trabalha com valores decimais (não centavos)
            amount_decimal = amount / 100
            
            payload = {
                "transaction_amount": amount_decimal,
                "description": description or "Payment",
                "payment_method_id": payment_method,
                "payer": {
                    "id": customer_id,
                },
                "metadata": metadata or {},
            }
            
            if payment_method == "pix":
                payload["payment_method_id"] = "pix"
            
            response = await self.client.post("/v1/payments", json=payload)
            response.raise_for_status()
            payment_data = response.json()
            
            result = {
                "id": str(payment_data["id"]),
                "client_secret": str(payment_data["id"]),
                "status": self._map_status(payment_data.get("status", "pending")),
                "amount": amount,
                "currency": currency.upper(),
                "payment_method": payment_method,
            }
            
            # Dados específicos do PIX
            if payment_method == "pix" and payment_data.get("point_of_interaction"):
                pix_data = payment_data["point_of_interaction"]["transaction_data"]
                result["pix"] = {
                    "qr_code": pix_data.get("qr_code"),
                    "qr_code_base64": pix_data.get("qr_code_base64"),
                    "ticket_url": payment_data.get("transaction_details", {}).get("external_resource_url"),
                }
            
            log.info(
                "Mercado Pago payment created",
                payment_id=result["id"],
                amount=amount,
                payment_method=payment_method,
            )
            
            return result
            
        except httpx.HTTPStatusError as e:
            error_msg = e.response.text if e.response else str(e)
            log.error(
                "Failed to create Mercado Pago payment",
                error=error_msg,
            )
            raise PaymentProviderError(
                f"Failed to create Mercado Pago payment: {error_msg}"
            ) from e

    async def retrieve_payment(
        self,
        payment_id: str,
    ) -> dict[str, Any]:
        """Recupera informações de um pagamento."""
        try:
            response = await self.client.get(f"/v1/payments/{payment_id}")
            response.raise_for_status()
            payment_data = response.json()
            
            return {
                "id": str(payment_data["id"]),
                "status": payment_data.get("status"),
                "amount": int(payment_data.get("transaction_amount", 0) * 100),
                "currency": payment_data.get("currency_id", "BRL"),
                "payment_method": payment_data.get("payment_method_id", "unknown"),
            }
        except httpx.HTTPStatusError as e:
            error_msg = e.response.text if e.response else str(e)
            log.error(
                "Failed to retrieve Mercado Pago payment",
                payment_id=payment_id,
                error=error_msg,
            )
            raise PaymentProviderError(
                f"Failed to retrieve Mercado Pago payment: {error_msg}"
            ) from e

    async def handle_webhook(
        self,
        event_type: str,
        event_data: dict[str, Any],
    ) -> None:
        """Processa um webhook do Mercado Pago."""
        log.info(
            "Mercado Pago webhook received",
            event_type=event_type,
            event_id=event_data.get("id"),
        )

    def _map_status(self, mp_status: str) -> str:
        """Mapeia status do Mercado Pago para status padrão."""
        status_map = {
            "approved": "succeeded",
            "pending": "pending",
            "in_process": "pending",
            "rejected": "failed",
            "cancelled": "failed",
            "refunded": "refunded",
            "charged_back": "refunded",
        }
        return status_map.get(mp_status.lower(), "pending")

    # Implementar outros métodos obrigatórios...
```

**Commit**: `feat: implement MercadoPagoProvider with PIX support`

---

### Passo 4: Criar Webhooks

**Arquivo**: `server/polar/integrations/payment_providers/mercadopago/webhooks.py`

```python
"""Endpoints de webhook do Mercado Pago."""

import structlog
from fastapi import Depends, HTTPException, Request

from polar.enums import PaymentProcessor
from polar.integrations.payment_providers.registry import PaymentProviderRegistry
from polar.postgres import AsyncSession, get_db_session
from polar.routing import APIRouter

log = structlog.get_logger()

router = APIRouter(
    prefix="/integrations/mercadopago",
    tags=["integrations_mercadopago"],
    include_in_schema=False,
)


@router.post("/webhook", status_code=200)
async def webhook(
    request: Request,
    session: AsyncSession = Depends(get_db_session),
) -> dict[str, str]:
    """
    Endpoint para receber webhooks do Mercado Pago.
    
    Mercado Pago envia:
    {
        "type": "payment",
        "action": "payment.created",
        "data": { "id": "12345" }
    }
    """
    try:
        payload = await request.json()
        
        # Validar estrutura
        if "type" not in payload or "data" not in payload:
            raise HTTPException(status_code=400, detail="Invalid webhook payload")
        
        event_type = payload["type"]
        payment_id = payload.get("data", {}).get("id")
        
        log.info(
            "Mercado Pago webhook received",
            event_type=event_type,
            payment_id=payment_id,
        )
        
        # Processar apenas eventos de pagamento
        if event_type == "payment":
            provider = PaymentProviderRegistry.get(PaymentProcessor.mercadopago)
            
            # Buscar dados completos do pagamento
            payment_data = await provider.retrieve_payment(str(payment_id))
            
            # Processar pagamento
            await provider.handle_webhook(event_type, payment_data)
            
            return {"status": "ok", "payment_id": payment_id}
        else:
            return {"status": "ignored", "event_type": event_type}
            
    except Exception as e:
        log.error(
            "Error processing Mercado Pago webhook",
            error=str(e),
        )
        raise HTTPException(status_code=500, detail="Error processing webhook") from e
```

**Commit**: `feat: add mercadopago webhook endpoint`

---

### Passo 5: Registrar Provider

**Arquivo**: `server/polar/app.py` (ou onde providers são registrados)

```python
from polar.integrations.payment_providers.registry import PaymentProviderRegistry
from polar.integrations.payment_providers.mercadopago.provider import MercadoPagoProvider

# No startup da aplicação
if settings.ENABLE_MERCADOPAGO:
    PaymentProviderRegistry.register(
        PaymentProcessor.mercadopago,
        MercadoPagoProvider()
    )
    log.info("Mercado Pago provider registered")
```

**Commit**: `feat: register mercadopago provider on startup`

---

### Passo 6: Integrar no Checkout

**Arquivo**: `server/polar/checkout/service.py`

```python
def _determine_payment_processor(
    self,
    product: Product,
) -> PaymentProcessor:
    """Determina qual gateway usar para o checkout."""
    
    # 1. Preferência do produto
    if product.preferred_payment_processor:
        if PaymentProviderRegistry.is_supported(
            product.preferred_payment_processor
        ):
            return product.preferred_payment_processor
    
    # 2. Mercado Pago (prioridade LATAM)
    if settings.ENABLE_MERCADOPAGO and PaymentProviderRegistry.is_supported(
        PaymentProcessor.mercadopago
    ):
        return PaymentProcessor.mercadopago
    
    # 3. Pagar.me (prioridade Brasil)
    if settings.ENABLE_PAGARME and PaymentProviderRegistry.is_supported(
        PaymentProcessor.pagarme
    ):
        return PaymentProcessor.pagarme
    
    # 4. Fallback para Stripe
    if settings.STRIPE_SECRET_KEY:
        return PaymentProcessor.stripe
    
    raise PaymentProviderError("No payment gateway configured")
```

**Commit**: `feat: add mercadopago to payment processor selection`

---

### Passo 7: Criar Testes

**Arquivo**: `test_mercadopago.py`

```python
#!/usr/bin/env python3
"""Testes do Mercado Pago."""

import asyncio
from server.polar.integrations.payment_providers.mercadopago.provider import MercadoPagoProvider


async def test_connection():
    """Testa conexão com Mercado Pago."""
    provider = MercadoPagoProvider()
    
    # Criar customer
    customer_id = await provider.create_customer(
        email="<EMAIL>",
        name="Teste Fluu",
    )
    print(f"✅ Customer criado: {customer_id}")
    
    # Criar pagamento PIX
    payment = await provider.create_payment_intent(
        amount=10000,  # R$ 100
        currency="BRL",
        customer_id=customer_id,
        description="Teste PIX Mercado Pago",
        payment_method="pix",
    )
    print(f"✅ Pagamento PIX criado: {payment['id']}")
    
    if "pix" in payment:
        print(f"🎉 QR Code: {payment['pix']['qr_code'][:50]}...")


if __name__ == "__main__":
    asyncio.run(test_connection())
```

**Executar**:
```bash
python3 test_mercadopago.py
```

**Commit**: `test: add mercadopago integration tests`

---

## ✅ Checklist de Implementação

### Configuração
- [ ] Adicionar `PaymentProcessor` enum
- [ ] Adicionar configurações em `Settings`
- [ ] Adicionar variáveis de ambiente
- [ ] Documentar configuração em README

### Provider
- [ ] Criar diretório do provider
- [ ] Implementar `create_customer()`
- [ ] Implementar `create_payment_intent()`
- [ ] Implementar `retrieve_payment()`
- [ ] Implementar `create_refund()`
- [ ] Implementar `handle_webhook()`
- [ ] Implementar `_map_status()`
- [ ] Adicionar suporte a PIX (se aplicável)
- [ ] Adicionar logs estruturados

### Webhooks
- [ ] Criar endpoint de webhook
- [ ] Validar assinatura do webhook (se aplicável)
- [ ] Processar eventos principais
- [ ] Adicionar logs

### Integração
- [ ] Registrar provider no startup
- [ ] Adicionar ao `_determine_payment_processor()`
- [ ] Atualizar frontend (payment method selector)
- [ ] Adicionar metadata específico do gateway

### Testes
- [ ] Criar script de teste standalone
- [ ] Testar em sandbox/desenvolvimento
- [ ] Testar criação de customer
- [ ] Testar criação de pagamento
- [ ] Testar webhook
- [ ] Testar PIX (se aplicável)
- [ ] Testes E2E completos

### Documentação
- [ ] Atualizar `ARQUITETURA_MULTI_GATEWAY.md`
- [ ] Documentar configuração
- [ ] Documentar limitações
- [ ] Adicionar exemplos de uso
- [ ] Atualizar README

### Deploy
- [ ] Configurar variáveis de ambiente em produção
- [ ] Configurar webhook URL no gateway
- [ ] Deploy em staging
- [ ] Testes em staging
- [ ] Deploy em produção
- [ ] Monitorar logs e métricas

---

## 🧪 Testes

### Ambiente Sandbox

Todos os gateways fornecem ambiente sandbox:

| Gateway | Sandbox URL | Test Cards |
|---------|-------------|------------|
| Stripe | https://dashboard.stripe.com/test | 4242 4242 4242 4242 |
| Pagar.me | https://dashboard.pagar.me/ | 4111 1111 1111 1111 |
| Mercado Pago | https://www.mercadopago.com.br/developers | 5031 4332 1540 6351 |

### Cenários de Teste

1. **Conexão**: Autenticação e acesso à API
2. **Customer**: Criar, atualizar, buscar
3. **Payment Intent**: Criar com diferentes métodos
4. **PIX**: Gerar QR Code e validar formato
5. **Webhook**: Receber e processar eventos
6. **Refund**: Criar reembolso
7. **Error Handling**: Testar erros comuns

---

## 🚀 Deploy

### Checklist de Deploy

```bash
# 1. Configurar variáveis de ambiente
export POLAR_MERCADOPAGO_ACCESS_TOKEN="..."
export POLAR_MERCADOPAGO_PUBLIC_KEY="..."
export ENABLE_MERCADOPAGO=true

# 2. Testar localmente
python3 test_mercadopago.py

# 3. Deploy
./deploy/cloud-run/deploy-backend.sh

# 4. Configurar webhook no gateway
# URL: https://api.fluu.digital/integrations/mercadopago/webhook

# 5. Testar em produção
curl -X POST https://api.fluu.digital/v1/products/ ...

# 6. Monitorar
gcloud logging read "resource.type=cloud_run_revision" --limit 50
```

---

## 📊 Métricas de Qualidade

### Code Quality
- [ ] Cobertura de testes > 80%
- [ ] Sem erros de linting
- [ ] Documentação completa
- [ ] Logs estruturados

### Performance
- [ ] Latência P95 < 500ms
- [ ] Timeout configurável
- [ ] Retry com backoff exponencial
- [ ] Circuit breaker (opcional)

### Reliability
- [ ] Error handling robusto
- [ ] Validação de entrada
- [ ] Logs de erro detalhados
- [ ] Fallback para outros gateways

---

## 🎓 Recursos Adicionais

### Documentação dos Gateways
- **Stripe**: https://docs.stripe.com/api
- **Pagar.me**: https://docs.pagar.me/
- **Mercado Pago**: https://www.mercadopago.com.br/developers/

### Padrões de Design
- **Strategy Pattern**: Trocar provider em runtime
- **Factory Pattern**: Criar provider baseado em configuração
- **Adapter Pattern**: Adaptar APIs diferentes para interface comum

### Boas Práticas
- ✅ Async/await para I/O bound operations
- ✅ Structured logging (structlog)
- ✅ Typing hints em todo código
- ✅ Docstrings em métodos públicos
- ✅ Error handling com context
- ✅ Retry lógica para operações idempotentes

---

## 🤝 Contribuindo

Se você implementou um novo gateway, considere:

1. Abrir PR no repositório
2. Adicionar testes completos
3. Atualizar documentação
4. Compartilhar aprendizados

---

**Versão**: 1.0  
**Data**: 2025-11-10  
**Autor**: Equipe Fluu  
**Licença**: MIT

