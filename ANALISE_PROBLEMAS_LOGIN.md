# Análise Completa dos Problemas de Login e Worker

## 🔍 Problemas Identificados

### 1. **Problema: Jobs Presos na Fila do Worker Relacionados ao Stripe**

**Causa Raiz:**
- O sistema ainda tem endpoints do Stripe ativos (`/integrations/stripe/webhook`)
- Se webhooks do Stripe chegarem, eles criam jobs `stripe.webhook.*` que podem falhar
- As tasks do Stripe ainda estão registradas no worker (`server/polar/integrations/stripe/tasks.py`)
- Jobs podem estar presos tentando processar eventos do Stripe que não existem mais

**Evidências:**
- `server/polar/integrations/stripe/endpoints.py` ainda está ativo e enfileirando eventos
- `server/polar/tasks.py` importa `stripe` tasks
- `server/polar/integrations/stripe/tasks.py` tem múltiplos actors registrados

**Impacto:**
- Jobs presos na fila podem bloquear o processamento de outros jobs (incluindo emails)
- Worker pode estar tentando processar jobs do Stripe que falham repetidamente

### 2. **Problema: Envio de Email Não Funciona**

**Causa Raiz:**
- Worker pode não estar processando a fila `high_priority` onde emails são enfileirados
- Jobs presos do Stripe podem estar bloqueando o processamento
- Worker pode não estar rodando corretamente

**Fluxo de Email:**
1. `login_code_service.send()` chama `enqueue_email()`
2. `enqueue_email()` chama `enqueue_job("email.send", ...)`
3. Job é enfileirado na fila `high_priority`
4. Worker deve processar o job `email.send`
5. `email.send` task chama `email_sender.send()`

**Verificações Necessárias:**
- Worker está rodando?
- Worker está processando a fila `high_priority`?
- Há jobs presos bloqueando o processamento?

### 3. **Problema: Código de Verificação Não é Reconhecido**

**Causa Raiz:**
- Hash do código pode não estar sendo gerado corretamente
- `SECRET` pode estar diferente entre geração e verificação
- Código pode estar expirado
- Email pode estar sendo enviado com código diferente do que foi salvo

**Fluxo de Verificação:**
1. `login_code_service.request()` gera código e hash
2. Código é salvo no banco com hash
3. Código é enviado por email
4. Usuário insere código
5. `login_code_service.authenticate()` gera hash do código inserido
6. Compara hash com hash salvo no banco

**Verificações Necessárias:**
- `SECRET` está configurado corretamente?
- Hash está sendo gerado com o mesmo `SECRET`?
- Código não está expirado?
- Código enviado por email é o mesmo que foi salvo?

## 🛠️ Plano de Correção

### Fase 1: Limpar Jobs Presos do Stripe

1. **Desabilitar endpoints do Stripe** (temporariamente)
   - Comentar ou remover roteamento dos endpoints de webhook do Stripe
   - Isso evita novos jobs sendo criados

2. **Verificar e limpar jobs presos**
   - Verificar filas do Redis para jobs do Stripe
   - Limpar jobs presos manualmente se necessário

3. **Remover ou desabilitar tasks do Stripe** (temporariamente)
   - Comentar imports das tasks do Stripe em `tasks.py`
   - Isso evita que o worker tente processar jobs do Stripe

### Fase 2: Garantir Processamento de Emails

1. **Verificar configuração do worker**
   - Confirmar que `DRAMATIQ_QUEUES` está configurado corretamente
   - Verificar que worker está processando `high_priority` e `default`

2. **Testar envio de email**
   - Fazer request de login code
   - Verificar se job `email.send` é criado
   - Verificar se worker processa o job
   - Verificar se email é enviado

### Fase 3: Corrigir Verificação de Código

1. **Verificar configuração de SECRET**
   - Confirmar que `POLAR_SECRET` está configurado
   - Verificar que mesmo secret é usado em geração e verificação

2. **Adicionar logs para debug**
   - Logar código gerado (temporariamente)
   - Logar hash gerado
   - Logar hash comparado na verificação

3. **Testar fluxo completo**
   - Request login code
   - Verificar código recebido por email
   - Tentar autenticar com código
   - Verificar logs de erro

### Fase 4: Testar Login Completo

1. **Testar com usuário admin**
   - Email: `<EMAIL>`
   - Request login code
   - Verificar email recebido
   - Inserir código
   - Verificar login bem-sucedido

## 📋 Ações Imediatas

1. ✅ Desabilitar endpoints do Stripe
2. ✅ Verificar jobs presos na fila
3. ✅ Garantir que worker processa emails
4. ✅ Verificar configuração de SECRET
5. ✅ Testar login completo

## 🔧 Comandos Úteis

```bash
# Verificar tamanho das filas do Redis
redis-cli LLEN "dramatiq:high_priority"
redis-cli LLEN "dramatiq:default"

# Ver jobs na fila (primeiros 10)
redis-cli LRANGE "dramatiq:high_priority" 0 9
redis-cli LRANGE "dramatiq:default" 0 9

# Limpar fila (CUIDADO - só se necessário)
redis-cli DEL "dramatiq:high_priority"
redis-cli DEL "dramatiq:default"

# Verificar logs do worker
# (depende do ambiente de deploy)
```



