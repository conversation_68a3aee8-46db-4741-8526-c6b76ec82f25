# Teste de Envio de Email com Código de Acesso

## Status do Teste

### ✅ Configuração
- **Banco de Dados**: Neon (Produção) - Conectado
- **API Server**: Rodando na porta 8000
- **Worker**: Rodando em background
- **CORS**: Funcionando corretamente
- **EMAIL_SENDER**: `logger` (modo desenvolvimento)

### ⚠️ Rate Limiting
O endpoint `/v1/login-code/request` tem rate limiting ativo:
- **Limite**: 6 requisições por minuto, 12 por hora
- **Block Time**: 900 segundos (15 minutos) após exceder o limite
- **Status Atual**: Rate limiting ativo (429 Too Many Requests)

### 📧 Como Funciona o Envio de Email

1. **Request de Login Code** (`/v1/login-code/request`):
   - Cria um código de login no banco de dados
   - Renderiza o template de email
   - Enfileira o email para envio via worker

2. **Worker Processa o Email**:
   - O worker pega o job da fila
   - Chama `email_sender.send()`
   - Em modo desenvolvimento (`EMAIL_SENDER=logger`): Email é logado
   - Em modo produção (`EMAIL_SENDER=resend`): Email é enviado via Resend

3. **Logs do Email**:
   - Em modo desenvolvimento, o email será logado nos logs do worker
   - Procure por: `"Sending an email"` nos logs

### 🔍 Como Verificar se o Email Foi Enviado

#### 1. Verificar Logs do Worker
```bash
# Os logs do worker devem mostrar:
# "Sending an email" com os detalhes do email
```

#### 2. Verificar no Banco de Dados
```bash
cd server
docker compose exec db psql -U polar -d polar -c "SELECT email, expires_at FROM login_codes ORDER BY created_at DESC LIMIT 5;"
```

#### 3. Testar Novamente (Após Rate Limit)
```bash
# Aguardar o tempo de bloqueio (retry-after segundos)
# Ou limpar o Redis:
docker compose exec redis redis-cli FLUSHALL
```

### 📋 Teste Manual

Para testar manualmente após o rate limit:

1. **Aguardar o tempo de bloqueio** (verificar `retry-after` header)
2. **Ou limpar o Redis**:
   ```bash
   docker compose exec redis redis-cli FLUSHALL
   ```
3. **Fazer requisição**:
   ```bash
   curl -X POST http://127.0.0.1:8000/v1/login-code/request \
     -H "Origin: http://127.0.0.1:3000" \
     -H "Content-Type: application/json" \
     -d '{"email":"<EMAIL>"}'
   ```
4. **Verificar logs do worker** para confirmar o envio

### ✅ Resultado Esperado

Quando funcionar corretamente:
- **Status**: 202 Accepted
- **Email**: Enfileirado para envio
- **Worker**: Processa o email e loga (em modo desenvolvimento)
- **Logs**: Mostram `"Sending an email"` com detalhes

### 🔧 Configuração de Email

#### Modo Desenvolvimento (Atual)
```bash
EMAIL_SENDER=logger
```
- Email é logado, não enviado
- Verifique os logs do worker

#### Modo Produção
```bash
EMAIL_SENDER=resend
RESEND_API_KEY=sua_chave_aqui
```
- Email é enviado via Resend
- Requer chave API do Resend

### 📝 Notas

1. **Rate Limiting**: O rate limiting está muito agressivo para testes. Considere aumentar os limites em desenvolvimento.
2. **Redis**: O rate limiting usa Redis. Limpar o Redis remove o rate limiting temporariamente.
3. **Worker**: O worker precisa estar rodando para processar os emails enfileirados.



