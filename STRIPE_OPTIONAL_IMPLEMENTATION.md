# Implementação: Stripe Opcional na Criação de Produtos

## ✅ FASE 1 - CONCLUÍDA

### Mudanças Implementadas

#### 1. **Adicionado import do structlog**
- **Arquivo**: `server/polar/product/service.py`
- **Mudança**: Importado `structlog` para logging estruturado

#### 2. **<PERSON>éto<PERSON> `_should_create_in_stripe()`**
- **Arquivo**: `server/polar/product/service.py` (linha ~64-77)
- **Função**: Verifica se o Stripe está configurado
- **Lógica**:
  ```python
  def _should_create_in_stripe(self) -> bool:
      from polar.config import settings
      return bool(
          settings.STRIPE_SECRET_KEY
          and settings.STRIPE_SECRET_KEY != ""
          and not settings.STRIPE_SECRET_KEY.startswith("YOUR_")
      )
  ```

#### 3. **<PERSON><PERSON><PERSON><PERSON> `create()` - Criação condicional**
- **Arquivo**: `server/polar/product/service.py` (linha ~268-304)
- **Mudança**: Criação no Stripe é condicional e com tratamento de erros
- **Comportamento**:
  - ✅ **Se Stripe configurado**: Tenta criar produto e preços no Stripe
  - ✅ **Se Stripe não configurado**: Pula criação no Stripe, loga informação
  - ✅ **Se erro no Stripe**: Loga warning mas continua criação do produto
  - ✅ **Produto sempre é salvo no banco**, independente do Stripe

#### 4. **Método `update()` - Atualização condicional**
- **Arquivo**: `server/polar/product/service.py` (linha ~459-525)
- **Mudança**: Atualização de produto e preços no Stripe é condicional
- **Comportamento**:
  - ✅ Atualiza no Stripe apenas se configurado E produto tem `stripe_product_id`
  - ✅ Arquiva preços no Stripe apenas se configurado
  - ✅ Cria novos preços no Stripe apenas se configurado
  - ✅ Erros do Stripe não bloqueiam a atualização do produto

#### 5. **Métodos `_archive()` e `_unarchive()`**
- **Arquivo**: `server/polar/product/service.py` (linha ~727-768)
- **Mudança**: Arquivamento no Stripe é condicional
- **Comportamento**:
  - ✅ Arquiva/desarquiva no Stripe apenas se configurado
  - ✅ Erros não bloqueiam a operação

#### 6. **Logs informativos adicionados**
- ✅ "Product created in Stripe" com `product_id` e `stripe_product_id`
- ✅ "Stripe not configured, skipping Stripe product creation"
- ✅ "Failed to create product in Stripe, continuing without Stripe integration"
- ✅ "Product updated in Stripe"
- ✅ "Price created/archived in Stripe"

---

## 🎯 Resultado

### Antes (❌)
```
POST /v1/products/
Status: 500 Internal Server Error
Error: stripe._error.AuthenticationError
```

### Depois (✅)
```
POST /v1/products/
Status: 201 Created
{
  "id": "...",
  "stripe_product_id": null,  # <- Sem erro!
  ...
}
```

### Logs Esperados

**Com Stripe configurado:**
```
Product created in Stripe product_id=xxx stripe_product_id=prod_xxx
```

**Sem Stripe configurado:**
```
Stripe not configured, skipping Stripe product creation product_id=xxx
```

**Com erro no Stripe:**
```
Failed to create product in Stripe, continuing without Stripe integration 
  product_id=xxx error=... error_type=AuthenticationError
```

---

## ⚠️ LIMITAÇÕES CONHECIDAS (Para FASE 2)

### 1. Checkout ainda requer `stripe_product_id`

**Arquivo**: `server/polar/checkout/service.py` (linha ~2041)
```python
async def _create_ad_hoc_custom_price(...):
    assert checkout.product.stripe_product_id is not None  # ❌ Falha se None
```

**Impacto**: Se criar produto sem Stripe e tentar fazer checkout, vai falhar.

**Solução (FASE 2)**: Implementar **lazy creation** - criar no Stripe no momento do checkout se necessário:
```python
if checkout.product.stripe_product_id is None and self._should_create_in_stripe():
    # Criar produto no Stripe agora (lazy creation)
    stripe_product = await stripe_service.create_product(...)
    checkout.product.stripe_product_id = stripe_product.id
```

### 2. Subscription também requer `stripe_product_id`

**Arquivo**: `server/polar/subscription/service.py` (linha ~756)
```python
# New subscription
if subscription is None:
    assert product.stripe_product_id is not None  # ❌ Falha se None
```

**Impacto**: Se criar produto recorrente sem Stripe e tentar criar assinatura, vai falhar.

**Solução (FASE 2)**: Mesma lógica de lazy creation.

---

## 📋 PRÓXIMOS PASSOS (FASE 2)

### 1. Adicionar Campos ao Modelo Product

**Arquivo**: `server/polar/models/product.py`
```python
# Campos adicionais (NOVO)
pagarme_product_id: Mapped[str | None] = mapped_column(String, nullable=True, default=None)
pagarme_plan_id: Mapped[str | None] = mapped_column(String, nullable=True, default=None)
preferred_payment_processor: Mapped[PaymentProcessor | None] = mapped_column(
    Enum(PaymentProcessor), nullable=True, default=None
)
```

### 2. Criar Migration
```bash
cd server
uv run alembic revision --autogenerate -m "add pagarme fields to product"
uv run alembic upgrade head
```

### 3. Implementar Lazy Creation

**No checkout service**:
```python
async def _ensure_payment_gateway_product(
    self,
    session: AsyncSession,
    product: Product,
    gateway: PaymentProcessor
) -> None:
    """Cria produto no gateway se necessário (lazy creation)."""
    if gateway == PaymentProcessor.stripe and product.stripe_product_id is None:
        if self._should_create_in_stripe():
            # Criar produto no Stripe
            ...
    
    elif gateway == PaymentProcessor.pagarme and product.pagarme_product_id is None:
        if self._should_create_in_pagarme():
            # Criar produto no Pagar.me
            ...
```

### 4. Criar Serviço Pagar.me para Produtos

**Arquivo**: `server/polar/integrations/payment_providers/pagarme/product_service.py` (NOVO)
```python
class PagarmeProductService:
    async def create_product(self, product: Product, prices: list[ProductPrice]) -> dict:
        """Cria produto/plano no Pagar.me."""
        # Implementar lógica
        pass
```

### 5. Integrar no ProductService

```python
# Em create() e update()
if self._should_create_in_pagarme():
    await self._create_in_pagarme(session, product)
```

---

## 🧪 COMO TESTAR

### Teste 1: Criar produto SEM Stripe
```bash
# Comentar POLAR_STRIPE_SECRET_KEY no .env
cd server
uv run task api

# Em outro terminal
curl -X POST http://localhost:8000/v1/products/ \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Product",
    "prices": [{...}],
    "organization_id": "..."
  }'

# ✅ Deve retornar 201
# ✅ stripe_product_id deve ser null
# ✅ Log: "Stripe not configured, skipping..."
```

### Teste 2: Criar produto COM Stripe
```bash
# Descomentar POLAR_STRIPE_SECRET_KEY no .env
# Reiniciar servidor

# Criar produto (mesma requisição acima)

# ✅ Deve retornar 201
# ✅ stripe_product_id deve estar preenchido
# ✅ Log: "Product created in Stripe product_id=... stripe_product_id=prod_..."
```

### Teste 3: Stripe configurado mas falha
```bash
# Definir POLAR_STRIPE_SECRET_KEY inválida
export POLAR_STRIPE_SECRET_KEY="sk_test_invalid"

# Criar produto

# ✅ Deve retornar 201 (não 500!)
# ✅ stripe_product_id deve ser null
# ✅ Log: "Failed to create product in Stripe, continuing..."
```

### Teste 4: Produtos existentes continuam funcionando
```bash
# Com Stripe configurado
# Atualizar produto existente que tem stripe_product_id

curl -X PATCH http://localhost:8000/v1/products/$PRODUCT_ID \
  -d '{"name": "Updated Name"}'

# ✅ Deve atualizar no banco E no Stripe
# ✅ Log: "Product updated in Stripe"
```

---

## 🚀 DEPLOY

### Commit
```bash
cd /Users/<USER>/Documents/www/Gateways/polar

git add server/polar/product/service.py
git add STRIPE_OPTIONAL_IMPLEMENTATION.md

git commit -m "fix: make Stripe optional for product creation

- Product creation no longer requires Stripe configuration
- Stripe integration is now conditional based on settings
- Errors in Stripe don't block product creation
- Added structured logging for Stripe operations
- Preparing for multi-gateway support (Pagar.me, etc)

Fixes #XXX

BREAKING CHANGE: Products can now be created without stripe_product_id.
Checkout and subscription flows still require Stripe products to exist.
Lazy creation will be implemented in Phase 2.
"
```

### Deploy Backend
```bash
./deploy/cloud-run/deploy-backend.sh
```

### Monitorar Logs
```bash
# Após deploy, monitorar logs do Cloud Run
gcloud logging read "resource.type=cloud_run_revision AND resource.labels.service_name=YOUR_SERVICE" --limit 50

# Procurar por:
# - "Stripe not configured, skipping..."
# - "Product created in Stripe"
# - "Failed to create product in Stripe, continuing..."
```

---

## 📊 COMPATIBILIDADE

### ✅ Mantém Compatibilidade Com

- Produtos existentes com `stripe_product_id`
- Webhooks do Stripe
- Checkouts existentes
- Assinaturas existentes
- API pública (nenhuma mudança nos schemas)

### ⚠️ Mudanças de Comportamento

1. **Produtos novos podem ter `stripe_product_id = null`**
   - Antes: Sempre preenchido
   - Depois: Preenchido apenas se Stripe configurado

2. **Erros do Stripe não bloqueiam criação**
   - Antes: Erro 500
   - Depois: Produto criado, log de warning

3. **Checkout/Subscription ainda requerem Stripe**
   - Produtos sem `stripe_product_id` não podem ser usados em checkout (ainda)
   - Será resolvido na FASE 2 com lazy creation

---

## 🔍 ARQUIVOS MODIFICADOS

```
server/polar/product/service.py  (MODIFICADO - 8 mudanças)
STRIPE_OPTIONAL_IMPLEMENTATION.md (NOVO - este arquivo)
```

---

**Data**: 2025-11-10  
**Fase**: 1 (URGENTE) - ✅ CONCLUÍDA  
**Próxima Fase**: 2 (Suporte Pagar.me) - PENDENTE  
**Tempo de implementação**: ~30 minutos  

