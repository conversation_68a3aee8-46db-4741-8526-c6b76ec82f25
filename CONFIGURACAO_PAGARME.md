# 🔧 Configuração do Pagar.me

## 🎯 Objetivo

Este guia ensina como configurar o Pagar.me no Fluu para testar PIX e outros métodos de pagamento.

---

## 📋 Pré-requisitos

1. **Conta no Pagar.me** (gratuita)
2. **Acesso ao Dashboard**
3. **API Keys** (sandbox e produção)

---

## 🚀 Passo a Passo

### 1. Criar Conta no Pagar.me

1. Acesse: https://dashboard.pagar.me/signup
2. Preencha os dados:
   - Email
   - Nome completo
   - CPF/CNPJ
   - Telefone
3. Confirme email
4. Complete cadastro

### 2. Acessar Dashboard

1. Login: https://dashboard.pagar.me/
2. Vá para **Configurações** → **API Keys**
3. Ou acesse direto: https://dashboard.pagar.me/settings/api-keys

### 3. Obter API Keys

Você verá duas chaves:

```
┌─────────────────────────────────────────────────────┐
│  AMBIENTE SANDBOX (TESTE)                           │
├─────────────────────────────────────────────────────┤
│  Secret Key:      sk_test_xxxxxxxxxxxxxxxxxxxxx     │
│  Publishable Key: pk_test_xxxxxxxxxxxxxxxxxxxxx     │
└─────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────┐
│  AMBIENTE PRODUÇÃO (REAL)                           │
├─────────────────────────────────────────────────────┤
│  Secret Key:      sk_live_xxxxxxxxxxxxxxxxxxxxx     │
│  Publishable Key: pk_live_xxxxxxxxxxxxxxxxxxxxx     │
└─────────────────────────────────────────────────────┘
```

**⚠️ IMPORTANTE**: 
- Use **SANDBOX** para testes
- **NUNCA** commite as keys no Git
- Mantenha Secret Key privada

### 4. Configurar no Fluu

#### Opção A: Usando arquivo .env (Recomendado)

Edite `server/.env`:

```bash
# Pagar.me (Sandbox)
POLAR_PAGARME_SECRET_KEY=sk_test_SUA_CHAVE_AQUI
POLAR_PAGARME_PUBLISHABLE_KEY=pk_test_SUA_CHAVE_AQUI
ENABLE_PAGARME=true

# Webhook (opcional para testes locais)
POLAR_PAGARME_WEBHOOK_SECRET=
```

#### Opção B: Usando variáveis de ambiente

```bash
export POLAR_PAGARME_SECRET_KEY=sk_test_SUA_CHAVE_AQUI
export POLAR_PAGARME_PUBLISHABLE_KEY=pk_test_SUA_CHAVE_AQUI
export ENABLE_PAGARME=true
```

### 5. Verificar Configuração

```bash
cd /Users/<USER>/Documents/www/Gateways/polar

# Verificar se .env tem as keys
grep PAGARME server/.env

# Deve mostrar:
# POLAR_PAGARME_SECRET_KEY=sk_test_xxx
# POLAR_PAGARME_PUBLISHABLE_KEY=pk_test_xxx
# ENABLE_PAGARME=true
```

### 6. Testar Conexão

```bash
python3 test_pagarme_pix.py
```

**Saída esperada**:

```
🔑 Usando API Key: sk_test_xxx...

╔═══════════════════════════════════════════════════════════════════╗
║         🧪 TESTE PAGAR.ME + PIX - FLUU (SANDBOX)                ║
╚═══════════════════════════════════════════════════════════════════╝

======================================================================
🔌 TESTE 1: Conexão com Pagar.me
======================================================================
✅ Conexão com Pagar.me estabelecida
   Status: 200
   API Key: sk_test_xxx...

======================================================================
👤 TESTE 2: Criar Customer
======================================================================
✅ Customer criado com sucesso
   ID: cus_xxx
   Email: <EMAIL>

======================================================================
💰 TESTE 3: Criar Pagamento PIX
======================================================================
✅ Order criada com sucesso
   Order ID: or_xxx
   Status: pending
   Charge ID: ch_xxx
   Charge Status: pending

🎉 PIX GERADO COM SUCESSO!
   ╔══════════════════════════════════════════╗
   ║  QR Code PIX                             ║
   ╚══════════════════════════════════════════╝
   Código PIX (copie e cole):
   00020126...

   📱 URL do QR Code: https://...
   ⏰ Expira em: 2025-11-10T...

   💡 Abra o app do seu banco e escaneie o QR Code
```

---

## 🧪 Testando PIX no Sandbox

### Como Funciona o Sandbox

No ambiente sandbox do Pagar.me:

1. **PIX é simulado** - não movimenta dinheiro real
2. **QR Code é gerado** - mas não pode ser pago de verdade
3. **Webhooks funcionam** - você pode testar callbacks
4. **Status pode ser alterado manualmente** via API

### Testar Pagamento Completo

Para simular um pagamento PIX aprovado no sandbox:

```bash
# 1. Criar PIX (já fizemos no teste)
python3 test_pagarme_pix.py

# 2. Anotar o charge_id gerado
# charge_id: ch_xxxxxxxxxxxxx

# 3. Aprovar manualmente via API (curl)
curl -X POST https://api.pagar.me/core/v5/charges/ch_xxxxxxxxxxxxx/confirm \
  -H "Authorization: Basic $(echo -n 'sk_test_SUA_CHAVE:' | base64)" \
  -H "Content-Type: application/json" \
  -d '{}'

# 4. Verificar status
python3 -c "
import asyncio
from test_pagarme_pix import test_check_payment_status
asyncio.run(test_check_payment_status('ch_xxxxxxxxxxxxx'))
"
```

### Cartões de Teste

Para testar cartão de crédito no sandbox:

```
Cartão Aprovado:   4111 1111 1111 1111
Cartão Recusado:   4000 0000 0000 0002
CVV:               123
Validade:          12/2030
Nome:              Teste da Silva
```

---

## 📊 Dashboard Pagar.me

### Visualizar Transações

1. Acesse: https://dashboard.pagar.me/transactions
2. Alterne para **Sandbox** (canto superior direito)
3. Veja todas as transações de teste

### Configurar Webhooks

1. Vá para: https://dashboard.pagar.me/settings/webhooks
2. Adicionar URL: `https://sua-api.com/integrations/pagarme/webhook`
3. Selecionar eventos:
   - `charge.paid`
   - `charge.pending`
   - `charge.failed`
   - `charge.refunded`

**Para testes locais**, use ngrok:

```bash
# Instalar ngrok
brew install ngrok

# Expor porta 8000
ngrok http 8000

# Copiar URL fornecida (ex: https://abc123.ngrok.io)
# Configurar webhook: https://abc123.ngrok.io/integrations/pagarme/webhook
```

---

## 🔐 Segurança

### Boas Práticas

1. **Nunca commite API keys** no Git
   ```bash
   # Adicionar ao .gitignore
   echo "server/.env" >> .gitignore
   ```

2. **Use .env para desenvolvimento**
   ```bash
   # .env (não commitado)
   POLAR_PAGARME_SECRET_KEY=sk_test_xxx
   ```

3. **Use variáveis de ambiente em produção**
   ```bash
   # No Cloud Run, Railway, etc
   gcloud run services update fluu-api \
     --set-env-vars POLAR_PAGARME_SECRET_KEY=sk_live_xxx
   ```

4. **Valide assinatura de webhooks**
   ```python
   # Em produção, sempre validar
   if settings.PAGARME_WEBHOOK_SECRET:
       signature = request.headers.get("X-Hub-Signature")
       if not validate_signature(payload, signature):
           raise HTTPException(401, "Invalid signature")
   ```

---

## 🐛 Troubleshooting

### Erro: "Authorization has been denied"

**Causa**: API key inválida ou não configurada

**Solução**:
1. Verificar se API key está correta no .env
2. Verificar se está usando `sk_test_` (não `pk_test_`)
3. Gerar nova API key no dashboard
4. Reiniciar servidor

```bash
# Verificar configuração
cd server
grep PAGARME .env

# Deve ter:
# POLAR_PAGARME_SECRET_KEY=sk_test_xxx (começando com sk_test_)
```

### Erro: "Customer creation failed"

**Causa**: Dados inválidos (CPF, email, etc)

**Solução**:
```python
# CPF de teste válido
document = "12345678909"  # ✅ Formato válido

# Email único
email = f"teste+{random.randint(1000,9999)}@fluu.digital"
```

### Erro: "PIX QR Code not generated"

**Causa**: Sandbox pode ter limitações

**Solução**:
1. Verificar response completa da API
2. Usar API key de produção (para testes reais)
3. Verificar logs do Pagar.me

### Webhook não chega

**Causa**: URL não acessível ou não HTTPS

**Solução**:
```bash
# Para dev local, use ngrok
ngrok http 8000

# Configurar URL no dashboard:
# https://abc123.ngrok.io/integrations/pagarme/webhook
```

---

## 📚 Recursos

### Documentação Oficial
- **API Reference**: https://docs.pagar.me/reference
- **Guia de PIX**: https://docs.pagar.me/docs/pix-introduction
- **Webhooks**: https://docs.pagar.me/docs/webhooks-1
- **Dashboard**: https://dashboard.pagar.me/

### Suporte
- **Chat**: No dashboard (canto inferior direito)
- **Email**: <EMAIL>
- **Status**: https://status.pagar.me/

### Comunidade
- **Discord Fluu**: [em breve]
- **GitHub Issues**: [link do repo]

---

## ✅ Checklist de Configuração

- [ ] Conta criada no Pagar.me
- [ ] API Keys obtidas (sandbox)
- [ ] Keys configuradas no .env
- [ ] ENABLE_PAGARME=true
- [ ] .env no .gitignore
- [ ] Teste de conexão passou
- [ ] Customer criado com sucesso
- [ ] PIX QR Code gerado
- [ ] Webhook configurado (opcional)
- [ ] Testado no frontend (opcional)

---

## 🎯 Próximos Passos

Após configurar o Pagar.me:

1. **Testar fluxo completo**
   ```bash
   python3 test_pagarme_pix.py
   ```

2. **Integrar no frontend**
   - Criar componente PIXCheckout
   - Exibir QR Code
   - Polling de status

3. **Deploy em staging**
   - Configurar keys em staging
   - Testar webhooks
   - Validar fluxo E2E

4. **Produção**
   - Trocar para keys de produção
   - Configurar webhooks produção
   - Monitorar métricas

---

**Data**: 2025-11-10  
**Versão**: 1.0  
**Status**: 📖 Documentação Completa


