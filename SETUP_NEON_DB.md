# Configuração do Banco de Dados Neon (Produção)

## Configuração Aplicada

O servidor foi configurado para usar o banco de dados de produção do Neon:

### Variáveis de Ambiente Configuradas

```bash
POLAR_POSTGRES_HOST=ep-dry-cell-achqzu4j-pooler.sa-east-1.aws.neon.tech
POLAR_POSTGRES_PORT=5432
POLAR_POSTGRES_USER=neondb_owner
POLAR_POSTGRES_PWD=npg_iX2kVBloh1YT
POLAR_POSTGRES_DATABASE=neondb
POLAR_POSTGRES_SSLMODE=require
```

### Status dos Serviços

- ✅ **API Server**: Rodando na porta 8000
- ✅ **Worker**: Rodando em background
- ✅ **CORS**: Configurado e funcionando
- ✅ **Banco de Dados**: Conectado ao Neon (produção)

### Comandos para Iniciar Serviços

#### API Server
```bash
cd server
POLAR_POSTGRES_USER=neondb_owner \
POLAR_POSTGRES_PWD=npg_iX2kVBloh1YT \
POLAR_POSTGRES_HOST=ep-dry-cell-achqzu4j-pooler.sa-east-1.aws.neon.tech \
POLAR_POSTGRES_DATABASE=neondb \
POLAR_POSTGRES_SSLMODE=require \
uv run task api
```

#### Worker
```bash
cd server
POLAR_POSTGRES_USER=neondb_owner \
POLAR_POSTGRES_PWD=npg_iX2kVBloh1YT \
POLAR_POSTGRES_HOST=ep-dry-cell-achqzu4j-pooler.sa-east-1.aws.neon.tech \
POLAR_POSTGRES_DATABASE=neondb \
POLAR_POSTGRES_SSLMODE=require \
uv run task worker
```

### Notas Importantes

1. **SSL Mode**: O banco do Neon requer SSL (`require`)
2. **Rate Limiting**: O servidor tem rate limiting ativo (pode retornar 429)
3. **Backup**: O arquivo `.env` original foi salvo como `.env.backup`

### Teste de Conexão

Para testar a conexão com o banco:
```bash
cd server
POLAR_POSTGRES_USER=neondb_owner \
POLAR_POSTGRES_PWD=npg_iX2kVBloh1YT \
POLAR_POSTGRES_HOST=ep-dry-cell-achqzu4j-pooler.sa-east-1.aws.neon.tech \
POLAR_POSTGRES_DATABASE=neondb \
POLAR_POSTGRES_SSLMODE=require \
uv run task db_migrate
```

### Voltar para Banco Local

Para voltar a usar o banco local, edite o `.env` e remova ou comente as linhas do Neon:
```bash
cd server
# Comentar ou remover as linhas do Neon
# POLAR_POSTGRES_HOST=ep-dry-cell-achqzu4j-pooler.sa-east-1.aws.neon.tech
# ...

# Usar configurações locais
POLAR_POSTGRES_HOST=127.0.0.1
POLAR_POSTGRES_USER=polar
POLAR_POSTGRES_PWD=polar
POLAR_POSTGRES_DATABASE=polar
POLAR_POSTGRES_SSLMODE=prefer
```

