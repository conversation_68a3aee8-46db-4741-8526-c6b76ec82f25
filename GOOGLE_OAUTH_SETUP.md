# 🔐 Google OAuth Configuration - Fluu

## ✅ Credenciais Configuradas

```
Client ID: ************-aj399ub5qflffahehklunc9sudu93eeu.apps.googleusercontent.com
Client Secret: 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
```

## 🔧 Configuração no Google Cloud Console

### 1. Acesse o Google Cloud Console
https://console.cloud.google.com/apis/credentials

### 2. Configure as URLs de Redirect Autorizadas

Na configuração do OAuth 2.0 Client ID, adicione as seguintes URLs:

#### Desenvolvimento Local
```
http://127.0.0.1:8000/v1/integrations/google/callback
http://localhost:8000/v1/integrations/google/callback
http://127.0.0.1:3000/v1/integrations/google/callback
http://localhost:3000/v1/integrations/google/callback
```

#### Produção
```
https://api.fluu.digital/v1/integrations/google/callback
https://app.fluu.digital/v1/integrations/google/callback
```

### 3. Configure as Origens JavaScript Autorizadas

#### Desenvolvimento Local
```
http://127.0.0.1:3000
http://localhost:3000
http://127.0.0.1:8000
http://localhost:8000
```

#### Produção
```
https://app.fluu.digital
https://api.fluu.digital
https://fluu.digital
```

## 📋 Variáveis de Ambiente

### Backend (`server/.env`)
⚠️ **IMPORTANTE**: As variáveis precisam do prefixo `POLAR_`

```bash
POLAR_GOOGLE_CLIENT_ID=************-aj399ub5qflffahehklunc9sudu93eeu.apps.googleusercontent.com
POLAR_GOOGLE_CLIENT_SECRET=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
```

### Produção (Backend)
Configure as mesmas variáveis no seu provedor de hosting (Render, Railway, etc):
```bash
POLAR_GOOGLE_CLIENT_ID=************-aj399ub5qflffahehklunc9sudu93eeu.apps.googleusercontent.com
POLAR_GOOGLE_CLIENT_SECRET=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
```

## 🧪 Teste Local

1. **Abra o navegador**: http://127.0.0.1:3000/login

2. **Clique em "Continue with Google"**

3. **Deve redirecionar para**: 
   ```
   http://127.0.0.1:8000/v1/integrations/google/authorize?return_to=%2Fdashboard
   ```

4. **Depois para Google OAuth**:
   ```
   https://accounts.google.com/o/oauth2/v2/auth?...
   ```

5. **Após autorizar, volta para**:
   ```
   http://127.0.0.1:8000/v1/integrations/google/callback?code=...&state=...
   ```

6. **Finalmente redireciona para**:
   ```
   http://127.0.0.1:3000/dashboard
   ```

## 🔍 Debug

### Verificar se as credenciais estão carregadas

```bash
cd /Users/<USER>/Documents/www/Gateways/polar/server
grep POLAR_GOOGLE .env
```

### Verificar logs do backend

```bash
tail -f /tmp/polar-api-logs.txt | grep -i google
```

### Testar endpoint de autorização

```bash
curl -v "http://127.0.0.1:8000/v1/integrations/google/authorize?return_to=%2Fdashboard"
```

Deve retornar HTTP 303 com redirect para Google.

## ⚠️ Problemas Comuns

### Erro: "redirect_uri_mismatch"
**Causa**: URL de callback não está configurada no Google Cloud Console

**Solução**: 
1. Vá para Google Cloud Console
2. Adicione exatamente: `http://127.0.0.1:8000/v1/integrations/google/callback`
3. Aguarde alguns minutos para propagar

### Erro: "access_denied"
**Causa**: Usuário cancelou ou app não tem permissões necessárias

**Solução**: 
- Verificar escopos solicitados (userinfo.profile, userinfo.email)
- Verificar se o app está em modo de teste e o usuário está na lista

### Erro: "invalid_client"
**Causa**: Client ID ou Secret incorretos

**Solução**: 
- Verificar se as credenciais no `.env` estão corretas
- Reiniciar o backend: `lsof -ti:8000 | xargs kill -9 && cd server && uv run task api`

## 🚀 Deploy em Produção

### 1. Configure as variáveis de ambiente no seu provedor

```bash
GOOGLE_CLIENT_ID=************-aj399ub5qflffahehklunc9sudu93eeu.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
```

### 2. Adicione as URLs de produção no Google Cloud Console

```
Redirect URI: https://api.fluu.digital/v1/integrations/google/callback
Origin: https://app.fluu.digital
```

### 3. Verifique o fluxo

1. https://app.fluu.digital/login
2. Clique em "Continue with Google"
3. Deve funcionar sem erros

## 📝 Escopos Solicitados

```python
scope=[
    "https://www.googleapis.com/auth/userinfo.profile",
    "https://www.googleapis.com/auth/userinfo.email",
]
```

Esses escopos permitem:
- ✅ Obter nome do usuário
- ✅ Obter email do usuário
- ✅ Obter foto de perfil
- ✅ Verificar se email está verificado

## ✅ Checklist

- [x] Credenciais adicionadas ao `.env` com prefixo `POLAR_`
- [x] Backend reiniciado e testado ✅
- [ ] URLs de redirect configuradas no Google Cloud Console
- [ ] Origens JavaScript configuradas no Google Cloud Console
- [ ] Testado localmente (http://127.0.0.1:3000/login)
- [ ] Variáveis configuradas em produção
- [ ] URLs de produção configuradas no Google Cloud Console
- [ ] Testado em produção (https://app.fluu.digital/login)

---

**Configurado em**: 2025-11-10  
**Backend Local**: ✅ Configurado e funcionando  
**Google Cloud Console**: ⚠️ Aguardando configuração de URLs de redirect

### 🧪 Teste Rápido Local

Backend responde corretamente:
```bash
curl -s "http://127.0.0.1:8000/v1/integrations/google/authorize?return_to=%2Fdashboard" | grep "client_id"
# Resultado esperado: client_id=************-aj399ub5qflffahehklunc9sudu93eeu.apps.googleusercontent.com
```

✅ **Pronto para testar no browser após configurar URLs no Google Cloud Console!**

