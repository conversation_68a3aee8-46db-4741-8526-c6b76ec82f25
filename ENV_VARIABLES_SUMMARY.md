# Resumo das Variáveis de Ambiente

## Servidor (Python/Backend)

### Arquivo: `server/.env`
<PERSON><PERSON> as variáveis usam o prefixo `POLAR_` (configurado em `server/polar/config.py`)

### Principais Variáveis de Ambiente

#### Ambiente
- `POLAR_ENV` - Ambiente (development, testing, sandbox, production) - Default: `development`
- `POLAR_LOG_LEVEL` - Nível de log - Default: `DEBUG`

#### Ban<PERSON> de <PERSON> (PostgreSQL)
- `POLAR_POSTGRES_USER` - Usu<PERSON><PERSON> do PostgreSQL - Default: `polar`
- `POLAR_POSTGRES_PWD` - Senha do PostgreSQL - Default: `polar`
- `POLAR_POSTGRES_HOST` - Host do PostgreSQL - Default: `127.0.0.1`
- `POLAR_POSTGRES_PORT` - Porta do PostgreSQL - Default: `5432`
- `POLAR_POSTGRES_DATABASE` - Nome do banco - Default: `polar_development`
- `POLAR_POSTGRES_SSLMODE` - Modo SSL - Default: `prefer`

#### Redis
- `POLAR_REDIS_HOST` - Host do Redis - Default: `127.0.0.1`
- `POLAR_REDIS_PORT` - Porta do Redis - Default: `6379`
- `POLAR_REDIS_DB` - Database do Redis - Default: `0`
- `POLAR_REDIS_PASSWORD` - Senha do Redis (opcional)
- `POLAR_REDIS_USERNAME` - Usuário do Redis (opcional)
- `POLAR_REDIS_SSL` - SSL habilitado - Default: `False`

#### URLs e CORS
- `POLAR_BASE_URL` - URL base do backend - Default: `http://127.0.0.1:8000`
- `POLAR_FRONTEND_BASE_URL` - URL do frontend - Default: `http://127.0.0.1:3000`
- `POLAR_CORS_ORIGINS` - JSON list de origens CORS aceitas
- `POLAR_ALLOWED_HOSTS` - Set de hosts permitidos - Default: `{"127.0.0.1:3000", "localhost:3000"}`

#### Autenticação e Segurança
- `POLAR_SECRET` - Secret JWT - Default: `super secret jwt secret`
- `POLAR_JWKS` - Caminho do arquivo JWKS - Default: `./.jwks.json`
- `POLAR_CURRENT_JWK_KID` - ID da chave JWT atual - Default: `fluu_dev`
- `POLAR_WWW_AUTHENTICATE_REALM` - Realm de autenticação - Default: `fluu`

#### Email
- `POLAR_EMAIL_SENDER` - Provedor de email (logger, resend) - Default: `logger`
- `POLAR_EMAIL_RENDERER_BINARY_PATH` - Caminho do binário do renderizador de email
- `POLAR_RESEND_API_KEY` - Chave da API Resend
- `POLAR_EMAIL_FROM_NAME` - Nome do remetente - Default: `Fluu`
- `POLAR_EMAIL_FROM_DOMAIN` - Domínio do remetente - Default: `notifications.polar.sh`

#### GitHub
- `POLAR_GITHUB_CLIENT_ID` - Client ID do GitHub
- `POLAR_GITHUB_CLIENT_SECRET` - Client Secret do GitHub
- `POLAR_GITHUB_REPOSITORY_BENEFITS_APP_NAMESPACE` - Namespace do GitHub App
- `POLAR_GITHUB_REPOSITORY_BENEFITS_APP_IDENTIFIER` - Identifier do GitHub App
- `POLAR_GITHUB_REPOSITORY_BENEFITS_APP_PRIVATE_KEY` - Chave privada do GitHub App

#### Stripe
- `POLAR_STRIPE_SECRET_KEY` - Chave secreta do Stripe
- `POLAR_STRIPE_PUBLISHABLE_KEY` - Chave pública do Stripe
- `POLAR_STRIPE_WEBHOOK_SECRET` - Secret do webhook do Stripe
- `POLAR_STRIPE_CONNECT_WEBHOOK_SECRET` - Secret do webhook Connect do Stripe

#### Pagar.me
- `POLAR_PAGARME_SECRET_KEY` - Chave secreta do Pagar.me - Default: `sk_test_dc24386c807a4b6886f02fde7b10c423`
- `POLAR_PAGARME_PUBLISHABLE_KEY` - Chave pública do Pagar.me
- `POLAR_PAGARME_WEBHOOK_SECRET` - Secret do webhook do Pagar.me
- `POLAR_ENABLE_PAGARME` - Habilitar Pagar.me - Default: `False`

#### AWS/S3
- `POLAR_AWS_ACCESS_KEY_ID` - Access Key ID da AWS - Default: `polar-development`
- `POLAR_AWS_SECRET_ACCESS_KEY` - Secret Access Key da AWS - Default: `polar123456789`
- `POLAR_AWS_REGION` - Região da AWS - Default: `us-east-2`
- `POLAR_S3_ENDPOINT_URL` - URL do endpoint S3 (para MinIO em dev) - Default: `http://127.0.0.1:9000`
- `POLAR_S3_FILES_BUCKET_NAME` - Nome do bucket S3 - Default: `polar-s3`
- `POLAR_S3_FILES_PUBLIC_BUCKET_NAME` - Nome do bucket público - Default: `polar-s3-public`
- `POLAR_MINIO_USER` - Usuário do MinIO - Default: `polar`
- `POLAR_MINIO_PWD` - Senha do MinIO - Default: `polarpolar`

#### MinIO (Docker Compose)
- `POLAR_MINIO_USER` - Usuário do MinIO
- `POLAR_MINIO_PWD` - Senha do MinIO
- `POLAR_S3_FILES_BUCKET_NAME` - Nome do bucket
- `POLAR_S3_FILES_PUBLIC_BUCKET_NAME` - Nome do bucket público
- `POLAR_AWS_ACCESS_KEY_ID` - Access Key ID
- `POLAR_AWS_SECRET_ACCESS_KEY` - Secret Access Key

#### Outros Serviços
- `POLAR_POSTHOG_PROJECT_API_KEY` - Chave da API do PostHog
- `POLAR_SENTRY_DSN` - DSN do Sentry
- `POLAR_OPENAI_API_KEY` - Chave da API OpenAI
- `POLAR_LOGFIRE_TOKEN` - Token do Logfire

## Cliente (Next.js/Frontend)

### Arquivo: `clients/apps/web/.env.local`
Todas as variáveis públicas devem ter o prefixo `NEXT_PUBLIC_`

### Principais Variáveis de Ambiente

#### URLs e API
- `NEXT_PUBLIC_API_URL` - URL da API backend - Default: `http://127.0.0.1:8000`
- `NEXT_PUBLIC_FRONTEND_BASE_URL` - URL do frontend - Default: `http://127.0.0.1:3000`
- `NEXT_PUBLIC_BACKOFFICE_URL` - URL do backoffice (opcional)

#### Ambiente
- `NEXT_PUBLIC_ENVIRONMENT` - Ambiente (development, sandbox, production)
- `VERCEL_ENV` - Ambiente do Vercel (se aplicável)
- `NEXT_PUBLIC_VERCEL_ENV` - Ambiente do Vercel público

#### Autenticação
- `POLAR_AUTH_COOKIE_KEY` - Chave do cookie de autenticação - Default: `polar_session`
- `POLAR_AUTH_MCP_COOKIE_KEY` - Chave do cookie MCP - Default: `polar_mcp_session`
- `NEXT_PUBLIC_LOGIN_PATH` - Caminho de login - Default: `/login`

#### Integrações
- `NEXT_PUBLIC_STRIPE_KEY` - Chave pública do Stripe
- `NEXT_PUBLIC_POSTHOG_TOKEN` - Token do PostHog
- `NEXT_PUBLIC_SENTRY_DSN` - DSN do Sentry
- `NEXT_PUBLIC_GOOGLE_ANALYTICS_ID` - ID do Google Analytics

#### GitHub
- `NEXT_PUBLIC_GITHUB_APP_NAMESPACE` - Namespace do GitHub App - Default: `polar-sh`
- `NEXT_PUBLIC_GITHUB_BADGE_EMBED_DEFAULT_LABEL` - Label padrão do badge - Default: `Fund`

#### Outros
- `NEXT_PUBLIC_APPLE_DOMAIN_ASSOCIATION` - Associação de domínio Apple Pay
- `NEXT_PUBLIC_CHECKOUT_EMBED_SCRIPT_SRC` - Caminho do script de embed do checkout
- `GITHUB_TOKEN` - Token do GitHub (para Codespaces)

## Docker Compose

### Variáveis necessárias no `server/docker-compose.yml`:
- `POLAR_POSTGRES_USER`
- `POLAR_POSTGRES_PWD`
- `POLAR_POSTGRES_DATABASE`
- `POLAR_POSTGRES_PORT`
- `POLAR_POSTGRES_READ_USER` (opcional)
- `POLAR_POSTGRES_READ_PWD` (opcional)
- `POLAR_REDIS_PORT`
- `POLAR_MINIO_USER`
- `POLAR_MINIO_PWD`
- `POLAR_S3_FILES_BUCKET_NAME`
- `POLAR_S3_FILES_PUBLIC_BUCKET_NAME`
- `POLAR_AWS_ACCESS_KEY_ID`
- `POLAR_AWS_SECRET_ACCESS_KEY`

## Scripts de Inicialização

### Servidor
```bash
cd server
docker compose up -d  # Inicia PostgreSQL, Redis, MinIO
uv run task emails    # Build email binary
uv run task db_migrate # Aplica migrações
uv run task api       # Inicia API (porta 8000)
uv run task worker    # Inicia Worker
```

### Cliente
```bash
cd clients
pnpm install          # Instala dependências
pnpm dev              # Inicia dev server (porta 3000)
```

