#!/usr/bin/env python3
"""
Script de Teste PIX Completo - Fluxo Real de Checkout
Simula o fluxo completo de checkout com PIX usando dados reais do Pagar.me
"""

import asyncio
import json
import qrcode
import requests
import sys
import time
from datetime import datetime
from io import BytesIO
from pathlib import Path

# Configurações
API_BASE_URL = "http://localhost:8000"
PAGARME_API_URL = "https://api.pagar.me/core/v5"

# Dados de teste (substitua pelos seus dados reais)
TEST_DATA = {
    "customer": {
        "name": "<PERSON>",
        "email": "<EMAIL>",
        "phone": "+5511999999999",
        "tax_id": "12345678901",  # CPF válido
        "billing_address": {
            "country": "BR",
            "state": "SP",
            "city": "São Paulo",
            "postal_code": "01310-100",
            "line1": "Av. <PERSON>, 1000",
            "line2": "Apto 101"
        }
    },
    "product": {
        "name": "Produto Teste PIX",
        "amount": 5000,  # R$ 50,00 em centavos
        "currency": "brl"
    }
}

class PixTestRunner:
    def __init__(self):
        self.session = requests.Session()
        self.checkout_data = None
        self.payment_data = None
        
    def log(self, message: str, level: str = "INFO"):
        """Log com timestamp"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {level}: {message}")
    
    def test_api_connection(self) -> bool:
        """Testa conexão com a API"""
        self.log("🔧 Testando conexão com a API...")
        
        try:
            response = self.session.get(f"{API_BASE_URL}/health", timeout=5)
            if response.status_code == 200:
                self.log("✅ API está rodando")
                return True
            else:
                self.log(f"❌ API retornou status {response.status_code}", "ERROR")
                return False
        except requests.exceptions.RequestException as e:
            self.log(f"❌ Erro ao conectar: {e}", "ERROR")
            self.log("💡 Certifique-se de que o servidor está rodando", "INFO")
            return False
    
    def create_test_product(self) -> str | None:
        """Cria um produto de teste ou usa um existente"""
        self.log("🛍️ Criando produto de teste...")
        
        # Primeiro, tenta listar produtos existentes
        try:
            response = self.session.get(f"{API_BASE_URL}/v1/products")
            if response.status_code == 200:
                products = response.json().get("items", [])
                if products:
                    product = products[0]
                    if product.get("prices"):
                        price_id = product["prices"][0]["id"]
                        self.log(f"✅ Usando produto existente: {product['name']}")
                        self.log(f"   Price ID: {price_id}")
                        return price_id
        except Exception as e:
            self.log(f"⚠️ Erro ao listar produtos: {e}", "WARN")
        
        # Se não encontrou produtos, cria um novo
        product_data = {
            "name": TEST_DATA["product"]["name"],
            "description": "Produto de teste para PIX",
            "is_recurring": False,
            "prices": [{
                "amount_type": "fixed",
                "price_amount": TEST_DATA["product"]["amount"],
                "price_currency": TEST_DATA["product"]["currency"]
            }]
        }
        
        try:
            response = self.session.post(
                f"{API_BASE_URL}/v1/products",
                json=product_data,
                timeout=10
            )
            
            if response.status_code == 201:
                product = response.json()
                price_id = product["prices"][0]["id"]
                self.log(f"✅ Produto criado: {product['name']}")
                self.log(f"   Price ID: {price_id}")
                return price_id
            else:
                self.log(f"❌ Erro ao criar produto: {response.status_code}", "ERROR")
                self.log(f"   Resposta: {response.text}", "ERROR")
                return None
                
        except Exception as e:
            self.log(f"❌ Erro na requisição: {e}", "ERROR")
            return None
    
    def create_checkout(self, price_id: str) -> bool:
        """Cria um checkout"""
        self.log("🛒 Criando checkout...")
        
        checkout_data = {
            "product_price_id": price_id,
            "customer_email": TEST_DATA["customer"]["email"]
        }
        
        try:
            response = self.session.post(
                f"{API_BASE_URL}/v1/checkouts/client",
                json=checkout_data,
                timeout=10
            )
            
            if response.status_code == 200:
                self.checkout_data = response.json()
                processor = self.checkout_data.get("payment_processor")
                
                self.log(f"✅ Checkout criado com sucesso")
                self.log(f"   Payment Processor: {processor}")
                self.log(f"   Client Secret: {self.checkout_data.get('client_secret')}")
                
                if processor == "pagarme":
                    self.log("✅ Pagar.me selecionado como esperado")
                    return True
                else:
                    self.log(f"⚠️ Processor inesperado: {processor}", "WARN")
                    return False
                    
            else:
                self.log(f"❌ Erro ao criar checkout: {response.status_code}", "ERROR")
                self.log(f"   Resposta: {response.text}", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"❌ Erro na requisição: {e}", "ERROR")
            return False
    
    def confirm_checkout_pix(self) -> bool:
        """Confirma o checkout com método PIX"""
        if not self.checkout_data:
            self.log("❌ Dados do checkout não disponíveis", "ERROR")
            return False
        
        self.log("💳 Confirmando checkout com PIX...")
        
        client_secret = self.checkout_data.get("client_secret")
        confirm_data = {
            "customer_name": TEST_DATA["customer"]["name"],
            "customer_email": TEST_DATA["customer"]["email"],
            "customer_billing_address": TEST_DATA["customer"]["billing_address"],
            "customer_tax_id": [TEST_DATA["customer"]["tax_id"]],
            "payment_method": "pix"
        }
        
        try:
            response = self.session.post(
                f"{API_BASE_URL}/v1/checkouts/client/{client_secret}/confirm",
                json=confirm_data,
                timeout=15
            )
            
            if response.status_code == 200:
                self.payment_data = response.json()
                self.log("✅ Checkout confirmado com sucesso")
                
                # Verificar metadata do PIX
                metadata = self.payment_data.get("payment_processor_metadata", {})
                pix_qr_code = metadata.get("pix_qr_code")
                payment_id = metadata.get("payment_id")
                
                if pix_qr_code:
                    self.log("✅ QR Code PIX gerado")
                    self.log(f"   Payment ID: {payment_id}")
                    return True
                else:
                    self.log("⚠️ QR Code PIX não encontrado", "WARN")
                    self.log("   Isso pode ser normal se o provider não estiver configurado")
                    return True
                    
            else:
                self.log(f"❌ Erro ao confirmar checkout: {response.status_code}", "ERROR")
                self.log(f"   Resposta: {response.text}", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"❌ Erro na requisição: {e}", "ERROR")
            return False
    
    def display_pix_qr_code(self):
        """Exibe o QR Code PIX"""
        if not self.payment_data:
            return
        
        metadata = self.payment_data.get("payment_processor_metadata", {})
        pix_qr_code = metadata.get("pix_qr_code")
        
        if not pix_qr_code:
            self.log("⚠️ QR Code PIX não disponível", "WARN")
            return
        
        self.log("📱 Gerando QR Code PIX...")
        
        try:
            # Gerar QR Code
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=4,
            )
            qr.add_data(pix_qr_code)
            qr.make(fit=True)
            
            # Salvar como imagem
            img = qr.make_image(fill_color="black", back_color="white")
            qr_path = Path("pix_qr_code.png")
            img.save(qr_path)
            
            self.log(f"✅ QR Code salvo em: {qr_path.absolute()}")
            
            # Exibir código PIX para cópia
            print("\n" + "="*60)
            print("📱 CÓDIGO PIX PARA PAGAMENTO")
            print("="*60)
            print(f"Valor: R$ {TEST_DATA['product']['amount'] / 100:.2f}")
            print(f"Beneficiário: {TEST_DATA['customer']['name']}")
            print("\n🔗 Código PIX (Copia e Cola):")
            print("-" * 60)
            print(pix_qr_code)
            print("-" * 60)
            print(f"\n💡 QR Code salvo em: {qr_path.absolute()}")
            print("   Escaneie com seu app do banco para pagar")
            print("="*60)
            
        except Exception as e:
            self.log(f"❌ Erro ao gerar QR Code: {e}", "ERROR")
            
            # Fallback: mostrar apenas o código
            print("\n" + "="*60)
            print("📱 CÓDIGO PIX PARA PAGAMENTO")
            print("="*60)
            print("🔗 Código PIX (Copia e Cola):")
            print(pix_qr_code)
            print("="*60)
    
    def check_payment_status(self) -> str:
        """Verifica o status do pagamento"""
        if not self.payment_data:
            return "unknown"
        
        self.log("🔍 Verificando status do pagamento...")
        
        # Em um cenário real, você consultaria a API do Pagar.me
        # Por enquanto, simulamos baseado nos dados do checkout
        status = self.payment_data.get("status", "unknown")
        
        self.log(f"   Status atual: {status}")
        
        if status == "confirmed":
            self.log("✅ Pagamento confirmado")
        elif status == "pending":
            self.log("⏳ Pagamento pendente")
        else:
            self.log(f"❓ Status: {status}")
        
        return status
    
    async def run_complete_test(self):
        """Executa o teste completo"""
        print("🚀 INICIANDO TESTE COMPLETO DE PIX")
        print("="*50)
        
        # 1. Testar conexão
        if not self.test_api_connection():
            return False
        
        # 2. Criar/obter produto
        price_id = self.create_test_product()
        if not price_id:
            return False
        
        # 3. Criar checkout
        if not self.create_checkout(price_id):
            return False
        
        # 4. Confirmar com PIX
        if not self.confirm_checkout_pix():
            return False
        
        # 5. Exibir QR Code
        self.display_pix_qr_code()
        
        # 6. Verificar status
        self.check_payment_status()
        
        print("\n" + "="*50)
        print("🎉 TESTE COMPLETO FINALIZADO")
        print("="*50)
        print("✅ Fluxo de checkout PIX executado com sucesso!")
        print("💡 Agora você pode testar o pagamento usando o QR Code gerado")
        
        return True

def main():
    """Função principal"""
    try:
        runner = PixTestRunner()
        success = asyncio.run(runner.run_complete_test())
        return 0 if success else 1
    except KeyboardInterrupt:
        print("\n⚠️ Teste interrompido pelo usuário")
        return 1
    except Exception as e:
        print(f"\n❌ Erro inesperado: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
