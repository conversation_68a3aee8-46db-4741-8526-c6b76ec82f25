# 🚀 Checklist de Deploy - Fluu

## 📋 Resumo das Correções Implementadas

### ✅ 1. Autenticação com OTP (Login Code)
- **Problema**: <PERSON><PERSON> n<PERSON> era compartilhado entre frontend e backend
- **Solução**: API Route proxy + correção de nomes de cookies
- **Arquivos alterados**:
  - `server/polar/config.py`
  - `server/polar/auth/service.py`
  - `clients/apps/web/src/proxy.ts`
  - `clients/apps/web/src/utils/config.ts`
  - `clients/apps/web/src/app/api/auth/login-code/authenticate/route.ts` (NOVO)
  - `clients/apps/web/src/app/(main)/login/code/verify/ClientPage.tsx`

### ✅ 2. Google OAuth
- **Credenciais configuradas**: ✅
- **Backend testado**: ✅
- **Aguardando**: Configuração de URLs no Google Cloud Console

---

## 🔐 1. Autenticação OTP - Deploy

### Backend

#### Variáveis de Ambiente em Produção
```bash
# Session Cookie Configuration
USER_SESSION_COOKIE_DOMAIN=.fluu.digital
POLAR_USER_SESSION_COOKIE_KEY=fluu_session
```

#### Arquivos para Commit
```bash
cd /Users/<USER>/Documents/www/Gateways/polar

git add server/polar/config.py
git add server/polar/auth/service.py
git commit -m "fix: session cookie configuration for cross-domain auth"
```

### Frontend

#### Variáveis de Ambiente em Produção
```bash
POLAR_AUTH_COOKIE_KEY=fluu_session
NEXT_PUBLIC_API_URL=https://api.fluu.digital
```

#### Arquivos para Commit
```bash
git add clients/apps/web/src/proxy.ts
git add clients/apps/web/src/utils/config.ts
git add clients/apps/web/src/app/api/auth/login-code/authenticate/
git add clients/apps/web/src/app/(main)/login/code/verify/ClientPage.tsx
git add clients/apps/web/next.config.mjs

git commit -m "fix: use API route proxy for authentication to comply with CSP"
```

---

## 🔐 2. Google OAuth - Deploy

### Backend

#### Variáveis de Ambiente
```bash
# Desenvolvimento e Produção
POLAR_GOOGLE_CLIENT_ID=923457232981-aj399ub5qflffahehklunc9sudu93eeu.apps.googleusercontent.com
POLAR_GOOGLE_CLIENT_SECRET=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
```

⚠️ **IMPORTANTE**: Note o prefixo `POLAR_`

### Google Cloud Console

#### URLs de Redirect Autorizadas
Adicione em: https://console.cloud.google.com/apis/credentials

**Desenvolvimento:**
```
http://127.0.0.1:8000/v1/integrations/google/callback
http://localhost:8000/v1/integrations/google/callback
```

**Produção:**
```
https://api.fluu.digital/v1/integrations/google/callback
```

#### Origens JavaScript Autorizadas
**Desenvolvimento:**
```
http://127.0.0.1:3000
http://localhost:3000
```

**Produção:**
```
https://app.fluu.digital
https://fluu.digital
```

---

## 🧪 Testes

### 1. Teste OTP Local (✅ VALIDADO)
```bash
# 1. Solicitar código
curl -X POST "http://127.0.0.1:8000/v1/login-code/request" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>"}'

# 2. Pegar código dos logs
tail -100 /tmp/polar-api-logs.txt | grep "acesso"

# 3. Autenticar
curl -X POST "http://127.0.0.1:8000/v1/login-code/authenticate?return_to=%2Fdashboard&email=seuemail%40example.com" \
  -H "Content-Type: multipart/form-data" \
  -F "code=CODIGO" \
  -v -c /tmp/cookies.txt

# ✅ Deve retornar HTTP 303 e Set-Cookie: fluu_session
```

### 2. Teste Google OAuth Local (✅ VALIDADO)
```bash
# Verificar se credenciais estão carregadas
curl -s "http://127.0.0.1:8000/v1/integrations/google/authorize?return_to=%2Fdashboard" | grep "client_id"

# ✅ Deve retornar: client_id=923457232981-...
```

### 3. Teste no Browser Local
1. Abrir: http://127.0.0.1:3000/login
2. Testar "Continue with Google"
3. Testar "Login with Email" + código OTP

### 4. Teste em Produção
1. Abrir: https://app.fluu.digital/login
2. Testar ambos os métodos de login
3. Verificar se sessão persiste após refresh

---

## ⚠️ Problemas Conhecidos e Soluções

### CSP Error em Produção
**Erro**: `form-action violates CSP`

**Causa**: Frontend antigo ainda usando form HTML direto

**Solução**: 
```bash
# Redesploy do frontend
cd clients
git push origin main
```

### Cookie não persiste
**Causa**: Domain errado

**Verificar**:
- Backend: `USER_SESSION_COOKIE_DOMAIN=.fluu.digital`
- Frontend: `POLAR_AUTH_COOKIE_KEY=fluu_session`

### Google OAuth redirect_uri_mismatch
**Causa**: URL não configurada no Google Cloud Console

**Solução**:
1. https://console.cloud.google.com/apis/credentials
2. Adicionar URL exata do callback
3. Aguardar ~5 minutos para propagar

---

## 📦 Ordem de Deploy Recomendada

### 1. Backend
```bash
cd server
git add .
git commit -m "fix: auth improvements (OTP + Google OAuth)"
git push origin main
```

Configure as variáveis:
- `USER_SESSION_COOKIE_DOMAIN=.fluu.digital`
- `POLAR_USER_SESSION_COOKIE_KEY=fluu_session`
- `POLAR_GOOGLE_CLIENT_ID=...`
- `POLAR_GOOGLE_CLIENT_SECRET=...`

### 2. Google Cloud Console
- Configure URLs de redirect e origens
- Aguarde propagação (~5 min)

### 3. Frontend
```bash
cd clients
git add .
git commit -m "fix: auth flow with API route proxy"
git push origin main
```

Configure as variáveis:
- `POLAR_AUTH_COOKIE_KEY=fluu_session`
- `NEXT_PUBLIC_API_URL=https://api.fluu.digital`

### 4. Teste Final
- Login com email + OTP
- Login com Google
- Verificar persistência de sessão

---

## 📞 Support

Documentação completa:
- `FIX_AUTH_DEPLOYMENT.md` - Detalhes da correção de OTP
- `GOOGLE_OAUTH_SETUP.md` - Configuração completa do Google OAuth

Logs em produção:
- Backend: Verificar no provedor de hosting
- Frontend: Console do browser (F12)

---

**Última atualização**: 2025-11-10
**Status**: ✅ Configuração local validada | ⏳ Aguardando deploy em produção

