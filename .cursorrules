# Fluu - Multi-Gateway Payment Platform

## 🎯 Missão do Projeto

Criar a melhor plataforma de pagamentos para SaaS no Brasil, com suporte nativo a PIX e múltiplos gateways locais.

## 🏗️ Arquitetura

- **Base**: Fork do Polar.sh (Python/FastAPI + Next.js/React)
- **Diferencial**: Multi-gateway (Stripe, Pagar.me, Mercado Pago)
- **Foco**: Mercado brasileiro, PIX, gateways locais

## 📂 Estrutura Principal

```
server/polar/
├── product/service.py                    # ✅ Stripe opcional
├── checkout/service.py                   # 🔄 Multi-gateway checkout
├── integrations/payment_providers/
│   ├── registry.py                       # Registry central
│   ├── base.py                           # Interface PaymentProvider
│   ├── stripe/                           # Gateway Stripe (original)
│   └── pagarme/                          # 🆕 Gateway Pagar.me
│       ├── provider.py                   # ✅ PIX + Boleto + Cartão
│       ├── webhooks.py                   # Webhook handler
│       └── schemas.py                    # Pydantic schemas
└── enums.py                              # PaymentProcessor enum
```

## 🔑 Conceitos-Chave

### 1. Payment Provider Registry

Todos os gateways são registrados centralmente:

```python
PaymentProviderRegistry.register(PaymentProcessor.pagarme, PagarmeProvider())
provider = PaymentProviderRegistry.get(PaymentProcessor.pagarme)
```

### 2. Payment Provider Interface

Todos os providers implementam:
- `create_customer()`
- `create_payment_intent()`
- `create_pix_payment()` (se suportar PIX)
- `retrieve_payment()`
- `create_refund()`
- `handle_webhook()`

### 3. Multi-Gateway Strategy

Ordem de prioridade:
1. `product.preferred_payment_processor` (se definido)
2. Pagar.me (se `ENABLE_PAGARME=true`)
3. Stripe (fallback)

## 🚀 Status Atual

### ✅ FASE 1: Stripe Opcional (CONCLUÍDA)
- Produtos podem ser criados sem Stripe
- Tratamento robusto de erros
- Logs estruturados

### 🔄 FASE 2: Pagar.me + PIX (80% CONCLUÍDA)
- Provider Pagar.me implementado
- PIX QR Code generation funcional
- Script de teste disponível
- **Falta**: Frontend PIX, testes E2E

### 🎯 FASE 3: Mercado Pago (PLANEJADA)
- Provider Mercado Pago
- Split de pagamento

## 📋 Tarefas Comuns

### Adicionar Novo Gateway

1. Adicionar enum em `enums.py`:
   ```python
   class PaymentProcessor(StrEnum):
       novogateway = "novogateway"
   ```

2. Criar provider em `integrations/payment_providers/novogateway/`

3. Registrar no startup:
   ```python
   PaymentProviderRegistry.register(
       PaymentProcessor.novogateway,
       NovoGatewayProvider()
   )
   ```

4. Ver `GUIA_IMPLEMENTACAO_GATEWAYS.md` para detalhes

### Testar Pagar.me + PIX

```bash
python3 test_pagarme_pix.py
```

### Criar Produto (sem gateway)

```python
# ProductService.create() automaticamente detecta se deve criar no gateway
# Se nenhum gateway configurado, apenas salva no banco
```

## 🎨 Padrões de Código

### Logging

```python
import structlog
log = structlog.get_logger()

log.info("Payment created", payment_id=payment_id, gateway="pagarme")
log.warning("Gateway failed, continuing", error=str(e))
```

### Error Handling

```python
try:
    result = await provider.create_payment_intent(...)
except PaymentProviderError as e:
    log.error("Payment failed", error=str(e))
    # Continuar sem bloquear operação
```

### Async/Await

```python
async def create_payment(self, ...):
    customer = await provider.create_customer(...)
    payment = await provider.create_payment_intent(...)
    return payment
```

## 🔧 Variáveis de Ambiente

```bash
# Stripe (opcional)
POLAR_STRIPE_SECRET_KEY=sk_test_xxx
POLAR_STRIPE_PUBLISHABLE_KEY=pk_test_xxx

# Pagar.me (recomendado para Brasil)
POLAR_PAGARME_SECRET_KEY=sk_test_xxx
POLAR_PAGARME_PUBLISHABLE_KEY=pk_test_xxx
ENABLE_PAGARME=true
```

## 📚 Documentação

### Multi-Gateway
- **Arquitetura**: `ARQUITETURA_MULTI_GATEWAY.md`
- **Estratégia de Negócio**: `ESTRATEGIA_NEGOCIO_MULTI_GATEWAY.md`
- **Guia de Implementação**: `GUIA_IMPLEMENTACAO_GATEWAYS.md`
- **README**: `README_MULTI_GATEWAY.md`

### Ativação de Contas
- **Prompt para IA**: `PROMPT_ATIVACAO_CONTAS.md` ⭐
- **Documentação**: `DOCUMENTACAO_ATIVACAO_CONTAS.md`
- **Guia Completo**: `GUIA_APROVACAO_CONTAS.md`
- **Exemplos Práticos**: `EXEMPLOS_USO_SCRIPTS.md`

## 💡 Princípios

1. **Gateway Agnóstico**: Produtos existem independente de gateway
2. **Fail-Safe**: Erros de gateway não bloqueiam operações
3. **Lazy Creation**: Criar no gateway apenas quando necessário
4. **Logging Abundante**: Log tudo para debug
5. **Brazilian First**: PIX e gateways BR têm prioridade

## 🎯 Objetivos de Negócio

- **Conversão**: +30-40% com PIX
- **Economia**: -50% em taxas vs Stripe-only
- **Adoção**: PIX = 45%+ das transações
- **Target**: SaaS B2B/B2C no Brasil

## 🚨 Atenção

- Sempre testar em sandbox antes de produção
- PIX requer customer com CPF/CNPJ válido
- Webhooks devem ser HTTPS
- Validar assinatura de webhooks em produção
- Stripe continua funcionando (não removemos)

## 🔄 Workflow Git

```bash
# Feature branch
git checkout -b feature/novo-gateway

# Commit semântico
git commit -m "feat: add mercadopago provider with PIX"

# Push e PR
git push origin feature/novo-gateway
```

## 📊 Métricas

- Uptime target: 99.9%
- Latência P95: < 500ms
- PIX confirmation: < 5 segundos
- Error rate: < 0.1%

---

## 🔐 Sistema de Ativação de Contas

### Visão Geral

Contas precisam ser **ativadas** antes de receber pagamentos. Este processo envolve:
- Criar/atualizar Account
- Verificar identidade do admin (KYC)
- Vincular à Organization
- Habilitar payouts e charges

### Script Principal: activate_account_complete.py ⭐

**FAZ TUDO automaticamente em uma única execução!**

```bash
# Ativar organização
uv run python -m scripts.activate_account_complete \
  --organization-slug SLUG \
  --admin-email EMAIL \
  --no-dry-run

# Ativar usuário
uv run python -m scripts.activate_account_complete \
  --user-email EMAIL \
  --no-dry-run
```

### Requisitos para Conta Ativa

Uma conta está pronta quando:

```python
✅ Account.status == "active"
✅ Account.is_payouts_enabled == True
✅ Account.is_charges_enabled == True
✅ Account.is_details_submitted == True
✅ Account.stripe_id != None
✅ User.identity_verification_status == "verified" or "pending"

# Se Organization:
✅ Organization.status == "active"
✅ Organization.details_submitted_at != None
✅ Organization.account_id == Account.id
```

### Problemas Comuns

| Erro | Causa | Solução |
|------|-------|---------|
| "Payment processing not available" | Organização sem conta | `activate_account_complete.py` |
| 403 "Not the admin" | Admin errado | `change_account_admin.py` |
| 500 ao criar verification | KYC não configurado | `verify_user_identity.py` |
| Conta sem Stripe ID | stripe_id = null | `add_stripe_id_to_account.py` |

### Scripts Disponíveis

```
server/scripts/
├── activate_account_complete.py       ⭐ FAZ TUDO (use este!)
├── approve_account_for_payout.py      - Aprovar conta existente
├── verify_user_identity.py            - Bypass KYC (dev only)
├── change_account_admin.py            - Mudar admin
├── add_stripe_id_to_account.py        - Adicionar Stripe ID
├── list_accounts_status.py            - Listar/verificar contas
├── check_organization_account.py      - Status de organização
├── create_user_account.py             - Criar conta para user
├── create_and_approve_account.py      - Criar conta para org
└── fix_organization_payment_ready.py  - Corrigir requisitos
```

### Workflow Rápido

```bash
# 1. Verificar status
uv run python -m scripts.check_organization_account SLUG

# 2. Ativar tudo
uv run python -m scripts.activate_account_complete \
  --organization-slug SLUG \
  --admin-email EMAIL \
  --no-dry-run

# 3. Confirmar
uv run python -m scripts.list_accounts_status show --email EMAIL
```

### Para Agentes de IA

**Quando usuário pede para ativar conta:**

1. **Sempre use dry-run primeiro** para mostrar mudanças
2. **Use `activate_account_complete.py`** - faz tudo automaticamente
3. **Confirme resultado** após execução
4. **Teste no dashboard** se possível

**Prompt rápido:**
```
"Ative a conta para receber pagamentos"
→ uv run python -m scripts.activate_account_complete --organization-slug X --admin-email Y --no-dry-run
```

### KYC para Brasil (Produção)

⚠️ **IMPORTANTE**: Scripts atuais fazem bypass de KYC para **desenvolvimento**.

**Em produção, implementar:**
- **Idwall** (https://idwall.co) - CPF/CNPJ + facial
- **unico IDCheck** (https://unico.io) - Biometria
- **Serpro** (https://serpro.gov.br) - API governo

Ver `DOCUMENTACAO_ATIVACAO_CONTAS.md` para implementação.

### Modelos Envolvidos

```python
# Account - Gerencia pagamentos
polar.models.Account
  - status: Status (ACTIVE para funcionar)
  - is_payouts_enabled: bool
  - stripe_id: str (pode ser fake em dev)
  - admin: User (quem controla)

# Organization - Entidade vendedora  
polar.models.Organization
  - status: OrganizationStatus (active)
  - account_id: UUID (vinculado)
  - details_submitted_at: datetime

# User - Admin com KYC
polar.models.User
  - identity_verification_status: Enum (verified)
  - account_id: UUID (vinculado)
```

### Verificação Backend

```python
# organization/service.py
async def is_organization_ready_for_payment(
    session, organization
) -> bool:
    # Checa todos os requisitos
    # Retorna True se pronta
```

### Documentação Completa

- **PROMPT_ATIVACAO_CONTAS.md** ⭐ - Guia para agentes IA
- **DOCUMENTACAO_ATIVACAO_CONTAS.md** - Arquitetura e implementação
- **GUIA_APROVACAO_CONTAS.md** - Workflow completo
- **EXEMPLOS_USO_SCRIPTS.md** - 15 cenários práticos

---

**Lembre-se**: 
- PIX é prioridade máxima para pagamentos
- Ativação de contas é essencial antes de vender
- Scripts fazem bypass de KYC apenas em desenvolvimento

