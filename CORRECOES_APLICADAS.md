# Correções Aplicadas - Problemas de Login e Worker

## ✅ Correções Realizadas

### 1. **Desabilitado Endpoints do Stripe**

**Arquivo:** `server/polar/api.py`
- Comentado import do router do Stripe
- Comentado registro do router do Stripe na API
- Isso evita que novos webhooks do Stripe sejam processados

**Impacto:**
- Novos webhooks do Stripe não serão mais enfileirados
- Evita criação de novos jobs do Stripe que podem falhar

### 2. **Removido Tasks do Stripe do Worker**

**Arquivo:** `server/polar/tasks.py`
- Comentado import das tasks do Stripe
- Removido "stripe" da lista de exports
- Isso evita que o worker tente processar jobs do Stripe

**Impacto:**
- Worker não tentará mais processar jobs `stripe.webhook.*`
- Jobs do Stripe que já estão na fila serão ignorados (não processados)
- Worker pode processar outros jobs normalmente

### 3. **Adicionado Logs de Debug para Verificação de Código**

**Arquivo:** `server/polar/login_code/service.py`
- Adicionado logs detalhados no método `authenticate()`
- Logs incluem:
  - Email sendo verificado
  - Tamanho do código
  - Prefixo do código (primeiros 2 caracteres)
  - Se SECRET está configurado
  - Quantidade de códigos recentes para o email

**Impacto:**
- Facilita debug de problemas de verificação de código
- Permite identificar se o problema é com hash, expiração ou código incorreto

### 4. **Criado Script para Verificar e Limpar Fila do Worker**

**Arquivo:** `server/scripts/check_worker_queue.py`
- Script para verificar status das filas
- Script para limpar jobs do Stripe das filas
- Script para limpar todas as filas (com confirmação)

**Uso:**
```bash
# Verificar status das filas
python server/scripts/check_worker_queue.py --check

# Limpar jobs do Stripe
python server/scripts/check_worker_queue.py --clean-stripe

# Limpar todas as filas (CUIDADO!)
python server/scripts/check_worker_queue.py --clean-all
```

## 🔧 Próximos Passos

### 1. **Verificar e Limpar Jobs Presos**

Execute o script para verificar se há jobs presos:

```bash
cd server
python scripts/check_worker_queue.py --check
```

Se houver jobs do Stripe, limpe-os:

```bash
python scripts/check_worker_queue.py --clean-stripe
```

### 2. **Verificar Configuração do Worker**

Certifique-se de que:
- Worker está rodando
- `DRAMATIQ_QUEUES` está configurado como `high_priority,default` (com vírgula)
- Worker está processando as filas corretamente

### 3. **Verificar Configuração de Email**

Certifique-se de que:
- `POLAR_EMAIL_SENDER=resend`
- `POLAR_RESEND_API_KEY` está configurado
- Resend API está funcionando

### 4. **Verificar Configuração de SECRET**

Certifique-se de que:
- `POLAR_SECRET` está configurado
- Mesmo SECRET é usado em todos os ambientes (dev, staging, production)
- SECRET não mudou entre geração e verificação do código

### 5. **Testar Login Completo**

1. **Request login code:**
   ```bash
   curl -X POST http://localhost:8000/v1/login-code/request \
     -H "Content-Type: application/json" \
     -d '{"email": "<EMAIL>"}'
   ```

2. **Verificar email recebido:**
   - Verificar se email foi enviado
   - Verificar código recebido

3. **Autenticar com código:**
   ```bash
   curl -X POST "http://localhost:3000/login/code/verify?email=<EMAIL>&return_to=/dashboard" \
     -H "Content-Type: application/x-www-form-urlencoded" \
     -d "code=ABC123"
   ```

4. **Verificar logs:**
   - Verificar logs do worker para processamento de email
   - Verificar logs de autenticação para debug

## 🐛 Debug de Problemas

### Problema: Email não é enviado

**Verificações:**
1. Worker está rodando?
2. Job `email.send` foi criado na fila?
3. Worker processou o job?
4. Resend API está funcionando?
5. `POLAR_RESEND_API_KEY` está correto?

**Comandos:**
```bash
# Verificar fila
python server/scripts/check_worker_queue.py --check

# Ver logs do worker
# (depende do ambiente de deploy)
```

### Problema: Código não é reconhecido

**Verificações:**
1. Código foi salvo no banco?
2. Código não expirou?
3. SECRET está correto?
4. Hash está sendo gerado corretamente?
5. Email usado na verificação é o mesmo do request?

**Logs:**
- Verificar logs de `login_code.authenticate.attempt`
- Verificar logs de `login_code.authenticate.invalid_or_expired`
- Verificar se há códigos recentes para o email

### Problema: Jobs presos na fila

**Verificações:**
1. Há jobs do Stripe na fila?
2. Worker está processando outras filas?
3. Jobs estão falhando repetidamente?

**Solução:**
```bash
# Limpar jobs do Stripe
python server/scripts/check_worker_queue.py --clean-stripe
```

## 📝 Notas Importantes

1. **Endpoints do Stripe foram desabilitados temporariamente**
   - Se precisar reativar, descomente as linhas em `server/polar/api.py`
   - Mas certifique-se de que não há mais webhooks do Stripe chegando

2. **Tasks do Stripe foram removidas do worker**
   - Se precisar reativar, descomente as linhas em `server/polar/tasks.py`
   - Mas certifique-se de que não há mais jobs do Stripe na fila

3. **Logs de debug foram adicionados**
   - Remover logs detalhados em produção se necessário
   - Logs incluem informações sensíveis (prefixo do código)

4. **Script de limpeza de fila**
   - Use com cuidado
   - Sempre verifique antes de limpar
   - Limpar todas as filas remove TODOS os jobs (incluindo emails pendentes)

## 🎯 Objetivo Final

Garantir que:
1. ✅ Worker processa emails corretamente
2. ✅ Código de verificação funciona
3. ✅ Login completo funciona com usuário admin
4. ✅ Não há jobs presos bloqueando o processamento



