#!/bin/bash
# 🚀 COMANDOS DE DEPLOY - COPIE E COLE NO TERMINAL
# Data: 2025-11-10

set -e

echo "════════════════════════════════════════════════════════════════"
echo "  🚀 DEPLOY DE CORREÇÕES DE AUTENTICAÇÃO - FLUU"
echo "════════════════════════════════════════════════════════════════"
echo ""
echo "📋 Este script vai atualizar as variáveis de ambiente nos serviços:"
echo "   - Backend (fluu-api)"
echo "   - Worker (fluu-worker)"
echo "   - Frontend (fluu-web)"
echo ""
echo "⏱️  Tempo estimado: ~2 minutos"
echo ""
read -p "Pressione ENTER para continuar ou Ctrl+C para cancelar..."

cd /Users/<USER>/Documents/www/Gateways/polar/deploy/cloud-run

echo ""
echo "════════════════════════════════════════════════════════════════"
echo "  1️⃣ ATUALIZANDO BACKEND (fluu-api)"
echo "════════════════════════════════════════════════════════════════"
echo ""
echo "📝 Variáveis sendo atualizadas:"
echo "   - POLAR_USER_SESSION_COOKIE_DOMAIN: .fluu.digital"
echo "   - POLAR_USER_SESSION_COOKIE_KEY: fluu_session"
echo "   - P<PERSON><PERSON>_GOOGLE_CLIENT_ID: 923457232981-***"
echo "   - POLAR_GOOGLE_CLIENT_SECRET: GOCSPX-***"
echo ""

gcloud run services update fluu-api \
  --region us-east1 \
  --env-vars-file=env.yaml

echo ""
echo "✅ Backend atualizado!"
echo ""

echo "════════════════════════════════════════════════════════════════"
echo "  2️⃣ ATUALIZANDO WORKER (fluu-worker)"
echo "════════════════════════════════════════════════════════════════"
echo ""
echo "📝 Variáveis sendo atualizadas:"
echo "   - POLAR_USER_SESSION_COOKIE_DOMAIN: .fluu.digital"
echo "   - POLAR_USER_SESSION_COOKIE_KEY: fluu_session"
echo "   - POLAR_GOOGLE_CLIENT_ID: 923457232981-***"
echo "   - POLAR_GOOGLE_CLIENT_SECRET: GOCSPX-***"
echo ""

gcloud run services update fluu-worker \
  --region us-east1 \
  --env-vars-file=env-worker.yaml

echo ""
echo "✅ Worker atualizado!"
echo ""

echo "════════════════════════════════════════════════════════════════"
echo "  3️⃣ ATUALIZANDO FRONTEND (fluu-web)"
echo "════════════════════════════════════════════════════════════════"
echo ""
echo "📝 Variáveis sendo atualizadas:"
echo "   - POLAR_AUTH_COOKIE_KEY: fluu_session"
echo ""

gcloud run services update fluu-web \
  --region us-east1 \
  --env-vars-file=env-frontend.yaml

echo ""
echo "✅ Frontend atualizado!"
echo ""

echo "════════════════════════════════════════════════════════════════"
echo "  ✅ DEPLOY CONCLUÍDO COM SUCESSO!"
echo "════════════════════════════════════════════════════════════════"
echo ""
echo "📝 URLs dos serviços:"
echo ""

BACKEND_URL=$(gcloud run services describe fluu-api --region us-east1 --format="get(status.url)" 2>/dev/null)
WORKER_URL=$(gcloud run services describe fluu-worker --region us-east1 --format="get(status.url)" 2>/dev/null)
FRONTEND_URL=$(gcloud run services describe fluu-web --region us-east1 --format="get(status.url)" 2>/dev/null)

echo "   Backend:  ${BACKEND_URL}"
echo "   Worker:   ${WORKER_URL}"
echo "   Frontend: ${FRONTEND_URL}"
echo ""

echo "════════════════════════════════════════════════════════════════"
echo "  ⚠️  PRÓXIMOS PASSOS OBRIGATÓRIOS"
echo "════════════════════════════════════════════════════════════════"
echo ""
echo "1️⃣ CONFIGURAR GOOGLE CLOUD CONSOLE"
echo ""
echo "   Acesse: https://console.cloud.google.com/apis/credentials"
echo ""
echo "   No Client ID OAuth 2.0, adicione:"
echo ""
echo "   📍 Authorized redirect URIs:"
echo "      https://api.fluu.digital/v1/integrations/google/callback"
echo "      ${BACKEND_URL}/v1/integrations/google/callback"
echo ""
echo "   📍 Authorized JavaScript origins:"
echo "      https://fluu.digital"
echo "      https://api.fluu.digital"
echo "      ${FRONTEND_URL}"
echo "      ${BACKEND_URL}"
echo ""
echo "   ⏱️  AGUARDE ~5 MINUTOS após salvar"
echo ""
echo "────────────────────────────────────────────────────────────────"
echo ""
echo "2️⃣ TESTAR LOGIN"
echo ""
echo "   Acesse: https://fluu.digital/login"
echo ""
echo "   Teste 1: Login com Email"
echo "   ✓ Inserir email"
echo "   ✓ Receber código OTP no email"
echo "   ✓ Inserir código"
echo "   ✓ Deve redirecionar para /dashboard"
echo "   ✓ Deve permanecer logado após refresh"
echo ""
echo "   Teste 2: Login com Google"
echo "   ✓ Clicar em 'Continue with Google'"
echo "   ✓ Autorizar no Google"
echo "   ✓ Deve redirecionar para /dashboard"
echo "   ✓ Deve permanecer logado após refresh"
echo ""
echo "────────────────────────────────────────────────────────────────"
echo ""
echo "3️⃣ VERIFICAR LOGS SE HOUVER PROBLEMAS"
echo ""
echo "   Backend:"
echo "   gcloud run services logs read fluu-api --region us-east1 --limit 50"
echo ""
echo "   Worker:"
echo "   gcloud run services logs read fluu-worker --region us-east1 --limit 50"
echo ""
echo "   Frontend:"
echo "   gcloud run services logs read fluu-web --region us-east1 --limit 50"
echo ""
echo "════════════════════════════════════════════════════════════════"
echo ""
echo "📚 DOCUMENTAÇÃO COMPLETA:"
echo "   - DEPLOY_NOW.md"
echo "   - GOOGLE_OAUTH_SETUP.md"
echo "   - DEPLOY_CHECKLIST.md"
echo ""
echo "✅ Deploy concluído! Siga os próximos passos acima."
echo ""


