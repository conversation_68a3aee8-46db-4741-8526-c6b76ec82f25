#!/usr/bin/env python3
"""
Script de teste para validar o envio de email com código de acesso
Testa diretamente o serviço de login code, contornando o rate limiting
"""

import asyncio
import os
import sys
from pathlib import Path

# Configurar variáveis de ambiente ANTES de importar módulos
os.environ["POLAR_ENV"] = "development"
os.environ["POLAR_POSTGRES_USER"] = "neondb_owner"
os.environ["POLAR_POSTGRES_PWD"] = "npg_iX2kVBloh1YT"
os.environ["POLAR_POSTGRES_HOST"] = "ep-dry-cell-achqzu4j-pooler.sa-east-1.aws.neon.tech"
os.environ["POLAR_POSTGRES_DATABASE"] = "neondb"
os.environ["POLAR_POSTGRES_SSLMODE"] = "require"

# Adicionar o diretório server ao path
server_dir = Path(__file__).parent
sys.path.insert(0, str(server_dir))

from polar.config import settings
from polar.email.sender import email_sender
from polar.kit.db.postgres import create_async_sessionmaker
from polar.login_code.service import login_code as login_code_service
from polar.postgres import create_async_engine


async def test_email_login():
    """Testa o envio de email com código de acesso"""
    print("📧 Testando Envio de Email com Código de Acesso...\n")
    
    test_email = "<EMAIL>"
    
    print(f"📋 Configurações:")
    print(f"   Email: {test_email}")
    print(f"   EMAIL_SENDER: {settings.EMAIL_SENDER}")
    print(f"   Database: {settings.POSTGRES_DATABASE}")
    print(f"   Host: {settings.POSTGRES_HOST}\n")
    
    # Criar engine e session
    print("🔌 Conectando ao banco de dados...")
    engine = create_async_engine("script")
    sessionmaker = create_async_sessionmaker(engine)
    
    try:
        async with sessionmaker() as session:
            print("✅ Conectado ao banco de dados!\n")
            
            # Teste 1: Request login code
            print("1️⃣ Testando Request de Login Code...")
            try:
                code_model, code = await login_code_service.request(
                    session,
                    test_email,
                    return_to=None,
                    signup_attribution=None,
                )
                
                print(f"   ✅ Login code criado!")
                print(f"   📧 Email: {code_model.email}")
                print(f"   🔑 Código: {code}")
                print(f"   ⏰ Expira em: {code_model.expires_at}\n")
                
                # Teste 2: Send email
                print("2️⃣ Testando Envio de Email...")
                try:
                    await login_code_service.send(code_model, code)
                    
                    print(f"   ✅ Email enfileirado para envio!")
                    print(f"   📧 Para: {test_email}")
                    print(f"   📝 Subject: Sign in to Fluu")
                    print(f"   🔑 Código: {code}\n")
                    
                    if settings.EMAIL_SENDER.value == "logger":
                        print("   💡 Modo de desenvolvimento: Email será logado (não enviado)")
                        print("   📋 Verifique os logs do worker para ver o email\n")
                    else:
                        print("   💡 Modo de produção: Email será enviado via Resend\n")
                    
                    print("✅ Teste concluído com sucesso!")
                    return True
                    
                except Exception as e:
                    print(f"   ❌ Erro ao enviar email:")
                    print(f"      {type(e).__name__}: {e}")
                    import traceback
                    traceback.print_exc()
                    return False
                    
            except Exception as e:
                print(f"   ❌ Erro ao criar login code:")
                print(f"      {type(e).__name__}: {e}")
                import traceback
                traceback.print_exc()
                return False
                
    except Exception as e:
        print(f"❌ Erro ao conectar ao banco de dados:")
        print(f"   {type(e).__name__}: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        await engine.dispose()


if __name__ == "__main__":
    success = asyncio.run(test_email_login())
    sys.exit(0 if success else 1)



