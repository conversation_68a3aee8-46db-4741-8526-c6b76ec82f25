#!/usr/bin/env python3
"""
Script para verificar e limpar jobs presos na fila do worker.

Uso:
    python scripts/check_worker_queue.py --check
    python scripts/check_worker_queue.py --clean-stripe
    python scripts/check_worker_queue.py --clean-all
"""

import argparse
import asyncio
import json
from typing import Any

import redis.asyncio as redis


async def get_redis_client() -> redis.Redis:
    """Cria cliente Redis usando configurações do settings."""
    import sys
    from pathlib import Path
    
    # Adicionar o diretório server ao path
    server_dir = Path(__file__).parent.parent
    if str(server_dir) not in sys.path:
        sys.path.insert(0, str(server_dir))
    
    from polar.config import settings
    
    redis_url = settings.redis_url
    return redis.from_url(redis_url, decode_responses=False)


async def check_queue(queue_name: str) -> dict[str, Any]:
    """Verifica status de uma fila."""
    client = await get_redis_client()
    
    # Tamanho da fila
    queue_length = await client.llen(f"dramatiq:{queue_name}")
    
    # Mensagens na fila (primeiras 10)
    messages = []
    if queue_length > 0:
        message_ids = await client.lrange(f"dramatiq:{queue_name}", 0, 9)
        for msg_id in message_ids:
            msg_id_str = msg_id.decode() if isinstance(msg_id, bytes) else msg_id
            # Buscar mensagem no hash
            msg_data = await client.hget(f"dramatiq:{queue_name}.msgs", msg_id_str)
            if msg_data:
                try:
                    # Tentar decodificar mensagem (pode ser binária)
                    if isinstance(msg_data, bytes):
                        # Dramatiq usa pickle, mas podemos tentar extrair info básica
                        msg_str = msg_data[:200].decode('utf-8', errors='ignore')
                    else:
                        msg_str = str(msg_data)[:200]
                    messages.append({
                        "id": msg_id_str,
                        "preview": msg_str,
                    })
                except Exception as e:
                    messages.append({
                        "id": msg_id_str,
                        "error": str(e),
                    })
    
    return {
        "queue_name": queue_name,
        "length": queue_length,
        "messages": messages,
    }


async def check_all_queues() -> None:
    """Verifica todas as filas."""
    queues = ["high_priority", "default"]
    
    print("=" * 60)
    print("Verificando filas do worker...")
    print("=" * 60)
    
    for queue_name in queues:
        info = await check_queue(queue_name)
        print(f"\n📦 Fila: {info['queue_name']}")
        print(f"   Tamanho: {info['length']} jobs")
        
        if info['messages']:
            print(f"   Primeiros {len(info['messages'])} jobs:")
            for msg in info['messages']:
                print(f"   - ID: {msg['id'][:50]}...")
                if 'preview' in msg:
                    # Procurar por "stripe" na preview
                    if 'stripe' in msg['preview'].lower():
                        print(f"     ⚠️  JOB DO STRIPE DETECTADO!")
                if 'error' in msg:
                    print(f"     Erro: {msg['error']}")


async def clean_stripe_jobs() -> None:
    """Remove jobs do Stripe das filas."""
    client = await get_redis_client()
    
    queues = ["high_priority", "default"]
    total_removed = 0
    
    print("=" * 60)
    print("Limpando jobs do Stripe...")
    print("=" * 60)
    
    for queue_name in queues:
        queue_key = f"dramatiq:{queue_name}"
        msgs_key = f"dramatiq:{queue_name}.msgs"
        
        # Obter todos os IDs da fila
        message_ids = await client.lrange(queue_key, 0, -1)
        
        removed = 0
        for msg_id in message_ids:
            msg_id_str = msg_id.decode() if isinstance(msg_id, bytes) else msg_id
            
            # Buscar mensagem
            msg_data = await client.hget(msgs_key, msg_id_str)
            if msg_data:
                # Verificar se é job do Stripe
                try:
                    msg_str = msg_data.decode('utf-8', errors='ignore') if isinstance(msg_data, bytes) else str(msg_data)
                    if 'stripe.webhook' in msg_str or 'stripe' in msg_str.lower():
                        # Remover da fila
                        await client.lrem(queue_key, 1, msg_id)
                        # Remover do hash
                        await client.hdel(msgs_key, msg_id_str)
                        removed += 1
                        print(f"   Removido job do Stripe: {msg_id_str[:50]}...")
                except Exception as e:
                    print(f"   Erro ao processar {msg_id_str}: {e}")
        
        total_removed += removed
        print(f"\n📦 Fila {queue_name}: {removed} jobs removidos")
    
    print(f"\n✅ Total de jobs do Stripe removidos: {total_removed}")


async def clean_all_queues() -> None:
    """Limpa todas as filas (CUIDADO!)."""
    client = await get_redis_client()
    
    queues = ["high_priority", "default"]
    
    print("=" * 60)
    print("⚠️  ATENÇÃO: Isso vai limpar TODAS as filas!")
    print("=" * 60)
    
    response = input("Tem certeza? Digite 'SIM' para confirmar: ")
    if response != "SIM":
        print("Operação cancelada.")
        return
    
    for queue_name in queues:
        queue_key = f"dramatiq:{queue_name}"
        msgs_key = f"dramatiq:{queue_name}.msgs"
        
        length = await client.llen(queue_key)
        await client.delete(queue_key)
        await client.delete(msgs_key)
        
        print(f"✅ Fila {queue_name} limpa ({length} jobs removidos)")


async def main() -> None:
    parser = argparse.ArgumentParser(description="Verificar e limpar filas do worker")
    parser.add_argument(
        "--check",
        action="store_true",
        help="Verificar status das filas",
    )
    parser.add_argument(
        "--clean-stripe",
        action="store_true",
        help="Remover jobs do Stripe das filas",
    )
    parser.add_argument(
        "--clean-all",
        action="store_true",
        help="Limpar todas as filas (CUIDADO!)",
    )
    
    args = parser.parse_args()
    
    if args.check:
        await check_all_queues()
    elif args.clean_stripe:
        await clean_stripe_jobs()
    elif args.clean_all:
        await clean_all_queues()
    else:
        parser.print_help()


if __name__ == "__main__":
    asyncio.run(main())

