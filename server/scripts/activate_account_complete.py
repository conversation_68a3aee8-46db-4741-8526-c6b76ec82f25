"""
Script COMPLETO para ativar uma conta para receber pagamentos.

Este script faz TUDO que é necessário:
- Cria conta se não existir
- Define Stripe ID
- Marca detalhes como submetidos
- Habilita saques e cobranças
- Verifica identidade do admin
- Aprova organização
- Status ACTIVE

Uso:
    # Para organização
    uv run python -m scripts.activate_account_complete --organization-slug ismc --admin-email <EMAIL> --no-dry-run

    # Para usuário
    uv run python -m scripts.activate_account_complete --user-email <EMAIL> --no-dry-run
"""

import asyncio
import logging.config
from datetime import datetime, UTC
from functools import wraps
from typing import Any

import structlog
import typer
from sqlalchemy import select
from sqlalchemy.orm import joinedload

from polar.account.repository import AccountRepository
from polar.enums import AccountType
from polar.kit.db.postgres import create_async_sessionmaker
from polar.models import Account, Organization, User
from polar.models.user import IdentityVerificationStatus
from polar.organization.repository import OrganizationRepository
from polar.organization.service import OrganizationStatus
from polar.postgres import AsyncSession, create_async_engine
from polar.user.repository import UserRepository

cli = typer.Typer()


def drop_all(*args: Any, **kwargs: Any) -> Any:
    raise structlog.DropEvent


structlog.configure(processors=[drop_all])
logging.config.dictConfig(
    {
        "version": 1,
        "disable_existing_loggers": True,
    }
)


def typer_async(f):  # type: ignore
    @wraps(f)
    def wrapper(*args, **kwargs):  # type: ignore
        return asyncio.run(f(*args, **kwargs))

    return wrapper


@cli.command()
@typer_async
async def activate(
    organization_slug: str = typer.Option(None, "--organization-slug", "-o", help="Slug da organização"),
    admin_email: str = typer.Option(None, "--admin-email", "-a", help="Email do admin da conta"),
    user_email: str = typer.Option(None, "--user-email", "-u", help="Email do usuário (modo simples)"),
    country: str = typer.Option("BR", help="País"),
    currency: str = typer.Option("BRL", help="Moeda"),
    next_review_threshold: int = typer.Option(10000, help="Threshold em centavos"),
    dry_run: bool = typer.Option(True, help="Se True, apenas mostra o que seria feito"),
) -> None:
    """Ativa conta completamente para receber pagamentos (FAZ TUDO!)."""
    
    if not organization_slug and not user_email:
        typer.echo("❌ Forneça --organization-slug ou --user-email")
        raise typer.Exit(code=1)
    
    engine = create_async_engine("script")
    sessionmaker = create_async_sessionmaker(engine)
    
    async with sessionmaker() as session:
        organization = None
        account = None
        admin = None
        
        # Modo 1: Ativar organização
        if organization_slug:
            typer.echo()
            typer.echo("🏢 MODO: Ativação de Organização")
            typer.echo()
            
            # Buscar organização
            organization_repository = OrganizationRepository.from_session(session)
            stmt = (
                select(Organization)
                .where(Organization.slug == organization_slug)
                .options(joinedload(Organization.account).joinedload(Account.admin))
            )
            result = await session.execute(stmt)
            organization = result.unique().scalars().one_or_none()
            
            if not organization:
                typer.echo(f"❌ Organização '{organization_slug}' não encontrada")
                raise typer.Exit(code=1)
            
            # Buscar admin
            if admin_email:
                user_repository = UserRepository.from_session(session)
                stmt = select(User).where(User.email == admin_email)
                result = await session.execute(stmt)
                admin = result.scalars().first()
                
                if not admin:
                    typer.echo(f"❌ Admin '{admin_email}' não encontrado")
                    raise typer.Exit(code=1)
            elif organization.account:
                admin = organization.account.admin
            else:
                # Buscar primeiro admin do sistema
                stmt = select(User).where(User.is_admin == True).limit(1)
                result = await session.execute(stmt)
                admin = result.scalars().first()
            
            account = organization.account
        
        # Modo 2: Ativar usuário simples
        elif user_email:
            typer.echo()
            typer.echo("👤 MODO: Ativação de Usuário")
            typer.echo()
            
            user_repository = UserRepository.from_session(session)
            stmt = select(User).where(User.email == user_email)
            result = await session.execute(stmt)
            admin = result.scalars().first()
            
            if not admin:
                typer.echo(f"❌ Usuário '{user_email}' não encontrado")
                raise typer.Exit(code=1)
            
            if admin.account_id:
                account_repository = AccountRepository.from_session(session)
                account = await account_repository.get_by_id(admin.account_id)
        
        if not admin:
            typer.echo("❌ Admin não encontrado")
            raise typer.Exit(code=1)
        
        # Status atual
        typer.echo("=" * 80)
        typer.echo("📊 STATUS ATUAL")
        typer.echo("=" * 80)
        typer.echo()
        
        if organization:
            typer.echo(f"🏢 Organização: {organization.name} ({organization.slug})")
            typer.echo(f"   Status: {organization.status}")
            typer.echo(f"   Details Submitted: {organization.details_submitted_at or 'Não'}")
            typer.echo()
        
        typer.echo(f"👤 Admin: {admin.email}")
        typer.echo(f"   Identity Status: {admin.identity_verification_status.value}")
        typer.echo(f"   Verified: {'✓' if admin.identity_verified else '✗'}")
        typer.echo()
        
        if account:
            typer.echo(f"💳 Conta Existente: {account.id}")
            typer.echo(f"   Status: {account.status.value}")
            typer.echo(f"   Stripe ID: {account.stripe_id or 'Não'}")
            typer.echo(f"   Admin: {account.admin.email}")
        else:
            typer.echo("💳 Conta: Nenhuma (será criada)")
        typer.echo()
        
        # Listar mudanças
        changes = []
        
        if not account:
            changes.append("✨ CRIAR conta de pagamento")
        else:
            if account.status != Account.Status.ACTIVE:
                changes.append(f"📝 Status conta: {account.status.value} → active")
            if not account.stripe_id:
                changes.append("✨ ADICIONAR Stripe ID")
            if not account.is_payouts_enabled:
                changes.append("✅ HABILITAR saques")
            if not account.is_charges_enabled:
                changes.append("✅ HABILITAR cobranças")
            if not account.is_details_submitted:
                changes.append("✅ MARCAR details como submetidos")
            if account.admin_id != admin.id:
                changes.append(f"👤 MUDAR admin: {account.admin.email} → {admin.email}")
        
        if not admin.identity_verified:
            changes.append("🔐 VERIFICAR identidade do admin")
        
        if organization:
            if organization.status != OrganizationStatus.ACTIVE:
                changes.append(f"📝 Status org: {organization.status} → active")
            if not organization.details_submitted_at:
                changes.append("✨ DEFINIR details_submitted_at")
            if account and organization.account_id != account.id:
                changes.append("🔗 VINCULAR conta à organização")
        
        if not changes:
            typer.echo("=" * 80)
            typer.echo("✅ TUDO JÁ ESTÁ ATIVADO!")
            typer.echo("=" * 80)
            return
        
        typer.echo("📝 MUDANÇAS A SEREM APLICADAS:")
        typer.echo()
        for change in changes:
            typer.echo(f"   {change}")
        typer.echo()
        
        if dry_run:
            typer.echo("=" * 80)
            typer.echo("🏃 DRY RUN MODE - Nenhuma mudança foi aplicada")
            typer.echo("   Use --no-dry-run para executar")
            typer.echo("=" * 80)
            return
        
        # EXECUTAR TODAS AS MUDANÇAS
        typer.echo("=" * 80)
        typer.echo("🚀 ATIVANDO CONTA COMPLETAMENTE...")
        typer.echo("=" * 80)
        typer.echo()
        
        # 1. Criar ou atualizar conta
        if not account:
            typer.echo("📝 Criando conta de pagamento...")
            account = Account(
                account_type=AccountType.stripe,
                admin_id=admin.id,
                country=country,
                currency=currency,
                status=Account.Status.ACTIVE,
                is_details_submitted=True,
                is_charges_enabled=True,
                is_payouts_enabled=True,
                processor_fees_applicable=True,
                next_review_threshold=next_review_threshold,
                stripe_id=f"acct_fake_{admin.id.hex[:16]}",
                data={},
            )
            session.add(account)
            await session.flush()
            typer.echo(f"   ✓ Conta criada: {account.id}")
        else:
            typer.echo("📝 Atualizando conta existente...")
            account.status = Account.Status.ACTIVE
            account.is_payouts_enabled = True
            account.is_charges_enabled = True
            account.is_details_submitted = True
            account.admin_id = admin.id
            account.next_review_threshold = next_review_threshold
            if not account.stripe_id:
                account.stripe_id = f"acct_fake_{account.id.hex[:16]}"
            session.add(account)
            typer.echo("   ✓ Conta atualizada")
        
        # 2. Verificar identidade do admin
        if not admin.identity_verified:
            typer.echo("📝 Verificando identidade do admin...")
            user_repository = UserRepository.from_session(session)
            await user_repository.update(
                admin,
                update_dict={
                    "identity_verification_status": IdentityVerificationStatus.verified
                }
            )
            typer.echo("   ✓ Identidade verificada")
        
        # 3. Vincular usuário à conta
        if not admin.account_id:
            admin.account_id = account.id
            session.add(admin)
            typer.echo("   ✓ Usuário vinculado à conta")
        
        # 4. Atualizar organização (se aplicável)
        if organization:
            typer.echo("📝 Atualizando organização...")
            organization.status = OrganizationStatus.ACTIVE
            organization.status_updated_at = datetime.now(UTC)
            organization.next_review_threshold = next_review_threshold
            organization.account_id = account.id
            
            if not organization.details_submitted_at:
                organization.details_submitted_at = datetime.now(UTC)
            
            if not organization.details:
                organization.details = {
                    "business": {
                        "name": organization.name,
                        "type": "individual"
                    }
                }
            
            session.add(organization)
            typer.echo("   ✓ Organização atualizada")
        
        # Commit
        await session.commit()
        
        typer.echo()
        typer.echo("=" * 80)
        typer.echo("✅ ATIVAÇÃO COMPLETA!")
        typer.echo("=" * 80)
        typer.echo()
        
        # Status final
        typer.echo("📊 STATUS FINAL:")
        typer.echo()
        
        if organization:
            typer.echo(f"🏢 Organização: {organization.name}")
            typer.echo(f"   Status: active ✅")
            typer.echo(f"   Details Submitted: ✅")
            typer.echo()
        
        typer.echo(f"👤 Admin: {admin.email}")
        typer.echo(f"   Identity Verified: ✅")
        typer.echo()
        
        typer.echo(f"💳 Conta: {account.id}")
        typer.echo(f"   Status: active ✅")
        typer.echo(f"   Stripe ID: {account.stripe_id} ✅")
        typer.echo(f"   Saques: ✅")
        typer.echo(f"   Cobranças: ✅")
        typer.echo(f"   Details Submitted: ✅")
        typer.echo()
        typer.echo("🎉 PRONTA PARA RECEBER PAGAMENTOS!")
        typer.echo()


if __name__ == "__main__":
    cli()


