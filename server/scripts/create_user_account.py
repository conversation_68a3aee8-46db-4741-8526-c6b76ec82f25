"""
Script para criar conta para um usuário específico.

Uso:
    uv run python -m scripts.create_user_account --email <EMAIL> --no-dry-run
"""

import asyncio
import logging.config
from functools import wraps
from typing import Any

import structlog
import typer
from sqlalchemy import select

from polar.account.repository import AccountRepository
from polar.enums import AccountType
from polar.kit.db.postgres import create_async_sessionmaker
from polar.models import Account, User
from polar.postgres import AsyncSession, create_async_engine
from polar.user.repository import UserRepository

cli = typer.Typer()


def drop_all(*args: Any, **kwargs: Any) -> Any:
    raise structlog.DropEvent


structlog.configure(processors=[drop_all])
logging.config.dictConfig(
    {
        "version": 1,
        "disable_existing_loggers": True,
    }
)


def typer_async(f):  # type: ignore
    @wraps(f)
    def wrapper(*args, **kwargs):  # type: ignore
        return asyncio.run(f(*args, **kwargs))

    return wrapper


@cli.command()
@typer_async
async def create_account(
    email: str = typer.Argument(..., help="Email do usuário"),
    country: str = typer.Option("BR", help="Código do país"),
    currency: str = typer.Option("BRL", help="Moeda"),
    next_review_threshold: int = typer.Option(10000, help="Threshold em centavos"),
    dry_run: bool = typer.Option(True, help="Se True, apenas mostra o que seria feito"),
) -> None:
    """Cria e aprova conta de pagamento para um usuário."""
    
    engine = create_async_engine("script")
    sessionmaker = create_async_sessionmaker(engine)
    
    async with sessionmaker() as session:
        # Buscar usuário
        user_repository = UserRepository.from_session(session)
        stmt = select(User).where(User.email == email)
        result = await session.execute(stmt)
        user = result.scalars().first()
        
        if not user:
            typer.echo(f"❌ Usuário '{email}' não encontrado no sistema")
            typer.echo()
            typer.echo("💡 O usuário precisa se cadastrar primeiro via interface web:")
            typer.echo("   https://fluu.digital")
            raise typer.Exit(code=1)
        
        # Verificar se já tem conta
        if user.account_id:
            typer.echo(f"ℹ️  Usuário já tem uma conta vinculada: {user.account_id}")
            typer.echo()
            typer.echo("   Use o script de aprovação:")
            typer.echo(f"   uv run python -m scripts.approve_account_for_payout --email {email} --no-dry-run")
            raise typer.Exit(code=0)
        
        # Exibir informações
        typer.echo()
        typer.echo("=" * 80)
        typer.echo("📊 USUÁRIO ENCONTRADO")
        typer.echo("=" * 80)
        typer.echo()
        typer.echo(f"👤 Email: {user.email}")
        typer.echo(f"   ID: {user.id}")
        typer.echo(f"   Email Verificado: {'✓' if user.email_verified else '✗'}")
        typer.echo(f"   Admin: {'✓' if user.is_admin else '✗'}")
        typer.echo(f"   Conta Atual: {user.account_id or 'Nenhuma'}")
        typer.echo()
        
        typer.echo("📝 MUDANÇAS A SEREM APLICADAS:")
        typer.echo()
        typer.echo("   ✨ Criar nova conta de pagamento")
        typer.echo(f"      - País: {country}")
        typer.echo(f"      - Moeda: {currency}")
        typer.echo(f"      - Tipo: stripe")
        typer.echo(f"      - Admin: {email}")
        typer.echo("   ✅ Habilitar saques e cobranças")
        typer.echo(f"   💰 Threshold: {next_review_threshold} centavos (${next_review_threshold/100:.2f})")
        typer.echo()
        
        if dry_run:
            typer.echo("=" * 80)
            typer.echo("🏃 DRY RUN MODE - Nenhuma mudança foi aplicada")
            typer.echo("   Use --no-dry-run para executar de verdade")
            typer.echo("=" * 80)
            return
        
        # EXECUTAR
        typer.echo("=" * 80)
        typer.echo("🚀 CRIANDO CONTA...")
        typer.echo("=" * 80)
        typer.echo()
        
        # Criar conta
        account = Account(
            account_type=AccountType.stripe,
            admin_id=user.id,
            country=country,
            currency=currency,
            status=Account.Status.ACTIVE,
            is_details_submitted=True,
            is_charges_enabled=True,
            is_payouts_enabled=True,
            processor_fees_applicable=True,
            next_review_threshold=next_review_threshold,
            data={},
        )
        
        session.add(account)
        await session.flush()
        
        typer.echo(f"   ✓ Conta criada: {account.id}")
        
        # Vincular ao usuário
        user.account_id = account.id
        session.add(user)
        await session.flush()
        
        typer.echo(f"   ✓ Conta vinculada ao usuário")
        
        # Commit
        await session.commit()
        
        typer.echo()
        typer.echo("=" * 80)
        typer.echo("✅ CONCLUÍDO COM SUCESSO!")
        typer.echo("=" * 80)
        typer.echo()
        
        # Status final
        typer.echo("📊 STATUS FINAL:")
        typer.echo()
        typer.echo(f"👤 Usuário: {user.email}")
        typer.echo(f"💳 Conta: {account.id}")
        typer.echo(f"   Status: {account.status.value}")
        typer.echo(f"   Saques: {'✓' if account.is_payouts_enabled else '✗'}")
        typer.echo(f"   Cobranças: {'✓' if account.is_charges_enabled else '✗'}")
        typer.echo()
        
        if account.is_payout_ready():
            typer.echo("   ✅ PRONTA PARA SAQUES!")
        else:
            typer.echo("   ⚠️  Ainda não pronta para saques")
        
        typer.echo()


if __name__ == "__main__":
    cli()


