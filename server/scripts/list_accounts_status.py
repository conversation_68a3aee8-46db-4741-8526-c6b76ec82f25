"""
Script para listar contas e seus status de payout.

Este script lista todas as contas do sistema e mostra:
- Status da conta
- Admin
- Organizações vinculadas
- Se está pronta para saques

Útil para identificar quais contas precisam ser aprovadas.

Uso:
    # Listar todas as contas
    uv run python -m scripts.list_accounts_status

    # Filtrar por status
    uv run python -m scripts.list_accounts_status --status under_review
    uv run python -m scripts.list_accounts_status --status created
    uv run python -m scripts.list_accounts_status --status active

    # Mostrar apenas contas NÃO prontas para saque
    uv run python -m scripts.list_accounts_status --not-payout-ready

    # Limitar número de resultados
    uv run python -m scripts.list_accounts_status --limit 10
"""

import asyncio
import logging.config
from functools import wraps
from typing import Any

import structlog
import typer
from sqlalchemy import select
from sqlalchemy.orm import joinedload

from polar.account.repository import AccountRepository
from polar.kit.db.postgres import create_async_sessionmaker
from polar.models import Account, Organization
from polar.postgres import AsyncSession, create_async_engine

cli = typer.Typer()


def drop_all(*args: Any, **kwargs: Any) -> Any:
    raise structlog.DropEvent


structlog.configure(processors=[drop_all])
logging.config.dictConfig(
    {
        "version": 1,
        "disable_existing_loggers": True,
    }
)


def typer_async(f):  # type: ignore
    @wraps(f)
    def wrapper(*args, **kwargs):  # type: ignore
        return asyncio.run(f(*args, **kwargs))

    return wrapper


def get_status_emoji(account: Account) -> str:
    """Retorna emoji baseado no status da conta."""
    if account.is_payout_ready():
        return "✅"
    elif account.is_under_review():
        return "⚠️ "
    elif account.status == Account.Status.DENIED:
        return "❌"
    elif account.status == Account.Status.ACTIVE:
        return "⚡"
    else:
        return "⏳"


def format_account_line(account: Account, organizations: list[Organization]) -> str:
    """Formata uma linha para exibição da conta."""
    status_emoji = get_status_emoji(account)
    status_name = account.status.get_display_name()
    
    # Informações de payout
    payout_info = []
    if account.is_payouts_enabled:
        payout_info.append("Saques✓")
    else:
        payout_info.append("Saques✗")
    
    if account.is_charges_enabled:
        payout_info.append("Cobranças✓")
    else:
        payout_info.append("Cobranças✗")
    
    payout_str = " | ".join(payout_info)
    
    # Organizações
    org_slugs = [org.slug for org in organizations] if organizations else ["Sem org"]
    org_str = ", ".join(org_slugs[:3])  # Mostrar no máximo 3
    if len(org_slugs) > 3:
        org_str += f" +{len(org_slugs) - 3} mais"
    
    return (
        f"{status_emoji} {status_name:20} | "
        f"{account.admin.email:35} | "
        f"{payout_str:30} | "
        f"{org_str}"
    )


@cli.command()
@typer_async
async def list_accounts(
    status: str = typer.Option(
        None,
        "--status",
        "-s",
        help="Filtrar por status (created, onboarding_started, under_review, denied, active)",
    ),
    not_payout_ready: bool = typer.Option(
        False,
        "--not-payout-ready",
        "-n",
        help="Mostrar apenas contas NÃO prontas para saque",
    ),
    limit: int = typer.Option(
        None, "--limit", "-l", help="Limitar número de resultados"
    ),
) -> None:
    """Lista contas e seus status de payout."""
    
    engine = create_async_engine("script")
    sessionmaker = create_async_sessionmaker(engine)
    
    async with sessionmaker() as session:
        # Construir query
        stmt = (
            select(Account)
            .options(
                joinedload(Account.admin),
                joinedload(Account.organizations),
            )
            .order_by(Account.created_at.desc())
        )
        
        # Filtrar por status se fornecido
        if status:
            try:
                status_enum = Account.Status[status.upper()]
                stmt = stmt.where(Account.status == status_enum)
            except KeyError:
                typer.echo(f"❌ Status inválido: {status}")
                typer.echo("Status válidos: created, onboarding_started, under_review, denied, active")
                raise typer.Exit(code=1)
        
        # Limitar resultados
        if limit:
            stmt = stmt.limit(limit)
        
        result = await session.execute(stmt)
        accounts = result.unique().scalars().all()
        
        if not accounts:
            typer.echo("📭 Nenhuma conta encontrada")
            return
        
        # Filtrar contas não prontas para payout se solicitado
        if not_payout_ready:
            accounts = [acc for acc in accounts if not acc.is_payout_ready()]
        
        # Cabeçalho
        typer.echo()
        typer.echo(f"📊 Total de contas: {len(accounts)}")
        typer.echo()
        typer.echo("=" * 140)
        typer.echo(
            f"{'Status':22} | "
            f"{'Admin Email':35} | "
            f"{'Capabilities':30} | "
            f"Organizações"
        )
        typer.echo("=" * 140)
        
        # Contar por status
        status_counts = {}
        payout_ready_count = 0
        
        for account in accounts:
            status_key = account.status.get_display_name()
            status_counts[status_key] = status_counts.get(status_key, 0) + 1
            
            if account.is_payout_ready():
                payout_ready_count += 1
            
            typer.echo(format_account_line(account, account.organizations))
        
        # Resumo
        typer.echo("=" * 140)
        typer.echo()
        typer.echo("📈 Resumo:")
        for status_name, count in sorted(status_counts.items()):
            typer.echo(f"   {status_name}: {count}")
        typer.echo()
        typer.echo(f"✅ Prontas para saque: {payout_ready_count}")
        typer.echo(f"⚠️  NÃO prontas para saque: {len(accounts) - payout_ready_count}")
        typer.echo()
        
        # Dicas
        if len(accounts) - payout_ready_count > 0:
            typer.echo("💡 Dica: Para aprovar uma conta, use:")
            typer.echo("   uv run python -m scripts.approve_account_for_payout --email ADMIN_EMAIL --no-dry-run")
            typer.echo()


@cli.command()
@typer_async
async def show(
    account_id: str = typer.Option(
        None, "--account-id", "-a", help="UUID da conta"
    ),
    email: str = typer.Option(
        None, "--email", "-e", help="Email do administrador"
    ),
) -> None:
    """Mostra detalhes de uma conta específica."""
    
    if not account_id and not email:
        typer.echo("❌ Erro: Forneça --account-id ou --email")
        raise typer.Exit(code=1)
    
    engine = create_async_engine("script")
    sessionmaker = create_async_sessionmaker(engine)
    
    async with sessionmaker() as session:
        account_repository = AccountRepository.from_session(session)
        
        if account_id:
            from uuid import UUID
            account = await account_repository.get_by_id(
                UUID(account_id),
                options=(
                    joinedload(Account.admin),
                    joinedload(Account.organizations),
                ),
            )
        else:
            from polar.user.repository import UserRepository
            user_repository = UserRepository.from_session(session)
            
            stmt = select(Account).options(
                joinedload(Account.admin),
                joinedload(Account.organizations),
            ).join(Account.admin).where(Account.admin.has(email=email))
            result = await session.execute(stmt)
            account = result.unique().scalars().one_or_none()
        
        if not account:
            typer.echo("❌ Conta não encontrada")
            raise typer.Exit(code=1)
        
        # Exibir detalhes
        typer.echo()
        typer.echo("=" * 80)
        typer.echo("📊 DETALHES DA CONTA")
        typer.echo("=" * 80)
        typer.echo()
        typer.echo(f"ID: {account.id}")
        typer.echo(f"Admin: {account.admin.email}")
        typer.echo(f"Tipo: {account.account_type}")
        typer.echo(f"Status: {account.status.value} ({account.status.get_display_name()})")
        typer.echo()
        typer.echo(f"País: {account.country}")
        typer.echo(f"Moeda: {account.currency}")
        typer.echo()
        typer.echo(f"Detalhes Enviados: {'✓' if account.is_details_submitted else '✗'}")
        typer.echo(f"Cobranças Habilitadas: {'✓' if account.is_charges_enabled else '✗'}")
        typer.echo(f"Saques Habilitados: {'✓' if account.is_payouts_enabled else '✗'}")
        typer.echo(f"Próxima Revisão: ${account.next_review_threshold / 100:.2f}" if account.next_review_threshold else "Próxima Revisão: Não definida")
        typer.echo()
        
        if account.stripe_id:
            typer.echo(f"Stripe ID: {account.stripe_id}")
            typer.echo()
        
        # Status de payout
        if account.is_payout_ready():
            typer.echo("✅ Status: PRONTA PARA SAQUES")
        elif account.is_under_review():
            typer.echo("⚠️  Status: EM REVISÃO - Saques bloqueados")
        elif account.status == Account.Status.DENIED:
            typer.echo("❌ Status: NEGADA")
        elif account.status == Account.Status.ACTIVE:
            typer.echo("⚡ Status: ATIVA mas não pronta para saques")
        else:
            typer.echo("⏳ Status: NÃO PRONTA")
        
        typer.echo()
        
        # Organizações
        if account.organizations:
            typer.echo(f"🏢 Organizações vinculadas ({len(account.organizations)}):")
            for org in account.organizations:
                typer.echo(f"   • {org.slug} (Status: {org.status})")
        else:
            typer.echo("🏢 Nenhuma organização vinculada")
        
        typer.echo()
        typer.echo("=" * 80)
        typer.echo()
        
        # Sugestões
        if not account.is_payout_ready():
            typer.echo("💡 Para aprovar esta conta:")
            typer.echo(f"   uv run python -m scripts.approve_account_for_payout --account-id {account.id} --no-dry-run")
            typer.echo()


if __name__ == "__main__":
    cli()

