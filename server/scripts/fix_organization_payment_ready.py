"""
Script para corrigir organização e deixá-la pronta para pagamentos.

Uso:
    uv run python -m scripts.fix_organization_payment_ready --slug ismc --no-dry-run
"""

import asyncio
import logging.config
from datetime import datetime, UTC
from functools import wraps
from typing import Any

import structlog
import typer
from sqlalchemy import select
from sqlalchemy.orm import joinedload

from polar.kit.db.postgres import create_async_sessionmaker
from polar.models import Account, Organization
from polar.organization.repository import OrganizationRepository
from polar.postgres import AsyncSession, create_async_engine

cli = typer.Typer()


def drop_all(*args: Any, **kwargs: Any) -> Any:
    raise structlog.DropEvent


structlog.configure(processors=[drop_all])
logging.config.dictConfig(
    {
        "version": 1,
        "disable_existing_loggers": True,
    }
)


def typer_async(f):  # type: ignore
    @wraps(f)
    def wrapper(*args, **kwargs):  # type: ignore
        return asyncio.run(f(*args, **kwargs))

    return wrapper


@cli.command()
@typer_async
async def fix(
    slug: str = typer.Argument(..., help="Slug da organização"),
    dry_run: bool = typer.Option(True, help="Se True, apenas mostra o que seria feito"),
) -> None:
    """Corrige organização para ficar pronta para pagamentos."""
    
    engine = create_async_engine("script")
    sessionmaker = create_async_sessionmaker(engine)
    
    async with sessionmaker() as session:
        # Buscar organização
        organization_repository = OrganizationRepository.from_session(session)
        stmt = (
            select(Organization)
            .where(Organization.slug == slug)
            .options(joinedload(Organization.account))
        )
        result = await session.execute(stmt)
        organization = result.unique().scalars().one_or_none()
        
        if not organization:
            typer.echo(f"❌ Organização '{slug}' não encontrada")
            raise typer.Exit(code=1)
        
        # Verificar status atual
        typer.echo()
        typer.echo("=" * 80)
        typer.echo("📊 STATUS ATUAL")
        typer.echo("=" * 80)
        typer.echo()
        typer.echo(f"🏢 Organização: {organization.name}")
        typer.echo(f"   Slug: {organization.slug}")
        typer.echo(f"   Status: {organization.status}")
        typer.echo(f"   Details Submitted At: {organization.details_submitted_at or 'Não definido'}")
        typer.echo(f"   Account ID: {organization.account_id or 'Nenhuma'}")
        typer.echo()
        
        if organization.account:
            account = organization.account
            typer.echo(f"💳 Conta:")
            typer.echo(f"   ID: {account.id}")
            typer.echo(f"   Status: {account.status.value}")
            typer.echo(f"   Stripe ID: {account.stripe_id or 'Não definido'}")
            typer.echo(f"   Details Submitted: {'✓' if account.is_details_submitted else '✗'}")
            typer.echo(f"   Payouts Enabled: {'✓' if account.is_payouts_enabled else '✗'}")
            typer.echo()
        
        # Listar correções necessárias
        changes = []
        
        if not organization.details_submitted_at:
            changes.append("✨ Definir details_submitted_at = agora")
        
        if organization.account:
            if not organization.account.stripe_id:
                changes.append("✨ Criar Stripe ID fake (para desenvolvimento)")
        
        if not changes:
            typer.echo("✅ Organização já está configurada corretamente!")
            return
        
        typer.echo("📝 CORREÇÕES A SEREM APLICADAS:")
        typer.echo()
        for change in changes:
            typer.echo(f"   {change}")
        typer.echo()
        
        if dry_run:
            typer.echo("=" * 80)
            typer.echo("🏃 DRY RUN MODE - Nenhuma mudança foi aplicada")
            typer.echo("   Use --no-dry-run para executar")
            typer.echo("=" * 80)
            return
        
        # APLICAR CORREÇÕES
        typer.echo("=" * 80)
        typer.echo("🚀 APLICANDO CORREÇÕES...")
        typer.echo("=" * 80)
        typer.echo()
        
        if not organization.details_submitted_at:
            organization.details_submitted_at = datetime.now(UTC)
            # Também definir details se não tiver
            if not organization.details:
                organization.details = {
                    "business": {
                        "name": organization.name,
                        "type": "individual"
                    }
                }
            typer.echo("   ✓ details_submitted_at definido")
        
        if organization.account and not organization.account.stripe_id:
            # Para desenvolvimento, criar um ID fake
            organization.account.stripe_id = f"acct_fake_{organization.account.id.hex[:16]}"
            typer.echo(f"   ✓ Stripe ID criado: {organization.account.stripe_id}")
        
        session.add(organization)
        if organization.account:
            session.add(organization.account)
        
        await session.commit()
        
        typer.echo()
        typer.echo("=" * 80)
        typer.echo("✅ CORREÇÕES APLICADAS COM SUCESSO!")
        typer.echo("=" * 80)
        typer.echo()
        
        # Status final
        await session.refresh(organization)
        
        typer.echo("📊 STATUS FINAL:")
        typer.echo()
        typer.echo(f"🏢 Organização: {organization.name}")
        typer.echo(f"   Details Submitted At: ✓ {organization.details_submitted_at}")
        typer.echo()
        
        if organization.account:
            await session.refresh(organization.account)
            typer.echo(f"💳 Conta:")
            typer.echo(f"   Stripe ID: ✓ {organization.account.stripe_id}")
            typer.echo(f"   Details Submitted: ✓")
            typer.echo(f"   Payouts Enabled: ✓")
            typer.echo()
        
        typer.echo("✅ PRONTA PARA PAGAMENTOS NO DASHBOARD!")
        typer.echo()


if __name__ == "__main__":
    cli()


