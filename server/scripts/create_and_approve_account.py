"""
Script para criar e aprovar conta de pagamento para uma organização.

Este script:
1. Cria uma conta de pagamento (Account) se não existir
2. <PERSON>cula a conta à organização
3. Aprova a organização
4. Habilita saques e cobranças

Uso:
    # Dry run (padrão)
    uv run python -m scripts.create_and_approve_account --slug ismc

    # Executar
    uv run python -m scripts.create_and_approve_account --slug ismc --no-dry-run
"""

import asyncio
import logging.config
from functools import wraps
from typing import Any
from uuid import UUID

import structlog
import typer
from sqlalchemy import select
from sqlalchemy.orm import joinedload

from polar.account.repository import AccountRepository
from polar.enums import AccountType
from polar.kit.db.postgres import create_async_sessionmaker
from polar.models import Account, Organization, User
from polar.organization.repository import OrganizationRepository
from polar.organization.service import organization as organization_service
from polar.postgres import AsyncSession, create_async_engine

cli = typer.Typer()


def drop_all(*args: Any, **kwargs: Any) -> Any:
    raise structlog.DropEvent


structlog.configure(processors=[drop_all])
logging.config.dictConfig(
    {
        "version": 1,
        "disable_existing_loggers": True,
    }
)


def typer_async(f):  # type: ignore
    @wraps(f)
    def wrapper(*args, **kwargs):  # type: ignore
        return asyncio.run(f(*args, **kwargs))

    return wrapper


async def get_organization_admin(session: AsyncSession, organization: Organization) -> User:
    """Busca o admin da organização."""
    # Buscar qualquer usuário admin do sistema
    stmt = select(User).where(User.is_admin == True).limit(1)
    result = await session.execute(stmt)
    admin = result.scalars().first()
    
    if not admin:
        # Se não houver admin, buscar primeiro usuário
        stmt = select(User).limit(1)
        result = await session.execute(stmt)
        admin = result.scalars().first()
    
    if not admin:
        raise ValueError("Nenhum usuário encontrado no sistema")
    
    return admin


@cli.command()
@typer_async
async def create_and_approve(
    slug: str = typer.Argument(..., help="Slug da organização"),
    country: str = typer.Option("BR", help="Código do país (ISO)"),
    currency: str = typer.Option("BRL", help="Moeda"),
    next_review_threshold: int = typer.Option(10000, help="Threshold em centavos"),
    dry_run: bool = typer.Option(
        True, help="Se True, apenas mostra o que seria feito"
    ),
) -> None:
    """Cria conta e aprova organização para saques."""
    
    engine = create_async_engine("script")
    sessionmaker = create_async_sessionmaker(engine)
    
    async with sessionmaker() as session:
        # Buscar organização
        organization_repository = OrganizationRepository.from_session(session)
        stmt = (
            select(Organization)
            .where(Organization.slug == slug)
            .options(joinedload(Organization.account))
        )
        result = await session.execute(stmt)
        organization = result.scalar_one_or_none()
        
        if not organization:
            typer.echo(f"❌ Organização '{slug}' não encontrada")
            raise typer.Exit(code=1)
        
        # Exibir status atual
        typer.echo()
        typer.echo("=" * 80)
        typer.echo("📊 SITUAÇÃO ATUAL")
        typer.echo("=" * 80)
        typer.echo()
        typer.echo(f"🏢 Organização: {organization.name}")
        typer.echo(f"   Slug: {organization.slug}")
        typer.echo(f"   Status: {organization.status}")
        typer.echo(f"   Account ID: {organization.account_id or 'Nenhuma'}")
        typer.echo()
        
        # Listar mudanças
        changes = []
        
        if not organization.account_id:
            changes.append("✨ Criar nova conta de pagamento")
            changes.append(f"   - País: {country}")
            changes.append(f"   - Moeda: {currency}")
            changes.append(f"   - Tipo: stripe")
        
        if organization.status != "active":
            changes.append(f"📝 Status: {organization.status} → active")
        
        changes.append("✅ Habilitar saques e cobranças")
        changes.append(f"💰 Threshold: {next_review_threshold} centavos (${next_review_threshold/100:.2f})")
        
        typer.echo("📝 MUDANÇAS A SEREM APLICADAS:")
        typer.echo()
        for change in changes:
            typer.echo(f"   {change}")
        typer.echo()
        
        if dry_run:
            typer.echo("=" * 80)
            typer.echo("🏃 DRY RUN MODE - Nenhuma mudança foi aplicada")
            typer.echo("   Use --no-dry-run para executar de verdade")
            typer.echo("=" * 80)
            return
        
        # EXECUTAR MUDANÇAS
        typer.echo("=" * 80)
        typer.echo("🚀 EXECUTANDO MUDANÇAS...")
        typer.echo("=" * 80)
        typer.echo()
        
        # 1. Criar conta se não existir
        if not organization.account_id:
            typer.echo("📝 Criando conta de pagamento...")
            
            # Buscar admin
            admin = await get_organization_admin(session, organization)
            typer.echo(f"   Admin: {admin.email}")
            
            # Criar conta
            account = Account(
                account_type=AccountType.stripe,
                admin_id=admin.id,
                country=country,
                currency=currency,
                status=Account.Status.ACTIVE,
                is_details_submitted=True,
                is_charges_enabled=True,
                is_payouts_enabled=True,
                processor_fees_applicable=True,
                next_review_threshold=next_review_threshold,
                data={},
            )
            
            session.add(account)
            await session.flush()
            
            typer.echo(f"   ✓ Conta criada: {account.id}")
            
            # Vincular à organização
            organization.account_id = account.id
            session.add(organization)
            await session.flush()
            
            typer.echo(f"   ✓ Conta vinculada à organização")
        else:
            typer.echo("ℹ️  Conta já existe, atualizando...")
            account_repository = AccountRepository.from_session(session)
            account = await account_repository.get_by_id(organization.account_id)
            
            if account:
                account.status = Account.Status.ACTIVE
                account.is_payouts_enabled = True
                account.is_charges_enabled = True
                account.is_details_submitted = True
                account.next_review_threshold = next_review_threshold
                session.add(account)
                typer.echo("   ✓ Conta atualizada")
        
        # 2. Aprovar organização
        typer.echo()
        typer.echo("📝 Aprovando organização...")
        
        try:
            await organization_service.confirm_organization_reviewed(
                session, organization, next_review_threshold
            )
            typer.echo("   ✓ Organização aprovada")
        except Exception as e:
            typer.echo(f"   ⚠️  Erro ao aprovar: {str(e)}")
            typer.echo("   ℹ️  Continuando com atualização manual...")
            
            # Atualização manual se o serviço falhar
            from polar.organization.service import OrganizationStatus
            from datetime import datetime, UTC
            
            organization.status = OrganizationStatus.ACTIVE
            organization.status_updated_at = datetime.now(UTC)
            organization.next_review_threshold = next_review_threshold
            session.add(organization)
            typer.echo("   ✓ Status atualizado manualmente")
        
        # Commit
        await session.commit()
        
        typer.echo()
        typer.echo("=" * 80)
        typer.echo("✅ CONCLUÍDO COM SUCESSO!")
        typer.echo("=" * 80)
        typer.echo()
        
        # Recarregar e mostrar status final
        await session.refresh(organization)
        
        if organization.account_id:
            account_repository = AccountRepository.from_session(session)
            account = await account_repository.get_by_id(organization.account_id)
            
            typer.echo("📊 STATUS FINAL:")
            typer.echo()
            typer.echo(f"🏢 Organização: {organization.name}")
            typer.echo(f"   Status: {organization.status}")
            typer.echo()
            
            if account:
                typer.echo(f"💳 Conta: {account.id}")
                typer.echo(f"   Status: {account.status.value}")
                typer.echo(f"   Saques: {'✓' if account.is_payouts_enabled else '✗'}")
                typer.echo(f"   Cobranças: {'✓' if account.is_charges_enabled else '✗'}")
                typer.echo()
                
                if account.is_payout_ready():
                    typer.echo("   ✅ PRONTA PARA SAQUES!")
                else:
                    typer.echo("   ⚠️  Ainda não pronta para saques")
        
        typer.echo()


if __name__ == "__main__":
    cli()

