"""
Script para adicionar <PERSON>e ID fake a uma conta (para desenvolvimento).

Uso:
    uv run python -m scripts.add_stripe_id_to_account --email <EMAIL> --no-dry-run
"""

import asyncio
import logging.config
from functools import wraps
from typing import Any
from uuid import UUID

import structlog
import typer
from sqlalchemy import select

from polar.account.repository import AccountRepository
from polar.kit.db.postgres import create_async_sessionmaker
from polar.models import Account, User
from polar.postgres import AsyncSession, create_async_engine
from polar.user.repository import UserRepository

cli = typer.Typer()


def drop_all(*args: Any, **kwargs: Any) -> Any:
    raise structlog.DropEvent


structlog.configure(processors=[drop_all])
logging.config.dictConfig(
    {
        "version": 1,
        "disable_existing_loggers": True,
    }
)


def typer_async(f):  # type: ignore
    @wraps(f)
    def wrapper(*args, **kwargs):  # type: ignore
        return asyncio.run(f(*args, **kwargs))

    return wrapper


@cli.command()
@typer_async
async def add_stripe_id(
    email: str = typer.Option(None, help="Email do usuário"),
    account_id: str = typer.Option(None, help="ID da conta"),
    dry_run: bool = typer.Option(True, help="Se True, apenas mostra o que seria feito"),
) -> None:
    """Adiciona Stripe ID fake a uma conta."""
    
    if not email and not account_id:
        typer.echo("❌ Forneça --email ou --account-id")
        raise typer.Exit(code=1)
    
    engine = create_async_engine("script")
    sessionmaker = create_async_sessionmaker(engine)
    
    async with sessionmaker() as session:
        account = None
        
        if email:
            # Buscar por email
            stmt = select(User).where(User.email == email)
            result = await session.execute(stmt)
            user = result.scalars().first()
            
            if not user or not user.account_id:
                typer.echo(f"❌ Usuário '{email}' não tem conta vinculada")
                raise typer.Exit(code=1)
            
            account_repository = AccountRepository.from_session(session)
            account = await account_repository.get_by_id(user.account_id)
        
        elif account_id:
            account_repository = AccountRepository.from_session(session)
            account = await account_repository.get_by_id(UUID(account_id))
        
        if not account:
            typer.echo("❌ Conta não encontrada")
            raise typer.Exit(code=1)
        
        # Status atual
        typer.echo()
        typer.echo("=" * 80)
        typer.echo("📊 STATUS ATUAL")
        typer.echo("=" * 80)
        typer.echo()
        typer.echo(f"💳 Conta: {account.id}")
        typer.echo(f"   Status: {account.status.value}")
        typer.echo(f"   Stripe ID: {account.stripe_id or 'Não definido'}")
        typer.echo(f"   Details Submitted: {'✓' if account.is_details_submitted else '✗'}")
        typer.echo(f"   Payouts Enabled: {'✓' if account.is_payouts_enabled else '✗'}")
        typer.echo()
        
        if account.stripe_id:
            typer.echo("✅ Conta já tem Stripe ID!")
            return
        
        typer.echo("📝 MUDANÇA A SER APLICADA:")
        typer.echo()
        stripe_id = f"acct_fake_{account.id.hex[:16]}"
        typer.echo(f"   ✨ Criar Stripe ID: {stripe_id}")
        typer.echo()
        
        if dry_run:
            typer.echo("=" * 80)
            typer.echo("🏃 DRY RUN MODE - Nenhuma mudança foi aplicada")
            typer.echo("   Use --no-dry-run para executar")
            typer.echo("=" * 80)
            return
        
        # EXECUTAR
        typer.echo("=" * 80)
        typer.echo("🚀 ADICIONANDO STRIPE ID...")
        typer.echo("=" * 80)
        typer.echo()
        
        account.stripe_id = stripe_id
        session.add(account)
        await session.commit()
        
        typer.echo(f"   ✓ Stripe ID adicionado: {stripe_id}")
        typer.echo()
        typer.echo("✅ CONCLUÍDO!")
        typer.echo()


if __name__ == "__main__":
    cli()


