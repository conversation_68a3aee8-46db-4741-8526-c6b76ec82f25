"""
Script para verificar status de organizações e suas contas.

Uso:
    uv run python -m scripts.check_organization_account --slug ismc
"""

import asyncio
import logging.config
from functools import wraps
from typing import Any

import structlog
import typer
from sqlalchemy import select
from sqlalchemy.orm import joinedload

from polar.kit.db.postgres import create_async_sessionmaker
from polar.models import Account, Organization, User
from polar.organization.repository import OrganizationRepository
from polar.postgres import AsyncSession, create_async_engine

cli = typer.Typer()


def drop_all(*args: Any, **kwargs: Any) -> Any:
    raise structlog.DropEvent


structlog.configure(processors=[drop_all])
logging.config.dictConfig(
    {
        "version": 1,
        "disable_existing_loggers": True,
    }
)


def typer_async(f):  # type: ignore
    @wraps(f)
    def wrapper(*args, **kwargs):  # type: ignore
        return asyncio.run(f(*args, **kwargs))

    return wrapper


@cli.command()
@typer_async
async def check(
    slug: str = typer.Argument(..., help="Slug da organização"),
) -> None:
    """Verifica status de uma organização e sua conta."""
    
    engine = create_async_engine("script")
    sessionmaker = create_async_sessionmaker(engine)
    
    async with sessionmaker() as session:
        organization_repository = OrganizationRepository.from_session(session)
        
        # Buscar organização
        stmt = (
            select(Organization)
            .where(Organization.slug == slug)
            .options(joinedload(Organization.account))
        )
        result = await session.execute(stmt)
        organization = result.scalar_one_or_none()
        
        if not organization:
            typer.echo(f"❌ Organização '{slug}' não encontrada")
            raise typer.Exit(code=1)
        
        # Exibir informações
        typer.echo()
        typer.echo("=" * 80)
        typer.echo("📊 INFORMAÇÕES DA ORGANIZAÇÃO")
        typer.echo("=" * 80)
        typer.echo()
        typer.echo(f"🏢 Organização: {organization.name}")
        typer.echo(f"   Slug: {organization.slug}")
        typer.echo(f"   ID: {organization.id}")
        typer.echo(f"   Status: {organization.status}")
        typer.echo(f"   Criada em: {organization.created_at}")
        typer.echo()
        
        # Verificar conta
        if organization.account_id:
            typer.echo("💳 CONTA VINCULADA:")
            typer.echo(f"   Account ID: {organization.account_id}")
            
            if organization.account:
                account = organization.account
                typer.echo(f"   Status: {account.status.value} ({account.status.get_display_name()})")
                typer.echo(f"   Tipo: {account.account_type}")
                typer.echo(f"   País: {account.country}")
                typer.echo(f"   Moeda: {account.currency}")
                typer.echo(f"   Saques Habilitados: {'✓' if account.is_payouts_enabled else '✗'}")
                typer.echo(f"   Cobranças Habilitadas: {'✓' if account.is_charges_enabled else '✗'}")
                typer.echo()
                
                if account.is_payout_ready():
                    typer.echo("   ✅ Status: PRONTA PARA SAQUES")
                else:
                    typer.echo("   ⚠️  Status: NÃO PRONTA PARA SAQUES")
            else:
                typer.echo("   ⚠️  Account ID existe mas conta não foi carregada")
        else:
            typer.echo("❌ NENHUMA CONTA VINCULADA")
            typer.echo()
            typer.echo("   A organização existe mas não tem uma conta de pagamento.")
            typer.echo()
            typer.echo("📝 PRÓXIMOS PASSOS:")
            typer.echo()
            typer.echo("   1. Complete o onboarding da organização")
            typer.echo("   2. Configure a conta de pagamento via interface")
            typer.echo("   3. Ou crie uma conta manualmente no banco")
            typer.echo()
            typer.echo("🔗 URL: https://fluu.digital/dashboard/ismc/finance/account")
        
        typer.echo()
        typer.echo("=" * 80)
        typer.echo()


if __name__ == "__main__":
    cli()


