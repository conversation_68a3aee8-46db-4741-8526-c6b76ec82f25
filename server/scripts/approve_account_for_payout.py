"""
<PERSON>ript para aprovar uma conta para saque (payout).

Este script permite aprovar uma conta para receber pagamentos (payouts), mudando:
1. Status da conta para ACTIVE
2. is_payouts_enabled para True
3. is_charges_enabled para True (se necessário)
4. is_details_submitted para True (se necessário)

A conta pode ser encontrada por:
- ID da conta (UUID)
- Email do administrador da conta
- Slug da organização vinculada à conta

Uso:
    # Modo dry-run (padrão - apenas mostra o que seria feito):
    uv run python -m scripts.approve_account_for_payout --account-id ACCOUNT_UUID
    uv run python -m scripts.approve_account_for_payout --email <EMAIL>
    uv run python -m scripts.approve_account_for_payout --organization-slug my-org

    # Executar aprovação real:
    uv run python -m scripts.approve_account_for_payout --account-id ACCOUNT_UUID --no-dry-run
    uv run python -m scripts.approve_account_for_payout --email <EMAIL> --no-dry-run
    uv run python -m scripts.approve_account_for_payout --organization-slug my-org --no-dry-run

    # Definir threshold de próxima revisão (padrão: 10000):
    uv run python -m scripts.approve_account_for_payout --account-id ACCOUNT_UUID --next-review-threshold 50000 --no-dry-run
"""

import asyncio
import logging.config
from functools import wraps
from typing import Any
from uuid import UUID

import structlog
import typer
from sqlalchemy import select
from sqlalchemy.orm import joinedload

from polar.account.repository import AccountRepository
from polar.kit.db.postgres import create_async_sessionmaker
from polar.models import Account, Organization, User
from polar.organization.repository import OrganizationRepository
from polar.organization.service import organization as organization_service
from polar.postgres import AsyncSession, create_async_engine
from polar.user.repository import UserRepository

cli = typer.Typer()


def drop_all(*args: Any, **kwargs: Any) -> Any:
    raise structlog.DropEvent


structlog.configure(processors=[drop_all])
logging.config.dictConfig(
    {
        "version": 1,
        "disable_existing_loggers": True,
    }
)


def typer_async(f):  # type: ignore
    @wraps(f)
    def wrapper(*args, **kwargs):  # type: ignore
        return asyncio.run(f(*args, **kwargs))

    return wrapper


def print_account_status(account: Account, admin: User) -> None:
    """Exibe o status atual de uma conta."""
    typer.echo(f"   ID: {account.id}")
    typer.echo(f"   Admin: {admin.email}")
    typer.echo(f"   Tipo: {account.account_type}")
    typer.echo(f"   Status: {account.status.value} ({account.status.get_display_name()})")
    typer.echo(f"   País: {account.country}")
    typer.echo(f"   Moeda: {account.currency}")
    typer.echo(f"   Detalhes Enviados: {'✓' if account.is_details_submitted else '✗'}")
    typer.echo(f"   Cobranças Habilitadas: {'✓' if account.is_charges_enabled else '✗'}")
    typer.echo(f"   Saques Habilitados: {'✓' if account.is_payouts_enabled else '✗'}")
    typer.echo(f"   Próxima Revisão: {account.next_review_threshold or 0}")
    
    if account.stripe_id:
        typer.echo(f"   Stripe ID: {account.stripe_id}")
    
    # Mostrar status de payout
    if account.is_payout_ready():
        typer.echo("   ✅ Pronta para Saques")
    elif account.is_under_review():
        typer.echo("   ⚠️  Em Revisão - Saques Bloqueados")
    elif account.is_active():
        typer.echo("   ⚠️  Ativa mas não pronta para saques")
    else:
        typer.echo("   ❌ Não Pronta para Saques")


async def find_account_by_id(
    session: AsyncSession, account_id: str
) -> tuple[Account, User] | None:
    """Encontra uma conta por ID."""
    try:
        account_uuid = UUID(account_id)
        account_repository = AccountRepository.from_session(session)
        account = await account_repository.get_by_id(
            account_uuid, options=(joinedload(Account.admin),)
        )
        if account:
            return account, account.admin
    except ValueError:
        pass
    return None


async def find_account_by_email(
    session: AsyncSession, email: str
) -> tuple[Account, User] | None:
    """Encontra uma conta pelo email do admin."""
    user_repository = UserRepository.from_session(session)
    
    # Buscar usuário pelo email
    stmt = select(User).where(User.email == email)
    result = await session.execute(stmt)
    user = result.scalars().first()
    
    if not user or not user.account_id:
        return None
    
    # Buscar a conta onde o usuário é admin
    account_repository = AccountRepository.from_session(session)
    stmt = select(Account).where(
        (Account.admin_id == user.id) | (Account.id == user.account_id)
    ).options(joinedload(Account.admin))
    result = await session.execute(stmt)
    account = result.unique().scalars().one_or_none()
    
    if account:
        return account, account.admin
    
    return None


async def find_account_by_organization_slug(
    session: AsyncSession, slug: str
) -> tuple[Account, User] | None:
    """Encontra uma conta pelo slug da organização."""
    organization_repository = OrganizationRepository.from_session(session)
    
    stmt = select(Organization).where(Organization.slug == slug).options(
        joinedload(Organization.account).joinedload(Account.admin)
    )
    result = await session.execute(stmt)
    organization = result.unique().scalar_one_or_none()
    
    if organization and organization.account:
        return organization.account, organization.account.admin
    
    return None


async def approve_account(
    session: AsyncSession,
    account: Account,
    next_review_threshold: int,
    dry_run: bool = True,
) -> None:
    """Aprova uma conta para saques."""
    
    changes_needed = []
    
    # Verificar o que precisa mudar
    if account.status != Account.Status.ACTIVE:
        changes_needed.append(f"Status: {account.status.value} → ACTIVE")
    
    if not account.is_payouts_enabled:
        changes_needed.append("Saques: Desabilitados → Habilitados")
    
    if not account.is_charges_enabled:
        changes_needed.append("Cobranças: Desabilitadas → Habilitadas")
    
    if not account.is_details_submitted:
        changes_needed.append("Detalhes: Não enviados → Enviados")
    
    if account.next_review_threshold != next_review_threshold:
        changes_needed.append(
            f"Threshold: {account.next_review_threshold or 0} → {next_review_threshold}"
        )
    
    if not changes_needed:
        typer.echo("\n✓ Conta já está aprovada para saques!")
        return
    
    typer.echo("\n📝 Mudanças a serem aplicadas:")
    for change in changes_needed:
        typer.echo(f"   • {change}")
    typer.echo()
    
    if dry_run:
        typer.echo("🏃 DRY RUN MODE - Nenhuma mudança foi aplicada")
        typer.echo("   Use --no-dry-run para aplicar as mudanças")
        return
    
    # Aplicar mudanças
    account.status = Account.Status.ACTIVE
    account.is_payouts_enabled = True
    account.is_charges_enabled = True
    account.is_details_submitted = True
    account.next_review_threshold = next_review_threshold
    
    session.add(account)
    
    # Também atualizar organizações vinculadas
    organization_repository = OrganizationRepository.from_session(session)
    organizations = await organization_repository.get_all_by_account(account.id)
    
    if organizations:
        typer.echo(f"📋 Atualizando {len(organizations)} organização(ões) vinculada(s):")
        for org in organizations:
            # Usar o serviço de organização para aprovar
            try:
                await organization_service.confirm_organization_reviewed(
                    session, org, next_review_threshold
                )
                typer.echo(f"   ✓ {org.slug} aprovada")
            except Exception as e:
                typer.echo(f"   ⚠️  {org.slug}: {str(e)}")
    
    await session.commit()
    
    typer.echo("\n✅ Conta aprovada com sucesso!")


@cli.command()
@typer_async
async def approve(
    account_id: str = typer.Option(
        None, "--account-id", "-a", help="UUID da conta"
    ),
    email: str = typer.Option(
        None, "--email", "-e", help="Email do administrador da conta"
    ),
    organization_slug: str = typer.Option(
        None, "--organization-slug", "-o", help="Slug da organização vinculada"
    ),
    next_review_threshold: int = typer.Option(
        10000,
        "--next-review-threshold",
        "-t",
        help="Threshold em centavos para próxima revisão",
    ),
    dry_run: bool = typer.Option(
        True, help="Se True, apenas mostra o que seria feito sem aplicar mudanças"
    ),
) -> None:
    """Aprova uma conta para receber saques (payouts)."""
    
    # Validar que ao menos um parâmetro foi fornecido
    if not any([account_id, email, organization_slug]):
        typer.echo("❌ Erro: Você deve fornecer pelo menos um dos seguintes:")
        typer.echo("   --account-id")
        typer.echo("   --email")
        typer.echo("   --organization-slug")
        raise typer.Exit(code=1)
    
    engine = create_async_engine("script")
    sessionmaker = create_async_sessionmaker(engine)
    
    async with sessionmaker() as session:
        account = None
        admin = None
        search_method = None
        
        # Tentar encontrar a conta
        if account_id:
            search_method = f"ID da conta: {account_id}"
            result = await find_account_by_id(session, account_id)
            if result:
                account, admin = result
        
        elif email:
            search_method = f"Email: {email}"
            result = await find_account_by_email(session, email)
            if result:
                account, admin = result
        
        elif organization_slug:
            search_method = f"Organização: {organization_slug}"
            result = await find_account_by_organization_slug(session, organization_slug)
            if result:
                account, admin = result
        
        if not account or not admin:
            typer.echo(f"❌ Conta não encontrada ({search_method})")
            raise typer.Exit(code=1)
        
        typer.echo(f"🔍 Conta encontrada ({search_method})")
        typer.echo()
        typer.echo("📊 Status Atual:")
        print_account_status(account, admin)
        
        # Aprovar conta
        await approve_account(session, account, next_review_threshold, dry_run)
        
        if not dry_run:
            # Recarregar conta para mostrar status atualizado
            await session.refresh(account)
            typer.echo()
            typer.echo("📊 Status Após Aprovação:")
            print_account_status(account, admin)


if __name__ == "__main__":
    cli()

