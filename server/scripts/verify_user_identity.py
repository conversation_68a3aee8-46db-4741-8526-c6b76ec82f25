"""
Script para verificar identidade de um usuário manualmente (bypass KYC).

Uso:
    uv run python -m scripts.verify_user_identity --email <EMAIL> --no-dry-run
"""

import asyncio
import logging.config
from functools import wraps
from typing import Any

import structlog
import typer
from sqlalchemy import select

from polar.kit.db.postgres import create_async_sessionmaker
from polar.models.user import IdentityVerificationStatus, User
from polar.postgres import AsyncSession, create_async_engine
from polar.user.repository import UserRepository

cli = typer.Typer()


def drop_all(*args: Any, **kwargs: Any) -> Any:
    raise structlog.DropEvent


structlog.configure(processors=[drop_all])
logging.config.dictConfig(
    {
        "version": 1,
        "disable_existing_loggers": True,
    }
)


def typer_async(f):  # type: ignore
    @wraps(f)
    def wrapper(*args, **kwargs):  # type: ignore
        return asyncio.run(f(*args, **kwargs))

    return wrapper


@cli.command()
@typer_async
async def verify(
    email: str = typer.Argument(..., help="Email do usuário"),
    dry_run: bool = typer.Option(True, help="Se True, apenas mostra o que seria feito"),
) -> None:
    """Marca identidade do usuário como verificada (bypass KYC para desenvolvimento)."""
    
    engine = create_async_engine("script")
    sessionmaker = create_async_sessionmaker(engine)
    
    async with sessionmaker() as session:
        # Buscar usuário
        user_repository = UserRepository.from_session(session)
        stmt = select(User).where(User.email == email)
        result = await session.execute(stmt)
        user = result.scalars().first()
        
        if not user:
            typer.echo(f"❌ Usuário '{email}' não encontrado")
            raise typer.Exit(code=1)
        
        # Status atual
        typer.echo()
        typer.echo("=" * 80)
        typer.echo("📊 STATUS ATUAL")
        typer.echo("=" * 80)
        typer.echo()
        typer.echo(f"👤 Usuário: {user.email}")
        typer.echo(f"   ID: {user.id}")
        typer.echo(f"   Admin: {'✓' if user.is_admin else '✗'}")
        typer.echo(f"   Email Verificado: {'✓' if user.email_verified else '✗'}")
        typer.echo()
        typer.echo(f"🔐 Verificação de Identidade:")
        typer.echo(f"   Status: {user.identity_verification_status.value}")
        typer.echo(f"   Status Display: {user.identity_verification_status.get_display_name()}")
        typer.echo(f"   Verificado: {'✓' if user.identity_verified else '✗'}")
        if user.identity_verification_id:
            typer.echo(f"   Stripe Verification ID: {user.identity_verification_id}")
        typer.echo()
        
        if user.identity_verified:
            typer.echo("✅ Usuário já está com identidade verificada!")
            return
        
        typer.echo("📝 MUDANÇA A SER APLICADA:")
        typer.echo()
        typer.echo(f"   ✨ Status: {user.identity_verification_status.value} → verified")
        typer.echo()
        
        if dry_run:
            typer.echo("=" * 80)
            typer.echo("🏃 DRY RUN MODE - Nenhuma mudança foi aplicada")
            typer.echo("   Use --no-dry-run para executar")
            typer.echo("=" * 80)
            typer.echo()
            typer.echo("⚠️  NOTA: Isso é para desenvolvimento/teste apenas!")
            typer.echo("   Em produção, você deve implementar KYC adequado para o Brasil.")
            return
        
        # EXECUTAR
        typer.echo("=" * 80)
        typer.echo("🚀 VERIFICANDO IDENTIDADE...")
        typer.echo("=" * 80)
        typer.echo()
        
        await user_repository.update(
            user,
            update_dict={
                "identity_verification_status": IdentityVerificationStatus.verified
            }
        )
        
        typer.echo(f"   ✓ Identidade marcada como verificada")
        typer.echo()
        typer.echo("=" * 80)
        typer.echo("✅ VERIFICAÇÃO CONCLUÍDA!")
        typer.echo("=" * 80)
        typer.echo()
        
        # Status final
        await session.refresh(user)
        
        typer.echo("📊 STATUS FINAL:")
        typer.echo()
        typer.echo(f"👤 Usuário: {user.email}")
        typer.echo(f"🔐 Verificação de Identidade: verified ✅")
        typer.echo(f"   Verificado: ✓")
        typer.echo()
        typer.echo("🎉 Agora você pode receber pagamentos!")
        typer.echo()
        typer.echo("⚠️  LEMBRE-SE:")
        typer.echo("   - Isso é um bypass para desenvolvimento")
        typer.echo("   - Em produção, implemente KYC adequado para o Brasil")
        typer.echo("   - Considere integração com serviços como Idwall, unico IDCheck, etc")
        typer.echo()


if __name__ == "__main__":
    cli()


