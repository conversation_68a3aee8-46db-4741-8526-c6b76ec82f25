"""
Script para mudar o admin de uma conta.

Uso:
    uv run python -m scripts.change_account_admin --organization-slug ismc --new-admin-email <EMAIL> --no-dry-run
"""

import asyncio
import logging.config
from functools import wraps
from typing import Any

import structlog
import typer
from sqlalchemy import select
from sqlalchemy.orm import joinedload

from polar.kit.db.postgres import create_async_sessionmaker
from polar.models import Account, Organization, User
from polar.organization.repository import OrganizationRepository
from polar.postgres import AsyncSession, create_async_engine
from polar.user.repository import UserRepository

cli = typer.Typer()


def drop_all(*args: Any, **kwargs: Any) -> Any:
    raise structlog.DropEvent


structlog.configure(processors=[drop_all])
logging.config.dictConfig(
    {
        "version": 1,
        "disable_existing_loggers": True,
    }
)


def typer_async(f):  # type: ignore
    @wraps(f)
    def wrapper(*args, **kwargs):  # type: ignore
        return asyncio.run(f(*args, **kwargs))

    return wrapper


@cli.command()
@typer_async
async def change_admin(
    organization_slug: str = typer.Argument(..., help="Slug da organização"),
    new_admin_email: str = typer.Argument(..., help="Email do novo admin"),
    dry_run: bool = typer.Option(True, help="Se True, apenas mostra o que seria feito"),
) -> None:
    """Muda o admin de uma conta de organização."""
    
    engine = create_async_engine("script")
    sessionmaker = create_async_sessionmaker(engine)
    
    async with sessionmaker() as session:
        # Buscar organização
        organization_repository = OrganizationRepository.from_session(session)
        stmt = (
            select(Organization)
            .where(Organization.slug == organization_slug)
            .options(joinedload(Organization.account).joinedload(Account.admin))
        )
        result = await session.execute(stmt)
        organization = result.unique().scalars().one_or_none()
        
        if not organization:
            typer.echo(f"❌ Organização '{organization_slug}' não encontrada")
            raise typer.Exit(code=1)
        
        if not organization.account:
            typer.echo(f"❌ Organização não tem conta vinculada")
            raise typer.Exit(code=1)
        
        # Buscar novo admin
        user_repository = UserRepository.from_session(session)
        stmt = select(User).where(User.email == new_admin_email)
        result = await session.execute(stmt)
        new_admin = result.scalars().first()
        
        if not new_admin:
            typer.echo(f"❌ Usuário '{new_admin_email}' não encontrado")
            raise typer.Exit(code=1)
        
        # Status atual
        account = organization.account
        old_admin = account.admin
        
        typer.echo()
        typer.echo("=" * 80)
        typer.echo("📊 STATUS ATUAL")
        typer.echo("=" * 80)
        typer.echo()
        typer.echo(f"🏢 Organização: {organization.name} ({organization.slug})")
        typer.echo(f"💳 Conta: {account.id}")
        typer.echo(f"   Status: {account.status.value}")
        typer.echo()
        typer.echo(f"👤 Admin Atual: {old_admin.email}")
        typer.echo(f"   ID: {old_admin.id}")
        typer.echo()
        typer.echo(f"👤 Novo Admin: {new_admin.email}")
        typer.echo(f"   ID: {new_admin.id}")
        typer.echo(f"   Admin: {'✓' if new_admin.is_admin else '✗'}")
        typer.echo()
        
        if old_admin.id == new_admin.id:
            typer.echo("✅ O usuário já é o admin desta conta!")
            return
        
        typer.echo("📝 MUDANÇA A SER APLICADA:")
        typer.echo()
        typer.echo(f"   ✨ Mudar admin de {old_admin.email} → {new_admin.email}")
        typer.echo()
        
        if dry_run:
            typer.echo("=" * 80)
            typer.echo("🏃 DRY RUN MODE - Nenhuma mudança foi aplicada")
            typer.echo("   Use --no-dry-run para executar")
            typer.echo("=" * 80)
            return
        
        # EXECUTAR
        typer.echo("=" * 80)
        typer.echo("🚀 MUDANDO ADMIN...")
        typer.echo("=" * 80)
        typer.echo()
        
        account.admin_id = new_admin.id
        session.add(account)
        await session.commit()
        
        typer.echo(f"   ✓ Admin alterado para {new_admin.email}")
        typer.echo()
        typer.echo("=" * 80)
        typer.echo("✅ MUDANÇA APLICADA COM SUCESSO!")
        typer.echo("=" * 80)
        typer.echo()
        typer.echo(f"💳 Conta: {account.id}")
        typer.echo(f"👤 Novo Admin: {new_admin.email}")
        typer.echo()
        typer.echo("🎉 Agora você tem acesso total à conta no dashboard!")
        typer.echo()


if __name__ == "__main__":
    cli()


