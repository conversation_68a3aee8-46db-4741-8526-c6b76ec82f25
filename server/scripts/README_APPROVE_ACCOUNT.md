# Script: Aprovar Conta para Saque

## 🚀 Uso Rápido

### 1. Ver o que seria feito (DRY RUN)
```bash
# Por email do admin
uv run python -m scripts.approve_account_for_payout --email <EMAIL>

# Por ID da conta
uv run python -m scripts.approve_account_for_payout --account-id "550e8400-e29b-41d4-a716-************"

# Por slug da organização
uv run python -m scripts.approve_account_for_payout --organization-slug "my-org"
```

### 2. Aprovar conta (EXECUTAR)
```bash
# Adicione --no-dry-run para aplicar as mudanças
uv run python -m scripts.approve_account_for_payout --email <EMAIL> --no-dry-run
```

### 3. Personalizar threshold
```bash
# Threshold de $500 (50000 centavos)
uv run python -m scripts.approve_account_for_payout \
  --email <EMAIL> \
  --next-review-threshold 50000 \
  --no-dry-run
```

## 📋 O Que o Script Faz

✅ Altera status para `ACTIVE`  
✅ Habilita saques (`is_payouts_enabled`)  
✅ Ha<PERSON>ita cobrança<PERSON> (`is_charges_enabled`)  
✅ Marca detalhes como enviados  
✅ Define threshold de revisão  
✅ Atualiza organizações vinculadas  

## 🔍 Opções

| Opção | Descrição | Exemplo |
|-------|-----------|---------|
| `--account-id` | UUID da conta | `--account-id "550e8400..."` |
| `--email` | Email do admin | `--email "<EMAIL>"` |
| `--organization-slug` | Slug da org | `--organization-slug "my-org"` |
| `--next-review-threshold` | Threshold ($) | `--next-review-threshold 50000` |
| `--no-dry-run` | Executar de verdade | `--no-dry-run` |

## ⚠️ IMPORTANTE

1. **Sempre rode em dry-run primeiro!**
2. Teste em desenvolvimento antes de produção
3. Verifique se a conta deve mesmo ser aprovada

## 📚 Documentação Completa

Ver: `SCRIPT_APROVAR_CONTA_SAQUE.md`

