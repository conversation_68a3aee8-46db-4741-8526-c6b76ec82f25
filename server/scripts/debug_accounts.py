"""Script rápido para debugar contas e organizações."""
import asyncio
from sqlalchemy import select
from sqlalchemy.orm import joinedload

from polar.kit.db.postgres import create_async_sessionmaker
from polar.models import Account, Organization, User
from polar.postgres import create_async_engine


async def main():
    engine = create_async_engine("script")
    sessionmaker = create_async_sessionmaker(engine)
    
    async with sessionmaker() as session:
        # Listar usuários
        print("\n=== USUÁRIOS ===")
        stmt = select(User).limit(10)
        result = await session.execute(stmt)
        users = result.scalars().all()
        print(f"Total: {len(users)}")
        for user in users:
            print(f"  - {user.email} (ID: {user.id})")
        
        # Listar organizações
        print("\n=== ORGANIZAÇÕES ===")
        stmt = select(Organization).options(joinedload(Organization.account)).limit(10)
        result = await session.execute(stmt)
        orgs = result.unique().scalars().all()
        print(f"Total: {len(orgs)}")
        for org in orgs:
            account_info = f"Account: {org.account_id}" if org.account_id else "Sem account"
            print(f"  - {org.slug} ({org.name}) - {account_info}")
        
        # Listar contas
        print("\n=== CONTAS ===")
        stmt = select(Account).options(joinedload(Account.admin)).limit(10)
        result = await session.execute(stmt)
        accounts = result.unique().scalars().all()
        print(f"Total: {len(accounts)}")
        for account in accounts:
            print(f"  - Admin: {account.admin.email}")
            print(f"    ID: {account.id}")
            print(f"    Status: {account.status}")
            print(f"    Payouts: {account.is_payouts_enabled}")
            print()


if __name__ == "__main__":
    asyncio.run(main())

