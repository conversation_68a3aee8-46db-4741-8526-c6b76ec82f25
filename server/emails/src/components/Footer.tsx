import { Column, Hr, <PERSON>, <PERSON>, <PERSON>, Text } from '@react-email/components'

const Footer = ({ email }: { email: string | null }) => (
  <>
    <Hr style={{ borderColor: '#e5e7eb', margin: '32px 0' }} />
    <Section style={{ maxWidth: '1312px', margin: '0 auto', padding: '0 16px' }}>
      {/* Logo and Brand Section */}
      <Section style={{ paddingBottom: '32px' }}>
        <Link
          href="https://fluu.app.br"
          style={{
            textDecoration: 'none',
            color: '#ffffff',
            fontSize: '24px',
            fontWeight: 'bold',
          }}
        >
          Fluu
        </Link>
        <Text style={{ marginTop: '16px', fontSize: '14px', color: '#9ca3af' }}>
          Onde criadores brasileiros fazem negócios.
        </Text>
      </Section>

      {/* Navigation Links */}
      <Row>
        <Column style={{ width: '25%', verticalAlign: 'top', paddingRight: '16px' }}>
          <Section>
            <Text
              style={{
                marginBottom: '12px',
                fontSize: '14px',
                fontWeight: '600',
                color: '#ffffff',
              }}
            >
              Fluu
            </Text>
            <Text style={{ marginBottom: '8px', fontSize: '14px', color: '#9ca3af' }}>
              <Link
                href="https://fluu.app.br/discover"
                style={{ color: '#9ca3af', textDecoration: 'none' }}
              >
                Marketplace
              </Link>
            </Text>
            <Text style={{ marginBottom: '8px', fontSize: '14px', color: '#9ca3af' }}>
              <Link
                href="https://fluu.app.br/company"
                style={{ color: '#9ca3af', textDecoration: 'none' }}
              >
                Sobre
              </Link>
            </Text>
            <Text style={{ marginBottom: '8px', fontSize: '14px', color: '#9ca3af' }}>
              <Link
                href="mailto:<EMAIL>"
                style={{ color: '#9ca3af', textDecoration: 'none' }}
              >
                Suporte
              </Link>
            </Text>
            <Text style={{ marginBottom: '8px', fontSize: '14px', color: '#9ca3af' }}>
              <Link
                href="https://fluu.app.br/blog"
                style={{ color: '#9ca3af', textDecoration: 'none' }}
              >
                Blog
              </Link>
            </Text>
          </Section>
        </Column>

        <Column style={{ width: '25%', verticalAlign: 'top', paddingRight: '16px' }}>
          <Section>
            <Text
              style={{
                marginBottom: '12px',
                fontSize: '14px',
                fontWeight: '600',
                color: '#ffffff',
              }}
            >
              Recursos
            </Text>
            <Text style={{ marginBottom: '8px', fontSize: '14px', color: '#9ca3af' }}>
              <Link
                href="https://fluu.app.br/docs"
                style={{ color: '#9ca3af', textDecoration: 'none' }}
              >
                Documentação
              </Link>
            </Text>
            <Text style={{ marginBottom: '8px', fontSize: '14px', color: '#9ca3af' }}>
              <Link
                href="https://fluu.app.br/blog"
                style={{ color: '#9ca3af', textDecoration: 'none' }}
              >
                Blog
              </Link>
            </Text>
            <Text style={{ marginBottom: '8px', fontSize: '14px', color: '#9ca3af' }}>
              <Link
                href="https://fluu.app.br/tutorials"
                style={{ color: '#9ca3af', textDecoration: 'none' }}
              >
                Tutoriais
              </Link>
            </Text>
            <Text style={{ marginBottom: '8px', fontSize: '14px', color: '#9ca3af' }}>
              <Link
                href="mailto:<EMAIL>"
                style={{ color: '#9ca3af', textDecoration: 'none' }}
              >
                Suporte
              </Link>
            </Text>
          </Section>
        </Column>

        <Column style={{ width: '25%', verticalAlign: 'top', paddingRight: '16px' }}>
          <Section>
            <Text
              style={{
                marginBottom: '12px',
                fontSize: '14px',
                fontWeight: '600',
                color: '#ffffff',
              }}
            >
              Desenvolvedores
            </Text>
            <Text style={{ marginBottom: '8px', fontSize: '14px', color: '#9ca3af' }}>
              <Link
                href="https://fluu.app.br/docs/apps"
                style={{ color: '#9ca3af', textDecoration: 'none' }}
              >
                Criar apps
              </Link>
            </Text>
            <Text style={{ marginBottom: '8px', fontSize: '14px', color: '#9ca3af' }}>
              <Link
                href="https://fluu.app.br/docs/payments"
                style={{ color: '#9ca3af', textDecoration: 'none' }}
              >
                Pagamentos
              </Link>
            </Text>
          </Section>
        </Column>

        <Column style={{ width: '25%', verticalAlign: 'top' }}>
          <Section>
            <Text
              style={{
                marginBottom: '12px',
                fontSize: '14px',
                fontWeight: '600',
                color: '#ffffff',
              }}
            >
              Legal
            </Text>
            <Text style={{ marginBottom: '8px', fontSize: '14px', color: '#9ca3af' }}>
              <Link
                href="https://fluu.app.br/legal/terms"
                style={{ color: '#9ca3af', textDecoration: 'none' }}
              >
                Termos de Serviço
              </Link>
            </Text>
            <Text style={{ marginBottom: '8px', fontSize: '14px', color: '#9ca3af' }}>
              <Link
                href="https://fluu.app.br/legal/privacy"
                style={{ color: '#9ca3af', textDecoration: 'none' }}
              >
                Política de Privacidade
              </Link>
            </Text>
          </Section>
        </Column>
      </Row>

      {/* Email Notice */}
      {email && (
        <Section
          style={{
            marginTop: '32px',
            paddingTop: '24px',
            borderTop: '1px solid #374151',
            textAlign: 'center',
          }}
        >
          <Text style={{ marginBottom: '8px', fontSize: '14px', color: '#9ca3af' }}>
            Este email foi enviado para{' '}
            <span style={{ fontWeight: '600', color: '#ffffff' }}>{email}</span>
          </Text>
          <Text style={{ fontSize: '14px', color: '#6b7280' }}>
            &copy; {new Date().getFullYear()} Fluu Digital. Todos os direitos reservados.
          </Text>
        </Section>
      )}
    </Section>
  </>
)

export default Footer
