from collections.abc import Sequence

from ratelimit import RateLimitMiddleware, Rule
from ratelimit.auths import EmptyInformation
from ratelimit.auths.ip import client_ip
from ratelimit.backends.redis import RedisBackend
from ratelimit.types import ASGIApp, Scope

from polar.auth.models import AuthSubject, Subject, is_anonymous
from polar.config import settings
from polar.enums import RateLimitGroup
from polar.redis import create_redis


async def _authenticate(scope: Scope) -> tuple[str, RateLimitGroup]:
    auth_subject: AuthSubject[Subject] = scope["state"]["auth_subject"]

    if is_anonymous(auth_subject):
        try:
            ip, _ = await client_ip(scope)
            # Disable rate limiting for localhost in development
            if settings.is_development() and ip in ("127.0.0.1", "::1", "localhost"):
                # Return a special key that won't be rate limited
                return f"dev-localhost-{ip}", RateLimitGroup.elevated
            return ip, RateLimitGroup.default
        except EmptyInformation:
            return auth_subject.rate_limit_key

    return auth_subject.rate_limit_key


# Development rules: very permissive for localhost
_DEV_RULES: dict[str, Sequence[Rule]] = {
    "^/v1/login-code": [
        # Very high limits for development - effectively no rate limiting
        Rule(minute=1000, hour=10000, block_time=0, zone="login-code")
    ],
    "^/v1/customer-portal/customer-session": [
        Rule(minute=1000, hour=10000, block_time=0, zone="customer-session")
    ],
    "^/v1/customer-portal/license-keys/(validate|activate|deactivate)": [
        Rule(second=1000, block_time=0, zone="customer-license-key")
    ],
    "^/v1/customer-seats/claim/.+/stream": [
        Rule(minute=1000, block_time=0, zone="seat-claim-stream")
    ],
    "^/v1/integrations/(google|github|discord)/authorize": [
        Rule(minute=1000, hour=10000, block_time=0, zone="oauth-authorize"),
    ],
    "^/v1/integrations/(google|github|discord)/callback": [
        Rule(minute=1000, hour=10000, block_time=0, zone="oauth-callback"),
    ],
    "^/v1": [
        Rule(group=RateLimitGroup.default, minute=10000, zone="api"),
        Rule(group=RateLimitGroup.web, second=10000, zone="api"),
        Rule(group=RateLimitGroup.elevated, second=10000, zone="api"),
    ],
}

# Production rules: normal rate limiting
_PROD_RULES: dict[str, Sequence[Rule]] = {
    "^/v1/login-code": [Rule(minute=6, hour=12, block_time=900, zone="login-code")],
    "^/v1/customer-portal/customer-session": [
        Rule(minute=6, hour=12, block_time=900, zone="customer-session")
    ],
    "^/v1/customer-portal/license-keys/(validate|activate|deactivate)": [
        Rule(second=3, block_time=60, zone="customer-license-key")
    ],
    "^/v1/customer-seats/claim/.+/stream": [
        Rule(minute=10, block_time=300, zone="seat-claim-stream")
    ],
    # OAuth endpoints need higher limits to avoid blocking legitimate login attempts
    "^/v1/integrations/(google|github|discord)/authorize": [
        Rule(minute=30, hour=200, block_time=60, zone="oauth-authorize"),
    ],
    "^/v1/integrations/(google|github|discord)/callback": [
        Rule(minute=30, hour=200, block_time=60, zone="oauth-callback"),
    ],
    "^/v1": [
        Rule(group=RateLimitGroup.default, minute=500, zone="api"),
        Rule(group=RateLimitGroup.web, second=100, zone="api"),
        Rule(group=RateLimitGroup.elevated, second=100, zone="api"),
    ],
}

# Use development rules if in development mode
_RULES = _DEV_RULES if settings.is_development() else _PROD_RULES


def get_middleware(app: ASGIApp) -> RateLimitMiddleware:
    return RateLimitMiddleware(
        app, _authenticate, RedisBackend(create_redis("rate-limit")), _RULES
    )


__all__ = ["get_middleware"]
