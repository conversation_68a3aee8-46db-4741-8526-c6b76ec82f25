"""Implementação do provider Pagar.me usando API REST v2 diretamente."""

import base64
import structlog
from typing import Any, Optional

import httpx

from polar.config import settings
from polar.enums import PaymentProcessor
from polar.models.payment import PaymentStatus

from ..base import PaymentProvider, PaymentProviderError

log = structlog.get_logger()


class PagarmeProvider(PaymentProvider):
    """Implementação do provider Pagar.me usando API REST v2 diretamente."""

    def __init__(self):
        """Inicializa o provider Pagar.me."""
        # Determinar base URL baseado no ambiente
        if settings.ENV.value == "production":
            self.base_url = "https://api.pagar.me/core/v5"
        else:
            self.base_url = "https://api.pagar.me/core/v5"  # Sandbox usa mesma URL

        # Configurar cliente HTTP
        # Pagar.me v2 usa Basic Auth com api_key como username
        api_key = settings.PAGARME_SECRET_KEY
        auth_header = base64.b64encode(f"{api_key}:".encode()).decode()

        self.client = httpx.AsyncClient(
            base_url=self.base_url,
            headers={
                "Authorization": f"Basic {auth_header}",
                "Content-Type": "application/json",
            },
            timeout=30.0,
        )

    @property
    def processor(self) -> PaymentProcessor:
        """Retorna o PaymentProcessor deste provider."""
        return PaymentProcessor.pagarme

    async def create_customer(
        self,
        email: str,
        name: Optional[str] = None,
        metadata: Optional[dict[str, str]] = None,
    ) -> str:
        """Cria customer no Pagar.me usando API REST."""
        try:
            payload: dict[str, Any] = {
                "email": email,
                "metadata": metadata or {},
            }

            if name:
                name_parts = name.split(" ", 1)
                payload["name"] = name_parts[0]
                if len(name_parts) > 1:
                    payload["last_name"] = name_parts[1]

            response = await self.client.post("/customers", json=payload)
            response.raise_for_status()
            customer_data = response.json()

            log.info(
                "Pagar.me customer created",
                customer_id=customer_data["id"],
                email=email,
            )
            return customer_data["id"]

        except httpx.HTTPStatusError as e:
            error_msg = e.response.text if e.response else str(e)
            log.error(
                "Failed to create Pagar.me customer",
                email=email,
                error=error_msg,
                status_code=e.response.status_code if e.response else None,
            )
            raise PaymentProviderError(
                f"Failed to create Pagar.me customer: {error_msg}"
            ) from e
        except Exception as e:
            log.error(
                "Unexpected error creating Pagar.me customer",
                email=email,
                error=str(e),
            )
            raise PaymentProviderError(
                f"Unexpected error creating Pagar.me customer: {str(e)}"
            ) from e

    async def create_payment_intent(
        self,
        amount: int,
        currency: str,
        customer_id: str,
        payment_method_id: Optional[str] = None,
        metadata: Optional[dict[str, str]] = None,
        description: Optional[str] = None,
        confirm: bool = False,
        off_session: bool = False,
        payment_method: str = "credit_card",
    ) -> dict[str, Any]:
        """
        Cria uma order no Pagar.me (equivalente a PaymentIntent).

        Pagar.me usa o conceito de "orders" para pagamentos.
        Suporta: credit_card, pix, boleto.
        """
        try:
            # Criar item da order
            items = [
                {
                    "amount": amount,
                    "description": description or "Payment",
                    "quantity": 1,
                }
            ]

            # Criar payment request baseado no método
            payment_data: dict[str, Any] = {
                "payment_method": payment_method,
            }

            if payment_method == "credit_card":
                payment_data["credit_card"] = {
                    "recurrence": False,
                    "installments": 1,
                    "capture": confirm,
                }
                if payment_method_id:
                    payment_data["credit_card"]["card_id"] = payment_method_id

            elif payment_method == "pix":
                payment_data["pix"] = {
                    "expires_in": 3600,  # 1 hora (PIX padrão)
                }

            elif payment_method == "boleto":
                payment_data["boleto"] = {
                    "bank": "033",  # Banco Santander (padrão)
                    "instructions": "Pagar até o vencimento",
                    "due_at": None,  # API calcula automaticamente
                }

            # Criar order request
            payload = {
                "customer_id": customer_id,
                "items": items,
                "payments": [payment_data],
                "metadata": metadata or {},
            }

            response = await self.client.post("/orders", json=payload)
            response.raise_for_status()
            order_data = response.json()

            # Pagar.me retorna charges dentro de payments
            charge = None
            if order_data.get("charges") and len(order_data["charges"]) > 0:
                charge = order_data["charges"][0]

            log.info(
                "Pagar.me order created",
                order_id=order_data["id"],
                charge_id=charge["id"] if charge else None,
                amount=amount,
                payment_method=payment_method,
            )

            # Montar resposta com dados específicos do método
            result = {
                "id": order_data["id"],
                "client_secret": charge["id"] if charge else order_data["id"],
                "status": self._map_status(charge["status"] if charge else "pending"),
                "amount": amount,
                "currency": currency.upper(),
                "payment_method": payment_method,
            }

            # Adicionar dados específicos do PIX
            if payment_method == "pix" and charge:
                last_tx = charge.get("last_transaction", {})
                if last_tx.get("qr_code"):
                    result["pix"] = {
                        "qr_code": last_tx["qr_code"],
                        "qr_code_url": last_tx.get("qr_code_url"),
                        "expires_at": last_tx.get("expires_at"),
                    }
                    log.info(
                        "PIX QR Code generated",
                        charge_id=charge["id"],
                        expires_at=last_tx.get("expires_at"),
                    )

            # Adicionar dados específicos do Boleto
            elif payment_method == "boleto" and charge:
                last_tx = charge.get("last_transaction", {})
                if last_tx.get("url"):
                    result["boleto"] = {
                        "url": last_tx["url"],
                        "barcode": last_tx.get("barcode"),
                        "due_at": last_tx.get("due_at"),
                    }

            return result

        except httpx.HTTPStatusError as e:
            error_msg = e.response.text if e.response else str(e)
            log.error(
                "Failed to create Pagar.me payment",
                customer_id=customer_id,
                amount=amount,
                payment_method=payment_method,
                error=error_msg,
                status_code=e.response.status_code if e.response else None,
            )
            raise PaymentProviderError(
                f"Failed to create Pagar.me payment: {error_msg}"
            ) from e
        except Exception as e:
            log.error(
                "Unexpected error creating Pagar.me payment",
                customer_id=customer_id,
                amount=amount,
                payment_method=payment_method,
                error=str(e),
            )
            raise PaymentProviderError(
                f"Unexpected error creating Pagar.me payment: {str(e)}"
            ) from e

    async def create_pix_payment(
        self,
        amount: int,
        customer_id: str,
        description: str,
        metadata: Optional[dict[str, str]] = None,
    ) -> dict[str, Any]:
        """
        Cria pagamento PIX no Pagar.me (atalho para create_payment_intent com PIX).
        
        Retorna QR Code e dados do PIX.
        """
        result = await self.create_payment_intent(
            amount=amount,
            currency="BRL",
            customer_id=customer_id,
            description=description,
            metadata=metadata,
            payment_method="pix",
        )
        
        if "pix" not in result:
            raise PaymentProviderError("Failed to generate PIX QR Code")
        
        return result

    async def create_setup_intent(
        self,
        customer_id: str,
        metadata: Optional[dict[str, str]] = None,
    ) -> dict[str, Any]:
        """
        Cria um card token no Pagar.me para salvar payment method.

        Pagar.me não tem setup intent, usa card tokens.
        Isso geralmente é feito no frontend.
        """
        log.info(
            "Pagar.me setup intent requested",
            customer_id=customer_id,
            note="Pagar.me uses card tokens created on frontend",
        )
        return {
            "id": f"token_{customer_id}",
            "client_secret": None,
            "status": "requires_payment_method",
        }

    async def create_customer_session(
        self,
        customer_id: str,
    ) -> dict[str, Any]:
        """
        Cria uma sessão de customer para checkout.

        Pagar.me não tem customer sessions como Stripe.
        Retorna dados básicos do customer.
        """
        try:
            response = await self.client.get(f"/customers/{customer_id}")
            response.raise_for_status()
            customer_data = response.json()

            return {
                "customer_id": customer_data["id"],
                "email": customer_data.get("email"),
            }
        except httpx.HTTPStatusError as e:
            error_msg = e.response.text if e.response else str(e)
            log.error(
                "Failed to get Pagar.me customer",
                customer_id=customer_id,
                error=error_msg,
            )
            raise PaymentProviderError(
                f"Failed to get Pagar.me customer: {error_msg}"
            ) from e

    async def retrieve_payment(
        self,
        payment_id: str,
    ) -> dict[str, Any]:
        """Recupera informações de um pagamento (charge)."""
        try:
            response = await self.client.get(f"/charges/{payment_id}")
            response.raise_for_status()
            charge_data = response.json()

            return {
                "id": charge_data["id"],
                "status": charge_data.get("status"),
                "amount": charge_data.get("amount", 0),
                "currency": charge_data.get("currency", "BRL"),
                "payment_method": charge_data.get("payment_method", "credit_card"),
            }
        except httpx.HTTPStatusError as e:
            error_msg = e.response.text if e.response else str(e)
            log.error(
                "Failed to retrieve Pagar.me payment",
                payment_id=payment_id,
                error=error_msg,
            )
            raise PaymentProviderError(
                f"Failed to retrieve Pagar.me payment: {error_msg}"
            ) from e

    async def create_refund(
        self,
        payment_id: str,
        amount: Optional[int] = None,
        reason: Optional[str] = None,
    ) -> dict[str, Any]:
        """Cria um reembolso."""
        try:
            payload: dict[str, Any] = {}
            if amount:
                payload["amount"] = amount

            response = await self.client.delete(
                f"/charges/{payment_id}", json=payload
            )
            response.raise_for_status()
            refund_data = response.json()

            log.info(
                "Pagar.me refund created",
                payment_id=payment_id,
                refund_id=refund_data.get("id"),
                amount=amount,
            )
            return {
                "id": refund_data.get("id", payment_id),
                "status": refund_data.get("status", "canceled"),
                "amount": amount or refund_data.get("amount", 0),
            }
        except httpx.HTTPStatusError as e:
            error_msg = e.response.text if e.response else str(e)
            log.error(
                "Failed to create Pagar.me refund",
                payment_id=payment_id,
                error=error_msg,
            )
            raise PaymentProviderError(
                f"Failed to create Pagar.me refund: {error_msg}"
            ) from e

    async def handle_webhook(
        self,
        event_type: str,
        event_data: dict[str, Any],
    ) -> None:
        """Processa um webhook do provider."""
        log.info(
            "Pagar.me webhook received",
            event_type=event_type,
            event_id=event_data.get("id"),
        )

    def _map_status(self, pagarme_status: str) -> str:
        """Mapeia status do Pagar.me para status padrão."""
        status_map = {
            "paid": "succeeded",
            "pending": "pending",
            "failed": "failed",
            "canceled": "failed",
            "processing": "pending",
        }
        return status_map.get(pagarme_status.lower(), "pending")

    def parse_payment_from_webhook(
        self,
        event_data: dict[str, Any],
    ) -> dict[str, Any]:
        """
        Converte webhook do Pagar.me para formato padrão.

        Pagar.me envia webhooks no formato:
        {
          "type": "charge.paid",
          "data": {
            "id": "...",
            "status": "paid",
            ...
          }
        }
        """
        # Extrair dados do charge
        charge = event_data.get("data", event_data)

        payment_method = charge.get("payment_method", "credit_card")
        last_transaction = charge.get("last_transaction", {})

        return {
            "processor_id": charge["id"],
            "status": PaymentStatus(self._map_status(charge.get("status", "pending"))),
            "amount": charge.get("amount", 0),
            "currency": charge.get("currency", "BRL").lower(),
            "method": payment_method,
            "method_metadata": {
                "card": last_transaction.get("card", {}),
            },
            "customer_email": (
                charge.get("customer", {}).get("email")
                if isinstance(charge.get("customer"), dict)
                else None
            ),
            "decline_reason": (
                last_transaction.get("gateway_response", {}).get("code")
                if isinstance(last_transaction.get("gateway_response"), dict)
                else None
            ),
            "decline_message": (
                last_transaction.get("gateway_response", {}).get("message")
                if isinstance(last_transaction.get("gateway_response"), dict)
                else None
            ),
            "risk_level": None,
            "risk_score": None,
        }

    def parse_payment_method_from_intent(
        self,
        intent_data: dict[str, Any],
    ) -> dict[str, Any]:
        """
        Extrai informações de payment method do intent/order.

        Em Pagar.me, payment methods vêm dentro de charges.
        """
        charge = intent_data.get("charge", intent_data)
        last_transaction = charge.get("last_transaction", {})
        card = last_transaction.get("card", {})

        return {
            "processor_id": card.get("id") or last_transaction.get("id"),
            "type": charge.get("payment_method", "credit_card"),
            "method_metadata": {
                "card": card,
                "last_four_digits": card.get("last_four_digits"),
                "brand": card.get("brand"),
            },
        }
