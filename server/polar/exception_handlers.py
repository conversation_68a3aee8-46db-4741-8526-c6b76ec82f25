import logging
from urllib.parse import urlencode

from fastapi import FastAP<PERSON>, Request
from fastapi.encoders import jsonable_encoder
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse, RedirectResponse, Response

from polar.config import settings
from polar.exceptions import (
    PolarError,
    PolarRedirectionError,
    PolarRequestValidationError,
    ResourceNotModified,
)

logger = logging.getLogger(__name__)


async def polar_exception_handler(request: Request, exc: PolarError) -> JSONResponse:
    return JSONResponse(
        status_code=exc.status_code,
        content={"error": type(exc).__name__, "detail": exc.message},
        headers=exc.headers,
    )


async def request_validation_exception_handler(
    request: Request, exc: RequestValidationError | PolarRequestValidationError
) -> JSONResponse:
    return JSONResponse(
        status_code=422,
        content={"error": type(exc).__name__, "detail": jsonable_encoder(exc.errors())},
    )


async def polar_redirection_exception_handler(
    request: Request, exc: PolarRedirectionError
) -> RedirectResponse:
    error_url_params = urlencode(
        {
            "message": exc.message,
            "return_to": exc.return_to or settings.FRONTEND_DEFAULT_RETURN_PATH,
        }
    )
    error_url = f"{settings.generate_frontend_url('/error')}?{error_url_params}"
    return RedirectResponse(error_url, 303)


async def polar_not_modified_handler(
    request: Request, exc: ResourceNotModified
) -> Response:
    return Response(status_code=exc.status_code)


async def general_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """Handle all unhandled exceptions and add CORS headers."""
    logger.exception("Unhandled exception", exc_info=exc)
    
    # Get origin from request
    origin = request.headers.get("origin")
    
    # Check if origin is allowed (same logic as CORS middleware)
    allowed_origins = list(settings.CORS_ORIGINS) if settings.CORS_ORIGINS else []
    if settings.FRONTEND_BASE_URL and settings.FRONTEND_BASE_URL not in allowed_origins:
        allowed_origins.append(settings.FRONTEND_BASE_URL)
    if "http://127.0.0.1:3000" in allowed_origins and "http://localhost:3000" not in allowed_origins:
        allowed_origins.append("http://localhost:3000")
    elif "http://localhost:3000" in allowed_origins and "http://127.0.0.1:3000" not in allowed_origins:
        allowed_origins.append("http://127.0.0.1:3000")
    
    # Build CORS headers
    headers = {}
    if origin and origin in allowed_origins:
        headers["Access-Control-Allow-Origin"] = origin
        headers["Access-Control-Allow-Credentials"] = "true"
        headers["Access-Control-Allow-Methods"] = "DELETE, GET, HEAD, OPTIONS, PATCH, POST, PUT"
        headers["Access-Control-Allow-Headers"] = "*"
    
    return JSONResponse(
        status_code=500,
        content={
            "error": "InternalServerError",
            "detail": "An error occurred while requesting the login code. Please try again.",
        },
        headers=headers,
    )


def add_exception_handlers(app: FastAPI) -> None:
    app.add_exception_handler(
        PolarRedirectionError,
        polar_redirection_exception_handler,  # type: ignore
    )
    app.add_exception_handler(
        ResourceNotModified,
        polar_not_modified_handler,  # type: ignore
    )

    app.add_exception_handler(
        RequestValidationError,
        request_validation_exception_handler,  # type: ignore
    )
    app.add_exception_handler(
        PolarRequestValidationError,
        request_validation_exception_handler,  # type: ignore
    )
    app.add_exception_handler(PolarError, polar_exception_handler)  # type: ignore
    # Add general exception handler for unhandled exceptions (must be last)
    app.add_exception_handler(Exception, general_exception_handler)  # type: ignore
