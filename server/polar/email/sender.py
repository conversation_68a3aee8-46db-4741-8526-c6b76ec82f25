from abc import ABC, abstractmethod
from collections.abc import Iterable
from typing import Any, TypedDict

import httpx
import structlog
from email_validator import validate_email

from polar.config import Email<PERSON>ender as EmailSenderType
from polar.config import settings
from polar.exceptions import PolarError
from polar.logging import Logger
from polar.worker import enqueue_job

log: Logger = structlog.get_logger()

def _get_default_from_name() -> str:
    return settings.EMAIL_FROM_NAME

def _get_default_from_email_address() -> str:
    return f"{settings.EMAIL_FROM_LOCAL}@{settings.EMAIL_FROM_DOMAIN}"

def _get_default_reply_to_name() -> str:
    return "Fluu Support"

def _get_default_reply_to_email_address() -> str:
    return f"hello@{settings.EMAIL_FROM_DOMAIN}"

# Removido: constantes calculadas no início do módulo podem usar valores antigos
# Sempre usar as funções _get_default_* para garantir valores atualizados


def to_ascii_email(email: str) -> str:
    """
    Convert an email address to ASCII format, possibly using punycode for internationalized domains.
    """
    validated_email = validate_email(email, check_deliverability=False)
    return validated_email.ascii_email or email


class EmailSenderError(PolarError): ...


class SendEmailError(EmailSenderError):
    def __init__(self, message: str) -> None:
        super().__init__(message)


class Attachment(TypedDict):
    remote_url: str
    filename: str


class EmailSender(ABC):
    @abstractmethod
    async def send(
        self,
        *,
        to_email_addr: str,
        subject: str,
        html_content: str,
        from_name: str | None = None,
        from_email_addr: str | None = None,
        email_headers: dict[str, str] | None = None,
        reply_to_name: str | None = None,
        reply_to_email_addr: str | None = None,
        attachments: Iterable[Attachment] | None = None,
    ) -> None:
        pass


class LoggingEmailSender(EmailSender):
    async def send(
        self,
        *,
        to_email_addr: str,
        subject: str,
        html_content: str,
        from_name: str | None = None,
        from_email_addr: str | None = None,
        email_headers: dict[str, str] | None = None,
        reply_to_name: str | None = None,
        reply_to_email_addr: str | None = None,
        attachments: Iterable[Attachment] | None = None,
    ) -> None:
        # Usar valores padrão calculados dinamicamente se não fornecidos
        if from_name is None:
            from_name = _get_default_from_name()
        if from_email_addr is None:
            from_email_addr = _get_default_from_email_address()
        if reply_to_name is None:
            reply_to_name = _get_default_reply_to_name()
        if reply_to_email_addr is None:
            reply_to_email_addr = _get_default_reply_to_email_address()
        
        log.info(
            "Sending an email",
            to_email_addr=to_ascii_email(to_email_addr),
            subject=subject,
            from_name=from_name,
            from_email_addr=to_ascii_email(from_email_addr),
        )


class ResendEmailSender(EmailSender):
    def __init__(self) -> None:
        self.client = httpx.AsyncClient(
            base_url=settings.RESEND_API_BASE_URL,
            headers={"Authorization": f"Bearer {settings.RESEND_API_KEY}"},
        )

    async def send(
        self,
        *,
        to_email_addr: str,
        subject: str,
        html_content: str,
        from_name: str | None = None,
        from_email_addr: str | None = None,
        email_headers: dict[str, str] | None = None,
        reply_to_name: str | None = None,
        reply_to_email_addr: str | None = None,
        attachments: Iterable[Attachment] | None = None,
    ) -> None:
        # Usar valores padrão calculados dinamicamente se não fornecidos
        if from_name is None:
            from_name = _get_default_from_name()
        if from_email_addr is None:
            from_email_addr = _get_default_from_email_address()
        if reply_to_name is None:
            reply_to_name = _get_default_reply_to_name()
        if reply_to_email_addr is None:
            reply_to_email_addr = _get_default_reply_to_email_address()
        
        to_email_addr_ascii = to_ascii_email(to_email_addr)
        payload: dict[str, Any] = {
            "from": f"{from_name} <{to_ascii_email(from_email_addr)}>",
            "to": [to_email_addr_ascii],
            "subject": subject,
            "html": html_content,
            "headers": email_headers or {},
            "attachments": [
                {
                    "path": attachment["remote_url"],
                    "filename": attachment["filename"],
                }
                for attachment in attachments
            ]
            if attachments
            else [],
        }
        if reply_to_name and reply_to_email_addr:
            payload["reply_to"] = (
                f"{reply_to_name} <{to_ascii_email(reply_to_email_addr)}>"
            )

        try:
            response = await self.client.post("/emails", json=payload)
            response.raise_for_status()
            email = response.json()
        except httpx.HTTPStatusError as e:
            error_detail = None
            try:
                error_detail = e.response.json()
            except Exception:
                error_detail = e.response.text
            log.error(
                "resend.send_error",
                to_email_addr=to_email_addr_ascii,
                subject=subject,
                status_code=e.response.status_code,
                error_detail=error_detail,
                from_email=from_email_addr,
                payload_from=payload.get("from"),
            )
            raise SendEmailError(f"Resend API error {e.response.status_code}: {error_detail}") from e
        except httpx.HTTPError as e:
            log.error(
                "resend.send_error",
                to_email_addr=to_email_addr_ascii,
                subject=subject,
                error=str(e),
                from_email=from_email_addr,
                payload_from=payload.get("from"),
            )
            raise SendEmailError(str(e)) from e

        log.info(
            "resend.send",
            to_email_addr=to_email_addr_ascii,
            subject=subject,
            email_id=email["id"],
        )


class EmailFromReply(TypedDict):
    from_name: str
    from_email_addr: str
    reply_to_name: str
    reply_to_email_addr: str


def enqueue_email(
    to_email_addr: str,
    subject: str,
    html_content: str,
    from_name: str | None = None,
    from_email_addr: str | None = None,
    email_headers: dict[str, str] | None = None,
    reply_to_name: str | None = None,
    reply_to_email_addr: str | None = None,
    attachments: Iterable[Attachment] | None = None,
) -> None:
    # Usar valores padrão calculados dinamicamente se não fornecidos
    if from_name is None:
        from_name = _get_default_from_name()
    if from_email_addr is None:
        from_email_addr = _get_default_from_email_address()
    if reply_to_name is None:
        reply_to_name = _get_default_reply_to_name()
    if reply_to_email_addr is None:
        reply_to_email_addr = _get_default_reply_to_email_address()
    enqueue_job(
        "email.send",
        to_email_addr=to_email_addr,
        subject=subject,
        html_content=html_content,
        from_name=from_name,
        from_email_addr=from_email_addr,
        email_headers=email_headers,
        reply_to_name=reply_to_name,
        reply_to_email_addr=reply_to_email_addr,
        attachments=attachments,
    )


email_sender: EmailSender
if settings.EMAIL_SENDER == EmailSenderType.resend:
    email_sender = ResendEmailSender()
else:
    # Logging in development
    email_sender = LoggingEmailSender()
