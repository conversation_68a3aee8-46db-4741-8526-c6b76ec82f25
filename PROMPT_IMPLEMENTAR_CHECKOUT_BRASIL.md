# 🚀 PROMPT: Implementar Checkout Multi-Gateway Adaptado para Brasil

**Para**: <PERSON> 4.5  
**Projeto**: <PERSON>lu<PERSON> (fork do Polar.sh)  
**Objetivo**: Desacoplar Stripe, usar Pagar.me como default, adaptar checkout para Brasil  
**Contexto**: <PERSON><PERSON> `ANALISE_CHECKOUT_BRASIL.md` para entender o problema completo

---

## 🎯 MISSÃO PRINCIPAL

Você é um desenvolvedor sênior Python/TypeScript trabalhando no **Fluu**, uma plataforma de pagamentos SaaS para o Brasil (fork do Polar.sh). Sua missão é:

1. **Desacoplar o Stripe** do checkout (atualmente hardcoded)
2. **Usar Pagar.me como gateway padrão** (PIX + Boleto + Cartão)
3. **Adaptar o checkout para o mercado brasileiro** (PIX em destaque, CPF/CNPJ obrigatório, pt-BR)
4. **Manter compatibilidade** com Stripe para clientes que já o usam

---

## 📚 CONTEXTO DO PROJETO

### Estrutura Atual

```
server/polar/
├── checkout/
│   ├── service.py              # ❌ Stripe hardcoded nas linhas 438, 615, 771
│   ├── endpoints.py            # ⚠️ Confirmação não roteia por gateway
│   └── schemas.py
├── checkout_link/
│   ├── service.py              # ⚠️ Prefixo "polar_cl_" hardcoded
│   └── schemas.py
├── integrations/payment_providers/
│   ├── registry.py             # ✅ Registry já existe
│   ├── base.py                 # ✅ Interface PaymentProvider
│   ├── stripe/                 # ✅ Provider Stripe funcional
│   └── pagarme/                # ✅ Provider Pagar.me implementado
└── enums.py                    # PaymentProcessor.stripe | pagarme

clients/apps/web/src/
├── app/checkout/[clientSecret]/
│   ├── page.tsx                # Carrega checkout
│   └── ClientPage.tsx          # Renderiza FluuCheckout
└── components/Checkout/
    ├── FluuCheckout.tsx        # ✅ Componente principal
    └── CheckoutForm.tsx        # ❌ Sempre carrega Stripe Elements

clients/packages/checkout/src/
├── components/
│   └── CheckoutForm.tsx        # ❌ Switch apenas Stripe vs Dummy
└── providers/
    ├── CheckoutProvider.tsx
    └── CheckoutFormProvider.tsx
```

### Problemas Identificados

1. **Backend**: `PaymentProcessor.stripe` hardcoded em 3 locais
2. **Frontend**: Stripe Elements é única opção funcional (outros são "dummy")
3. **Prefixo**: "polar_cl_" e "polar_c_" aparecem nas URLs públicas
4. **Erros**: Stripe API 401 (chaves inválidas) causam checkout quebrado

---

## 🎯 IMPLEMENTAÇÃO INCREMENTAL

### FASE 1: Desacoplar Stripe no Backend ⚡ (PRIORIDADE MÁXIMA)

**Objetivo**: Checkout deve detectar qual gateway usar dinamicamente, não sempre Stripe.

#### 1.1 Criar método de seleção de gateway

**Arquivo**: `server/polar/checkout/service.py`  
**Localização**: Adicionar após linha 203 (logo depois da classe CheckoutService)

```python
def _get_default_payment_processor(
    self,
    product: Product | None = None,
    customer: Customer | None = None,
    checkout_link: CheckoutLink | None = None,
) -> PaymentProcessor:
    """
    Determina qual gateway de pagamento usar baseado em:
    1. CheckoutLink.payment_processor (se definido)
    2. Product.preferred_payment_processor (se definido)
    3. Customer brasileiro → Pagar.me
    4. ENABLE_PAGARME=true → Pagar.me
    5. Fallback → Stripe
    """
    log = structlog.get_logger()
    
    # 1. CheckoutLink tem prioridade máxima
    if checkout_link and checkout_link.payment_processor:
        log.info(
            "Using payment processor from checkout link",
            processor=checkout_link.payment_processor
        )
        return checkout_link.payment_processor
    
    # 2. Produto pode ter preferência configurada
    if product and hasattr(product, 'preferred_payment_processor'):
        if product.preferred_payment_processor:
            log.info(
                "Using payment processor from product",
                processor=product.preferred_payment_processor,
                product_id=product.id
            )
            return product.preferred_payment_processor
    
    # 3. Cliente brasileiro → Pagar.me
    if customer and customer.billing_address:
        if customer.billing_address.country == "BR" and settings.ENABLE_PAGARME:
            log.info(
                "Using Pagar.me for Brazilian customer",
                customer_id=customer.id
            )
            return PaymentProcessor.pagarme
    
    # 4. Default do sistema (Pagar.me se habilitado)
    if settings.ENABLE_PAGARME:
        log.info("Using Pagar.me as default payment processor")
        return PaymentProcessor.pagarme
    
    # 5. Fallback para Stripe
    log.info("Falling back to Stripe payment processor")
    return PaymentProcessor.stripe
```

#### 1.2 Criar método de preparação de metadata

**Arquivo**: `server/polar/checkout/service.py`  
**Localização**: Adicionar logo após `_get_default_payment_processor()`

```python
async def _prepare_payment_processor_metadata(
    self, 
    checkout: Checkout,
    session: AsyncSession
) -> dict[str, Any]:
    """
    Prepara metadata específico do gateway de pagamento.
    Este metadata é enviado ao frontend para configurar os forms.
    """
    log = structlog.get_logger()
    
    if checkout.payment_processor == PaymentProcessor.stripe:
        log.info("Preparing Stripe metadata", checkout_id=checkout.id)
        
        metadata: dict[str, Any] = {
            "publishable_key": settings.STRIPE_PUBLISHABLE_KEY,
        }
        
        # Stripe Customer Session (se cliente já existe no Stripe)
        if checkout.customer and checkout.customer.stripe_customer_id:
            try:
                stripe_customer_session = await stripe_service.create_customer_session(
                    checkout.customer.stripe_customer_id
                )
                metadata["customer_session_client_secret"] = (
                    stripe_customer_session.client_secret
                )
                log.info(
                    "Created Stripe customer session",
                    customer_id=checkout.customer.id
                )
            except Exception as e:
                log.warning(
                    "Failed to create Stripe customer session",
                    error=str(e),
                    customer_id=checkout.customer.id
                )
        
        return metadata
    
    elif checkout.payment_processor == PaymentProcessor.pagarme:
        log.info("Preparing Pagar.me metadata", checkout_id=checkout.id)
        
        return {
            "publishable_key": settings.PAGARME_PUBLISHABLE_KEY,
            "pix_enabled": True,
            "boleto_enabled": True,
            "credit_card_enabled": True,
            "currency": checkout.currency or "brl",
        }
    
    log.warning(
        "Unknown payment processor, returning empty metadata",
        processor=checkout.payment_processor
    )
    return {}
```

#### 1.3 Modificar método `create()` (linha 438)

**Arquivo**: `server/polar/checkout/service.py`  
**Localização**: Substituir linha 438

**ANTES**:
```python
checkout = Checkout(
    payment_processor=PaymentProcessor.stripe,  # ❌ HARDCODED
    client_secret=generate_token(prefix=CHECKOUT_CLIENT_SECRET_PREFIX),
    ...
)
```

**DEPOIS**:
```python
# Determinar gateway dinamicamente
payment_processor = self._get_default_payment_processor(
    product=product,
    customer=customer
)

checkout = Checkout(
    payment_processor=payment_processor,  # ✅ DINÂMICO
    client_secret=generate_token(prefix=CHECKOUT_CLIENT_SECRET_PREFIX),
    ...
)
```

**DEPOIS do checkout ser criado, substituir o bloco condicional (linhas 485-494)**:

**ANTES**:
```python
if checkout.payment_processor == PaymentProcessor.stripe:
    checkout.payment_processor_metadata = {
        **(checkout.payment_processor_metadata or {}),
        "publishable_key": settings.STRIPE_PUBLISHABLE_KEY,
    }
    ...
```

**DEPOIS**:
```python
# Preparar metadata específico do gateway
checkout.payment_processor_metadata = await self._prepare_payment_processor_metadata(
    checkout, session
)
```

#### 1.4 Modificar método `client_create()` (linha 615)

**Arquivo**: `server/polar/checkout/service.py`  
**Localização**: Substituir linha 615

**Aplicar as mesmas mudanças**:
1. Substituir `PaymentProcessor.stripe` por chamada a `_get_default_payment_processor()`
2. Substituir bloco condicional (linhas 630-642) por `_prepare_payment_processor_metadata()`

#### 1.5 Modificar método `checkout_link_create()` (linha 771)

**Arquivo**: `server/polar/checkout/service.py`  
**Localização**: Linha 771 já usa `checkout_link.payment_processor` ✅

**Apenas substituir bloco condicional (linhas 836-840)**:

**ANTES**:
```python
if checkout.payment_processor == PaymentProcessor.stripe:
    checkout.payment_processor_metadata = {
        **(checkout.payment_processor_metadata or {}),
        "publishable_key": settings.STRIPE_PUBLISHABLE_KEY,
    }
```

**DEPOIS**:
```python
# Preparar metadata específico do gateway
checkout.payment_processor_metadata = await self._prepare_payment_processor_metadata(
    checkout, session
)
```

#### 1.6 Verificar configurações

**Arquivo**: `server/polar/config.py`  
**Verificar linhas 196-209**

Certificar que:
- `ENABLE_PAGARME` está com default `True`
- Chaves Pagar.me estão configuráveis
- Stripe é opcional (pode estar vazio)

**Arquivo**: `deploy/cloud-run/env.yaml`  
**Adicionar/corrigir**:

```yaml
# Pagar.me (Gateway Default)
POLAR_ENABLE_PAGARME: "true"
POLAR_PAGARME_SECRET_KEY: sk_test_SUA_CHAVE_AQUI
POLAR_PAGARME_PUBLISHABLE_KEY: pk_test_SUA_CHAVE_AQUI

# Stripe (Opcional - pode ficar vazio se não usar)
POLAR_STRIPE_SECRET_KEY: ""
POLAR_STRIPE_PUBLISHABLE_KEY: ""
```

---

### FASE 2: Criar Componente Frontend Pagar.me 🎨

**Objetivo**: Criar formulário de checkout que suporta PIX, Boleto e Cartão.

#### 2.1 Criar `PagarmeCheckoutForm.tsx`

**Arquivo NOVO**: `clients/packages/checkout/src/components/PagarmeCheckoutForm.tsx`

```typescript
'use client'

import { useState, useCallback } from 'react'
import type { CheckoutPublic } from '@polar-sh/sdk/models/components/checkoutpublic'
import type { CheckoutPublicConfirmed } from '@polar-sh/sdk/models/components/checkoutpublicconfirmed'
import type { CheckoutUpdatePublic } from '@polar-sh/sdk/models/components/checkoutupdatepublic'
import Button from '@polar-sh/ui/components/atoms/Button'
import { UseFormReturn } from 'react-hook-form'
import { ThemingPresetProps } from '@polar-sh/ui/hooks/theming'

type PaymentMethod = 'pix' | 'boleto' | 'credit_card'

interface PagarmeCheckoutFormProps {
  form: UseFormReturn<CheckoutUpdatePublic>
  checkout: CheckoutPublic
  update: (data: CheckoutUpdatePublic) => Promise<CheckoutPublic>
  confirm: (data: any) => Promise<CheckoutPublicConfirmed>
  loading: boolean
  loadingLabel: string | undefined
  disabled?: boolean
  isUpdatePending?: boolean
  themePreset: ThemingPresetProps
}

const PagarmeCheckoutForm = (props: PagarmeCheckoutFormProps) => {
  const { checkout, confirm, loading } = props
  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod>('pix')
  const [showPixQrCode, setShowPixQrCode] = useState(false)
  const [pixQrCodeData, setPixQrCodeData] = useState<string>('')

  const handleConfirm = useCallback(async (data: any) => {
    // Adicionar método de pagamento aos dados
    const confirmData = {
      ...data,
      payment_method: paymentMethod,
    }
    
    const result = await confirm(confirmData)
    
    // Se PIX, mostrar QR Code
    if (paymentMethod === 'pix' && result.paymentProcessorMetadata?.pix_qr_code) {
      setPixQrCodeData(result.paymentProcessorMetadata.pix_qr_code)
      setShowPixQrCode(true)
    }
    
    return result
  }, [paymentMethod, confirm])

  return (
    <div className="space-y-6">
      {/* Seletor de Método de Pagamento */}
      <div className="space-y-3">
        <label className="text-sm font-medium">Método de Pagamento</label>
        
        <div className="grid grid-cols-1 gap-2">
          {/* PIX - Destacado */}
          <button
            type="button"
            onClick={() => setPaymentMethod('pix')}
            className={`
              flex items-center gap-3 p-4 rounded-lg border-2 transition-all
              ${paymentMethod === 'pix' 
                ? 'border-green-500 bg-green-50 dark:bg-green-900/20' 
                : 'border-gray-200 dark:border-gray-700 hover:border-green-300'
              }
            `}
          >
            <span className="text-2xl">⚡</span>
            <div className="flex-1 text-left">
              <div className="font-medium">PIX</div>
              <div className="text-xs text-gray-500 dark:text-gray-400">
                Aprovação instantânea
              </div>
            </div>
            {paymentMethod === 'pix' && (
              <span className="text-green-600 dark:text-green-400">✓</span>
            )}
          </button>

          {/* Cartão de Crédito */}
          <button
            type="button"
            onClick={() => setPaymentMethod('credit_card')}
            className={`
              flex items-center gap-3 p-4 rounded-lg border-2 transition-all
              ${paymentMethod === 'credit_card' 
                ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
                : 'border-gray-200 dark:border-gray-700 hover:border-blue-300'
              }
            `}
          >
            <span className="text-2xl">💳</span>
            <div className="flex-1 text-left">
              <div className="font-medium">Cartão de Crédito</div>
              <div className="text-xs text-gray-500 dark:text-gray-400">
                Todas as bandeiras
              </div>
            </div>
            {paymentMethod === 'credit_card' && (
              <span className="text-blue-600 dark:text-blue-400">✓</span>
            )}
          </button>

          {/* Boleto */}
          <button
            type="button"
            onClick={() => setPaymentMethod('boleto')}
            className={`
              flex items-center gap-3 p-4 rounded-lg border-2 transition-all
              ${paymentMethod === 'boleto' 
                ? 'border-orange-500 bg-orange-50 dark:bg-orange-900/20' 
                : 'border-gray-200 dark:border-gray-700 hover:border-orange-300'
              }
            `}
          >
            <span className="text-2xl">🧾</span>
            <div className="flex-1 text-left">
              <div className="font-medium">Boleto Bancário</div>
              <div className="text-xs text-gray-500 dark:text-gray-400">
                Vence em 3 dias úteis
              </div>
            </div>
            {paymentMethod === 'boleto' && (
              <span className="text-orange-600 dark:text-orange-400">✓</span>
            )}
          </button>
        </div>
      </div>

      {/* TODO: Adicionar BaseCheckoutForm com campos de email, nome, etc */}
      {/* TODO: Adicionar campos específicos do método (cartão se credit_card) */}
      {/* TODO: Adicionar modal QR Code PIX */}
      
      <Button
        type="button"
        size="lg"
        className="w-full"
        disabled={props.disabled || props.isUpdatePending}
        loading={loading}
        onClick={() => {
          // TODO: Validar form e chamar handleConfirm
        }}
      >
        {paymentMethod === 'pix' && 'Gerar QR Code PIX'}
        {paymentMethod === 'credit_card' && 'Pagar com Cartão'}
        {paymentMethod === 'boleto' && 'Gerar Boleto'}
      </Button>
    </div>
  )
}

export default PagarmeCheckoutForm
```

#### 2.2 Integrar no CheckoutForm.tsx

**Arquivo**: `clients/packages/checkout/src/components/CheckoutForm.tsx`  
**Localização**: Substituir linhas 1055-1064

**ANTES**:
```typescript
const CheckoutForm = (props: CheckoutFormProps) => {
  const { checkout: { paymentProcessor } } = props

  if (paymentProcessor === 'stripe') {
    return <StripeCheckoutForm {...props} />
  }
  return <DummyCheckoutForm {...props} />
}
```

**DEPOIS**:
```typescript
const CheckoutForm = (props: CheckoutFormProps) => {
  const { checkout: { paymentProcessor } } = props

  if (paymentProcessor === 'stripe') {
    return <StripeCheckoutForm {...props} />
  }
  
  if (paymentProcessor === 'pagarme') {
    return <PagarmeCheckoutForm {...props} />
  }
  
  // Fallback para gateways desconhecidos
  return <DummyCheckoutForm {...props} />
}
```

**Adicionar import no topo do arquivo**:
```typescript
import PagarmeCheckoutForm from './PagarmeCheckoutForm'
```

---

### FASE 3: Implementar Confirmação Pagar.me no Backend 🔐

**Objetivo**: Backend deve processar pagamentos PIX/Boleto/Cartão via Pagar.me.

#### 3.1 Criar método `confirm_pagarme()`

**Arquivo**: `server/polar/checkout/service.py`  
**Localização**: Adicionar após método `confirm()` (por volta da linha 1500)

```python
async def confirm_pagarme(
    self,
    session: AsyncSession,
    locker: Locker,
    checkout: Checkout,
    checkout_confirm: CheckoutConfirm,
) -> Checkout:
    """
    Confirma checkout usando Pagar.me.
    Suporta: PIX, Boleto, Cartão de Crédito
    """
    log = structlog.get_logger()
    
    log.info(
        "Confirming checkout with Pagar.me",
        checkout_id=checkout.id,
        payment_method=checkout_confirm.get("payment_method", "unknown")
    )
    
    # 1. Validar checkout está aberto
    if checkout.status != CheckoutStatus.open:
        raise NotOpenCheckout(checkout)
    
    # 2. Obter provider Pagar.me
    try:
        provider = PaymentProviderRegistry.get(PaymentProcessor.pagarme)
    except Exception as e:
        log.error("Failed to get Pagar.me provider", error=str(e))
        raise PolarError("Pagar.me provider not available")
    
    # 3. Garantir customer existe
    customer = await self._ensure_customer(session, checkout)
    
    # 4. Validar CPF/CNPJ para Brasil
    if not customer.tax_id:
        raise ValidationError("CPF/CNPJ é obrigatório para pagamentos no Brasil")
    
    # 5. Criar pagamento baseado no método
    payment_method = checkout_confirm.get("payment_method", "pix")
    
    try:
        if payment_method == "pix":
            # Criar pagamento PIX
            payment = await provider.create_pix_payment(
                amount=checkout.total_amount,
                currency=checkout.currency or "brl",
                customer={
                    "id": customer.id,
                    "email": customer.email,
                    "name": customer.name,
                    "tax_id": customer.tax_id[0] if customer.tax_id else None,
                },
                metadata={
                    "checkout_id": str(checkout.id),
                    "organization_id": str(checkout.organization_id),
                    "product_id": str(checkout.product_id) if checkout.product_id else None,
                }
            )
            
            # Salvar dados do PIX no metadata
            checkout.payment_processor_metadata = {
                **checkout.payment_processor_metadata,
                "pix_qr_code": payment.get("pix_qr_code"),
                "pix_qr_code_url": payment.get("pix_qr_code_url"),
                "pix_expires_at": payment.get("expires_at"),
                "payment_id": payment.get("id"),
            }
            
            log.info(
                "PIX payment created",
                checkout_id=checkout.id,
                payment_id=payment.get("id")
            )
        
        elif payment_method == "boleto":
            # TODO: Implementar Boleto
            raise NotImplementedError("Boleto not implemented yet")
        
        elif payment_method == "credit_card":
            # TODO: Implementar Cartão
            raise NotImplementedError("Credit card not implemented yet")
        
        else:
            raise ValidationError(f"Invalid payment method: {payment_method}")
    
    except Exception as e:
        log.error(
            "Failed to create payment",
            error=str(e),
            payment_method=payment_method,
            checkout_id=checkout.id
        )
        raise PaymentError(f"Failed to process payment: {str(e)}")
    
    # 6. Atualizar status do checkout
    checkout.status = CheckoutStatus.confirmed
    checkout.confirmed_at = utc_now()
    
    session.add(checkout)
    await session.commit()
    
    log.info(
        "Checkout confirmed with Pagar.me",
        checkout_id=checkout.id,
        status=checkout.status,
        payment_method=payment_method
    )
    
    return checkout
```

#### 3.2 Modificar endpoint de confirmação

**Arquivo**: `server/polar/checkout/endpoints.py`  
**Localização**: Modificar `client_confirm()` (por volta da linha 220)

**Encontrar o método `client_confirm()` e modificar para rotear por gateway**:

**DEPOIS da linha que obtém o checkout, adicionar roteamento**:

```python
# Rotear confirmação baseado no payment processor
if checkout.payment_processor == PaymentProcessor.stripe:
    return await checkout_service.confirm(
        session, locker, checkout, checkout_confirm, ip_geolocation_client
    )
elif checkout.payment_processor == PaymentProcessor.pagarme:
    return await checkout_service.confirm_pagarme(
        session, locker, checkout, checkout_confirm
    )
else:
    raise BadRequest(f"Unsupported payment processor: {checkout.payment_processor}")
```

---

### FASE 4 (OPCIONAL): Remover Prefixo "polar_" 🏷️

**Objetivo**: URLs mais limpas sem branding do Polar.

#### 4.1 Modificar constantes

**Arquivo**: `server/polar/checkout_link/service.py`  
**Linha 38**:

```python
# ANTES
CHECKOUT_LINK_CLIENT_SECRET_PREFIX = "polar_cl_"

# DEPOIS (opção 1 - usar "fluu")
CHECKOUT_LINK_CLIENT_SECRET_PREFIX = "fluu_cl_"

# DEPOIS (opção 2 - remover prefixo)
CHECKOUT_LINK_CLIENT_SECRET_PREFIX = ""
```

**Arquivo**: `server/polar/checkout/service.py`  
**Linha 201**:

```python
# ANTES
CHECKOUT_CLIENT_SECRET_PREFIX = "polar_c_"

# DEPOIS (opção 1 - usar "fluu")
CHECKOUT_CLIENT_SECRET_PREFIX = "fluu_c_"

# DEPOIS (opção 2 - remover prefixo)
CHECKOUT_CLIENT_SECRET_PREFIX = ""
```

**⚠️ ATENÇÃO**: Se remover prefixo completamente, ajustar testes que verificam o prefixo.

---

## ✅ CHECKLIST DE IMPLEMENTAÇÃO

Marque cada item conforme implementa:

### Backend
- [ ] Criar `_get_default_payment_processor()` em `checkout/service.py`
- [ ] Criar `_prepare_payment_processor_metadata()` em `checkout/service.py`
- [ ] Modificar `create()` linha 438 (usar método dinâmico)
- [ ] Modificar `client_create()` linha 615 (usar método dinâmico)
- [ ] Modificar `checkout_link_create()` linha 836 (usar metadata method)
- [ ] Criar `confirm_pagarme()` em `checkout/service.py`
- [ ] Modificar `client_confirm()` em `checkout/endpoints.py` (rotear por gateway)
- [ ] Adicionar chaves Pagar.me válidas em `deploy/cloud-run/env.yaml`
- [ ] Testar checkout com Pagar.me via API

### Frontend
- [ ] Criar `PagarmeCheckoutForm.tsx` básico
- [ ] Integrar no `CheckoutForm.tsx` (switch de gateway)
- [ ] Adicionar import de `PagarmeCheckoutForm`
- [ ] Testar renderização com `paymentProcessor: 'pagarme'`
- [ ] Implementar campos de formulário completos
- [ ] Criar modal QR Code PIX
- [ ] Testar fluxo completo PIX

### Testes
- [ ] Criar checkout via API → deve usar Pagar.me se ENABLE_PAGARME=true
- [ ] Criar checkout com customer BR → deve usar Pagar.me
- [ ] Criar checkout com checkout_link Stripe → deve usar Stripe
- [ ] Confirmar checkout PIX → deve retornar QR Code
- [ ] Frontend renderiza form Pagar.me corretamente
- [ ] Frontend renderiza form Stripe quando processor=stripe

---

## 🎯 ORDEM DE EXECUÇÃO RECOMENDADA

### DIA 1: Backend Core
1. Implementar `_get_default_payment_processor()`
2. Implementar `_prepare_payment_processor_metadata()`
3. Modificar os 3 métodos de criação de checkout
4. Testar via API: checkout deve usar Pagar.me

### DIA 2: Backend Confirmação
1. Implementar `confirm_pagarme()` básico (apenas PIX)
2. Modificar `client_confirm()` para rotear
3. Testar confirmação PIX via API
4. Validar QR Code é retornado

### DIA 3: Frontend Base
1. Criar `PagarmeCheckoutForm.tsx` com seletor de método
2. Integrar no `CheckoutForm.tsx`
3. Testar renderização (pode ser dummy ainda)
4. Validar switch de gateway funciona

### DIA 4: Frontend PIX
1. Implementar campos completos no form
2. Criar modal QR Code PIX
3. Integrar confirmação
4. Testar fluxo E2E: criar → pagar → confirmar

### DIA 5: Polimento
1. Adicionar traduções pt-BR
2. Melhorar UX (banners PIX, instruções)
3. Testar casos edge
4. Documentar mudanças

---

## 📋 REGRAS IMPORTANTES

### ✅ FAZER

1. **Logging abundante**: Use `structlog` em todo lugar
   ```python
   log.info("Action performed", checkout_id=checkout.id, processor=processor)
   ```

2. **Try/catch defensivo**: Gateway pode falhar
   ```python
   try:
       payment = await provider.create_pix_payment(...)
   except Exception as e:
       log.error("Payment failed", error=str(e))
       raise PaymentError("Failed to process payment")
   ```

3. **Validação**: Sempre validar dados críticos
   ```python
   if not customer.tax_id:
       raise ValidationError("CPF/CNPJ required for Brazil")
   ```

4. **Compatibilidade**: Não quebrar Stripe existente
   ```python
   if checkout.payment_processor == PaymentProcessor.stripe:
       # Código Stripe existente
   elif checkout.payment_processor == PaymentProcessor.pagarme:
       # Novo código Pagar.me
   ```

### ❌ NÃO FAZER

1. **Não remover código Stripe** - apenas desacoplar
2. **Não hardcodar nada** - usar configurações e detectar dinamicamente
3. **Não expor secret keys** - apenas publishable keys no frontend
4. **Não quebrar checkouts antigos** - manter compatibilidade
5. **Não commitar sem testar** - validar cada fase

---

## 🐛 DEBUG

### Se checkout não usa Pagar.me:

```python
# Adicionar logs temporários em _get_default_payment_processor()
log.info("Checking payment processor", 
         has_checkout_link=checkout_link is not None,
         has_product=product is not None,
         enable_pagarme=settings.ENABLE_PAGARME)
```

### Se frontend quebra:

```typescript
// Verificar no console do navegador
console.log('Payment Processor:', checkout.paymentProcessor)
console.log('Metadata:', checkout.paymentProcessorMetadata)
```

### Se PIX não gera QR Code:

```python
# Verificar se provider está registrado
from polar.integrations.payment_providers.registry import PaymentProviderRegistry
from polar.enums import PaymentProcessor

provider = PaymentProviderRegistry.get(PaymentProcessor.pagarme)
print(f"Provider: {provider}")
```

---

## 📚 ARQUIVOS DE REFERÊNCIA

Leia estes para entender o código existente:

1. `ANALISE_CHECKOUT_BRASIL.md` - **Análise completa do problema**
2. `README_MULTI_GATEWAY.md` - Arquitetura multi-gateway
3. `GUIA_IMPLEMENTACAO_GATEWAYS.md` - Como implementar novos gateways
4. `server/polar/integrations/payment_providers/pagarme/provider.py` - Provider Pagar.me
5. `server/polar/integrations/payment_providers/stripe/service.py` - Referência Stripe

---

## 🚀 COMEÇAR AGORA

**Sua primeira task**:

```bash
# 1. Ler a análise completa
cat ANALISE_CHECKOUT_BRASIL.md

# 2. Abrir o arquivo principal
code server/polar/checkout/service.py

# 3. Encontrar linha 203 (após classe CheckoutService)

# 4. Implementar _get_default_payment_processor()

# 5. Testar com um print temporário:
print(f"Gateway selecionado: {self._get_default_payment_processor()}")
```

Boa sorte! 🚀🇧🇷


