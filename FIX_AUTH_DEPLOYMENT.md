# 🔧 Fix de Autenticação - Guia de Deployment

## 📋 Resumo do Problema

O sistema de autenticação com OTP estava falhando porque:

1. **Nome do cookie inconsistente**: Frontend procurava `polar_session` mas backend setava `fluu_session`
2. **Domínio do cookie**: <PERSON><PERSON> configurado para `127.0.0.1` não funcionava entre portas diferentes
3. **CORS/Cookie cross-origin**: Formulário HTML POST direto para backend violava CSP e cookies não eram compartilhados entre frontend (porta 3000) e backend (porta 8000)

## ✅ Correções Implementadas

### 1. Backend (`server/`)

#### `server/polar/config.py`
```python
# Linha 92: Mudou de string fixa para None para permitir funcionamento local
USER_SESSION_COOKIE_DOMAIN: str | None = None  # Era: "127.0.0.1"
```

#### `server/polar/auth/service.py`
```python
# Linhas 141-163: Atualizada para não setar domain se for None
def _set_user_session_cookie(self, request: Request, response: R, value: str, expires: int | datetime) -> R:
    is_localhost = request.url.hostname in ["127.0.0.1", "localhost"]
    secure = False if is_localhost else True
    
    cookie_params = {
        "key": settings.USER_SESSION_COOKIE_KEY,
        "value": value,
        "expires": expires,
        "path": "/",
        "secure": secure,
        "httponly": True,
        "samesite": "lax",
    }
    
    # Only set domain if explicitly configured
    if settings.USER_SESSION_COOKIE_DOMAIN is not None:
        cookie_params["domain"] = settings.USER_SESSION_COOKIE_DOMAIN
    
    response.set_cookie(**cookie_params)
    return response
```

### 2. Frontend (`clients/apps/web/`)

#### `clients/apps/web/src/proxy.ts`
```typescript
// Linha 8: Corrigido fallback do nome do cookie
const POLAR_AUTH_COOKIE_KEY =
  process.env.POLAR_AUTH_COOKIE_KEY || 'fluu_session'  // Era: 'polar_session'
```

#### `clients/apps/web/src/utils/config.ts`
```typescript
// Linha 18: Corrigido fallback do nome do cookie
AUTH_COOKIE_KEY: process.env.POLAR_AUTH_COOKIE_KEY || 'fluu_session',  // Era: 'polar_session'
```

#### `clients/apps/web/src/app/api/auth/login-code/authenticate/route.ts` ⭐ **NOVO ARQUIVO**
```typescript
// API Route para proxy de autenticação que gerencia cookies corretamente
// Este arquivo permite que o frontend faça autenticação sem violar CSP
// e compartilha cookies na mesma origem
```

#### `clients/apps/web/src/app/(main)/login/code/verify/ClientPage.tsx`
```typescript
// Mudou de formulário HTML POST para fetch via API route
const response = await fetch(
  `/api/auth/login-code/authenticate?${urlSearchParams.toString()}`,
  {
    method: 'POST',
    credentials: 'include',
    body: formData,
  }
)
```

## 🚀 Passos para Deploy em Produção

### 1. Backend (API)

```bash
cd /Users/<USER>/Documents/www/Gateways/polar/server

# Verificar mudanças
git diff server/polar/config.py server/polar/auth/service.py

# Commit
git add server/polar/config.py server/polar/auth/service.py
git commit -m "fix: session cookie domain configuration for production"

# Deploy (ajustar conforme seu processo)
# Exemplo Render/Railway/etc:
git push origin main
```

**⚠️ IMPORTANTE**: Configure a variável de ambiente em produção:
```bash
USER_SESSION_COOKIE_DOMAIN=.fluu.digital  # Para compartilhar entre api.fluu.digital e app
```

### 2. Frontend (Next.js)

```bash
cd /Users/<USER>/Documents/www/Gateways/polar/clients

# Verificar mudanças
git status

# Commit TODOS os arquivos
git add apps/web/src/proxy.ts
git add apps/web/src/utils/config.ts
git add apps/web/src/app/api/auth/login-code/authenticate/route.ts
git add apps/web/src/app/(main)/login/code/verify/ClientPage.tsx
git add apps/web/next.config.mjs

git commit -m "fix: authentication flow with API route proxy for CSP compliance"

# Deploy
git push origin main
```

**⚠️ IMPORTANTE**: Configure a variável de ambiente em produção:
```bash
POLAR_AUTH_COOKIE_KEY=fluu_session
NEXT_PUBLIC_API_URL=https://api.fluu.digital
```

### 3. Verificação de CSP

O `next.config.mjs` já está configurado, mas verifique se em produção o CSP permite:
- `form-action 'self'` (já está correto, pois agora o form submete para a mesma origem)

## 🧪 Teste Local (VALIDADO ✅)

```bash
# 1. Solicitar código
curl -X POST "http://127.0.0.1:8000/v1/login-code/request" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>"}'

# 2. Pegar código dos logs
tail -100 /tmp/polar-api-logs.txt | grep "acesso"

# 3. Autenticar
curl -X POST "http://127.0.0.1:8000/v1/login-code/authenticate?return_to=%2Fdashboard&email=seuemail%40example.com" \
  -H "Content-Type: multipart/form-data" \
  -F "code=CODIGO_AQUI" \
  -v -c /tmp/cookies.txt

# ✅ Deve retornar HTTP 303 e Set-Cookie com fluu_session
```

## 🔍 Teste em Produção

Após deploy:

1. Abra `https://app.fluu.digital/login` (ou seu domínio)
2. Insira email
3. Insira código OTP recebido
4. **Deve redirecionar para dashboard E permanecer logado**

### Debug em Produção

Se ainda houver problemas:

```bash
# Verifique variáveis de ambiente
echo $USER_SESSION_COOKIE_DOMAIN
echo $POLAR_AUTH_COOKIE_KEY

# Verifique logs do backend
# (no seu provedor de hosting)

# Verifique console do browser
# Deve ver requisição para /api/auth/login-code/authenticate (não diretamente para API)
```

## 📝 Checklist de Deploy

### Backend
- [ ] Arquivo `server/polar/config.py` atualizado
- [ ] Arquivo `server/polar/auth/service.py` atualizado
- [ ] Variável `USER_SESSION_COOKIE_DOMAIN=.fluu.digital` configurada em produção
- [ ] Backend deployado e rodando
- [ ] Testar endpoint `/v1/login-code/authenticate` via curl

### Frontend
- [ ] Arquivo `clients/apps/web/src/proxy.ts` atualizado
- [ ] Arquivo `clients/apps/web/src/utils/config.ts` atualizado
- [ ] Arquivo `clients/apps/web/src/app/api/auth/login-code/authenticate/route.ts` criado
- [ ] Arquivo `clients/apps/web/src/app/(main)/login/code/verify/ClientPage.tsx` atualizado
- [ ] Variável `POLAR_AUTH_COOKIE_KEY=fluu_session` configurada em produção
- [ ] Variável `NEXT_PUBLIC_API_URL=https://api.fluu.digital` configurada em produção
- [ ] Frontend deployado e rodando
- [ ] Testar login completo no browser

## ✅ Resultado Esperado

1. Usuário entra com email → Recebe código OTP
2. Usuário entra com código → **Autentica com sucesso**
3. Redireciona para `/dashboard` e **permanece logado**
4. Refresh da página **mantém usuário logado**
5. Sem erros de CSP no console
6. Sem erros de CORS no console

## 🆘 Troubleshooting

### Erro: "form-action violates CSP"
- **Causa**: Frontend antigo ainda deployado
- **Solução**: Redesploy do frontend com ClientPage.tsx atualizado

### Erro: Cookie não persiste
- **Causa**: Domain do cookie errado
- **Solução**: Verifique `USER_SESSION_COOKIE_DOMAIN=.fluu.digital` no backend

### Erro: "Invalid or expired code"
- **Causa**: Código expirou (30 minutos) ou já foi usado
- **Solução**: Solicite novo código

### Erro: Redireciona de volta para /login
- **Causa**: Cookie não está sendo lido pelo middleware
- **Solução**: Verifique `POLAR_AUTH_COOKIE_KEY` no frontend

---

**Data da correção**: 2025-11-10
**Testado em**: Ambiente local ✅
**Aguardando**: Deploy em produção

