# 🚀 DEPLOY RÁPIDO - Correções de Autenticação

## ✅ O QUE FOI ATUALIZADO

### Arquivos de Configuração (env.yaml)
- ✅ `deploy/cloud-run/env.yaml` - Backend
- ✅ `deploy/cloud-run/env-worker.yaml` - Worker  
- ✅ `deploy/cloud-run/env-frontend.yaml` - Frontend

### Novas Variáveis Adicionadas

#### Backend e Worker
```yaml
POLAR_USER_SESSION_COOKIE_DOMAIN: .fluu.digital
POLAR_USER_SESSION_COOKIE_KEY: fluu_session
POLAR_GOOGLE_CLIENT_ID: 923457232981-aj399ub5qflffahehklunc9sudu93eeu.apps.googleusercontent.com
POLAR_GOOGLE_CLIENT_SECRET: 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
```

#### Frontend
```yaml
POLAR_AUTH_COOKIE_KEY: fluu_session  # Mudou de polar_session
```

---

## 🚀 OPÇÃO 1: Deploy Completo (Recomendado)

```bash
cd /Users/<USER>/Documents/www/Gateways/polar

# Executar script interativo
./deploy/cloud-run/deploy-auth-fix.sh
```

O script vai perguntar o que você quer deployar:
- **Opção 5**: Deploy completo (Backend + Worker + Frontend)
- **Opção 6**: Apenas atualizar variáveis (MAIS RÁPIDO - sem rebuild)

### ⚡ RECOMENDAÇÃO: Use Opção 6 Primeiro

Se você já tem os serviços rodando e só quer atualizar as variáveis:

```bash
./deploy/cloud-run/deploy-auth-fix.sh
# Escolher opção: 6
```

Isso atualiza as variáveis em **~2 minutos** sem rebuild.

---

## 🚀 OPÇÃO 2: Deploy Manual

### 1. Apenas Atualizar Variáveis (RÁPIDO ~2 min)

```bash
cd /Users/<USER>/Documents/www/Gateways/polar/deploy/cloud-run

# Backend
gcloud run services update fluu-api \
  --region us-east1 \
  --env-vars-file=env.yaml

# Worker
gcloud run services update fluu-worker \
  --region us-east1 \
  --env-vars-file=env-worker.yaml

# Frontend
gcloud run services update fluu-web \
  --region us-east1 \
  --env-vars-file=env-frontend.yaml
```

### 2. Deploy Completo com Rebuild (LENTO ~20-30 min)

```bash
cd /Users/<USER>/Documents/www/Gateways/polar

# Backend (5-10 min)
./deploy/cloud-run/deploy-backend.sh

# Worker (usa mesma imagem do backend, ~2 min)
./deploy/cloud-run/deploy-worker.sh

# Frontend (10-20 min)
./deploy/cloud-run/deploy-frontend.sh
```

---

## ⚠️ IMPORTANTE: Configurar Google Cloud Console

**ANTES DE TESTAR**, você precisa configurar as URLs no Google Cloud Console:

### 1. Acesse
https://console.cloud.google.com/apis/credentials?project=pix-api-proxy-1758593444

### 2. Clique no Client ID: `923457232981-aj399ub5qflffahehklunc9sudu93eeu.apps.googleusercontent.com`

### 3. Adicione estas URLs em "Authorized redirect URIs":
```
https://api.fluu.digital/v1/integrations/google/callback
https://fluu-api-iuu5qv6jja-ue.a.run.app/v1/integrations/google/callback
```

### 4. Adicione estas URLs em "Authorized JavaScript origins":
```
https://fluu.digital
https://api.fluu.digital
https://fluu-web-iuu5qv6jja-ue.a.run.app
https://fluu-api-iuu5qv6jja-ue.a.run.app
```

### 5. Clique em "SAVE" e aguarde ~5 minutos

---

## 🧪 TESTE APÓS DEPLOY

### 1. Verificar se serviços estão rodando

```bash
# Backend
gcloud run services describe fluu-api --region us-east1 --format="get(status.url)"

# Worker
gcloud run services describe fluu-worker --region us-east1 --format="get(status.url)"

# Frontend
gcloud run services describe fluu-web --region us-east1 --format="get(status.url)"
```

### 2. Testar no Browser

#### Acesse: https://fluu.digital/login

#### Teste 1: Login com Email + OTP
1. Insira seu email
2. Receba código no email
3. Insira código
4. **Deve redirecionar para /dashboard e permanecer logado** ✅

#### Teste 2: Login com Google
1. Clique em "Continue with Google"
2. Autorize no Google
3. **Deve redirecionar para /dashboard e permanecer logado** ✅

### 3. Verificar Logs se houver erro

```bash
# Ver últimos logs do backend
gcloud run services logs read fluu-api --region us-east1 --limit 50

# Ver últimos logs do worker
gcloud run services logs read fluu-worker --region us-east1 --limit 50

# Ver últimos logs do frontend
gcloud run services logs read fluu-web --region us-east1 --limit 50
```

---

## 🔍 TROUBLESHOOTING

### Erro: CSP "form-action violates"
**Causa**: Frontend antigo ainda deployado

**Solução**:
```bash
./deploy/cloud-run/deploy-frontend.sh
```

### Erro: "redirect_uri_mismatch" (Google)
**Causa**: URL não configurada no Google Cloud Console

**Solução**:
1. Adicione exatamente: `https://api.fluu.digital/v1/integrations/google/callback`
2. Aguarde 5 minutos
3. Teste novamente

### Erro: Cookie não persiste
**Causa**: Domain do cookie errado

**Verificar**:
```bash
# Deve retornar: .fluu.digital
gcloud run services describe fluu-api --region us-east1 \
  --format="get(spec.template.spec.containers[0].env)" | grep COOKIE_DOMAIN
```

### Erro: "Invalid or expired code"
**Causa**: Código OTP expirou (30 min) ou email não foi enviado

**Verificar logs do worker**:
```bash
gcloud run services logs read fluu-worker --region us-east1 --limit 50 | grep email
```

---

## 📊 STATUS DOS ARQUIVOS

### ✅ Arquivos Atualizados (prontos para deploy)
- `deploy/cloud-run/env.yaml` - Backend
- `deploy/cloud-run/env-worker.yaml` - Worker
- `deploy/cloud-run/env-frontend.yaml` - Frontend
- `deploy/cloud-run/deploy-auth-fix.sh` - Script de deploy

### ✅ Código Atualizado (precisa commit + deploy)
- `server/polar/config.py`
- `server/polar/auth/service.py`
- `clients/apps/web/src/proxy.ts`
- `clients/apps/web/src/utils/config.ts`
- `clients/apps/web/src/app/api/auth/login-code/authenticate/route.ts` (NOVO)
- `clients/apps/web/src/app/(main)/login/code/verify/ClientPage.tsx`
- `clients/apps/web/next.config.mjs`

---

## 🎯 COMANDO RÁPIDO

### Atualizar APENAS variáveis (2 min):
```bash
cd /Users/<USER>/Documents/www/Gateways/polar
./deploy/cloud-run/deploy-auth-fix.sh
# Escolher opção: 6
```

### Deploy completo com novo código (30 min):
```bash
cd /Users/<USER>/Documents/www/Gateways/polar
./deploy/cloud-run/deploy-auth-fix.sh
# Escolher opção: 5
```

---

## ✅ CHECKLIST FINAL

- [ ] Executar `./deploy/cloud-run/deploy-auth-fix.sh` (opção 6 ou 5)
- [ ] Configurar URLs no Google Cloud Console
- [ ] Aguardar ~5 minutos (propagação Google)
- [ ] Testar login com email + OTP em https://fluu.digital/login
- [ ] Testar login com Google em https://fluu.digital/login
- [ ] Verificar se sessão persiste após refresh

---

**Data**: 2025-11-10  
**Tempo estimado**: 2-30 minutos (dependendo da opção escolhida)  
**Status**: ✅ Pronto para executar

