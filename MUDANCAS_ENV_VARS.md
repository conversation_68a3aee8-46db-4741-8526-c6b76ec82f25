# 🔄 MUDANÇAS NAS VARIÁVEIS DE AMBIENTE

## 📊 COMPARAÇÃO: ANTES vs DEPOIS

### 🔴 Backend (`env.yaml`)

#### ❌ ANTES (<PERSON><PERSON>)
```yaml
POLAR_USER_SESSION_COOKIE_DOMAIN: fluu.digital
# ⬆️ SEM ponto, não compartilha entre subdomínios
# ⬇️ Variável não existia
POLAR_GOOGLE_CLIENT_SECRET: 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
# ⬆️ Secret antigo
```

#### ✅ DEPOIS (Valores Novos)
```yaml
POLAR_USER_SESSION_COOKIE_DOMAIN: .fluu.digital
# ⬆️ COM ponto, compartilha entre api.fluu.digital e fluu.digital
POLAR_USER_SESSION_COOKIE_KEY: fluu_session
# ⬆️ NOVO: Padroniza nome do cookie
POLAR_GOOGLE_CLIENT_SECRET: 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
# ⬆️ Secret novo e correto
```

---

### 🟡 Worker (`env-worker.yaml`)

#### ❌ ANTES
```yaml
# Não tinha variáveis de sessão configuradas
POLAR_GOOGLE_CLIENT_SECRET: 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
# ⬆️ Secret antigo
```

#### ✅ DEPOIS
```yaml
POLAR_USER_SESSION_COOKIE_DOMAIN: .fluu.digital
POLAR_USER_SESSION_COOKIE_KEY: fluu_session
# ⬆️ NOVO: Adicionado para consistência
POLAR_GOOGLE_CLIENT_SECRET: 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
# ⬆️ Secret novo e correto
```

---

### 🔵 Frontend (`env-frontend.yaml`)

#### ❌ ANTES
```yaml
POLAR_AUTH_COOKIE_KEY: polar_session
# ⬆️ Nome errado! Backend usa fluu_session
```

#### ✅ DEPOIS
```yaml
POLAR_AUTH_COOKIE_KEY: fluu_session
# ⬆️ Corrigido para corresponder ao backend
```

---

## 📝 RESUMO DAS MUDANÇAS

### Variáveis ADICIONADAS ✨
- `POLAR_USER_SESSION_COOKIE_KEY: fluu_session` (Backend, Worker)

### Variáveis MODIFICADAS 🔧
- `POLAR_USER_SESSION_COOKIE_DOMAIN`: `fluu.digital` → `.fluu.digital` (Backend, Worker)
- `POLAR_AUTH_COOKIE_KEY`: `polar_session` → `fluu_session` (Frontend)
- `POLAR_GOOGLE_CLIENT_SECRET`: Secret antigo → `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` (Backend, Worker)

### Variáveis MANTIDAS ✓
- `POLAR_GOOGLE_CLIENT_ID: 923457232981-aj399ub5qflffahehklunc9sudu93eeu.apps.googleusercontent.com`
- `NEXT_PUBLIC_API_URL: https://api.fluu.digital`
- `NEXT_PUBLIC_FRONTEND_BASE_URL: https://fluu.digital`

---

## 🎯 POR QUE CADA MUDANÇA?

### 1. `.fluu.digital` (com ponto)
**Problema**: `fluu.digital` só funciona em `fluu.digital`, não em `api.fluu.digital`  
**Solução**: `.fluu.digital` funciona em todos os subdomínios  
**Resultado**: Cookie compartilhado entre API e Frontend ✅

### 2. `fluu_session` (padronizado)
**Problema**: Backend setava `fluu_session`, Frontend procurava `polar_session`  
**Solução**: Todos usam `fluu_session`  
**Resultado**: Frontend consegue ler o cookie do backend ✅

### 3. Novo Google Client Secret
**Problema**: Secret antigo pode ter sido revogado ou incorreto  
**Solução**: Usar o secret que você forneceu  
**Resultado**: Google OAuth funcionará corretamente ✅

### 4. Cookie Key no Worker
**Problema**: Worker não tinha configuração de cookie  
**Solução**: Adicionar as mesmas configurações do backend  
**Resultado**: Consistência entre serviços ✅

---

## 📦 ARQUIVOS MODIFICADOS

```
✅ deploy/cloud-run/env.yaml (Backend)
✅ deploy/cloud-run/env-worker.yaml (Worker)
✅ deploy/cloud-run/env-frontend.yaml (Frontend)
✅ deploy/cloud-run/deploy-auth-fix.sh (Script de deploy NOVO)
✅ DEPLOY_COMMANDS.sh (Este arquivo NOVO)
```

---

## 🚀 EXECUTE O DEPLOY AGORA

### Opção 1: Script Automático (RECOMENDADO)
```bash
cd /Users/<USER>/Documents/www/Gateways/polar
./DEPLOY_COMMANDS.sh
```

### Opção 2: Script Interativo
```bash
cd /Users/<USER>/Documents/www/Gateways/polar
./deploy/cloud-run/deploy-auth-fix.sh
# Escolher opção: 6 (atualizar variáveis apenas)
```

### Opção 3: Comandos Manuais
```bash
cd /Users/<USER>/Documents/www/Gateways/polar/deploy/cloud-run

# Backend
gcloud run services update fluu-api \
  --region us-east1 \
  --env-vars-file=env.yaml

# Worker
gcloud run services update fluu-worker \
  --region us-east1 \
  --env-vars-file=env-worker.yaml

# Frontend
gcloud run services update fluu-web \
  --region us-east1 \
  --env-vars-file=env-frontend.yaml
```

---

## ⚠️ DEPOIS DO DEPLOY

### OBRIGATÓRIO: Configurar Google Cloud Console
https://console.cloud.google.com/apis/credentials?project=pix-api-proxy-1758593444

**Adicionar em "Authorized redirect URIs":**
```
https://api.fluu.digital/v1/integrations/google/callback
```

**Adicionar em "Authorized JavaScript origins":**
```
https://fluu.digital
https://api.fluu.digital
```

**Aguardar 5 minutos após salvar**

---

## 🧪 TESTE

Após configurar Google Console:

1. Acesse: **https://fluu.digital/login**
2. Teste login com **email + código OTP**
3. Teste login com **Google**
4. Verifique se **permanece logado** após refresh

---

**⏱️ Tempo total**: ~10 minutos (2 min deploy + 5 min propagação Google + 3 min testes)


