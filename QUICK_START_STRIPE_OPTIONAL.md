# 🚀 Quick Start - Stripe Opcional

## TL;DR

Produtos agora podem ser criados **sem** Stripe configurado. Erro 500 resolvido! ✅

---

## ⚡ Deploy Rápido (5 minutos)

### 1. Testar localmente (opcional mas recomendado)

```bash
cd /Users/<USER>/Documents/www/Gateways/polar

# Comentar POLAR_STRIPE_SECRET_KEY no server/.env
# Linha: POLAR_STRIPE_SECRET_KEY="sk_test_..."
# Vira: # POLAR_STRIPE_SECRET_KEY="sk_test_..."

cd server
uv run task api

# Em outro terminal
./test_stripe_optional.sh
```

**Resultado esperado**: ✅ HTTP 201, stripe_product_id = null

---

### 2. Commit e Push

```bash
git add server/polar/product/service.py
git add *.md
git add test_stripe_optional.sh

git commit -m "fix: make Stripe optional for product creation"
git push
```

---

### 3. Deploy

```bash
./deploy/cloud-run/deploy-backend.sh
```

---

### 4. Testar em Produção

```bash
# Criar produto
curl -X POST https://api.fluu.digital/v1/products/ \
  -H "Authorization: Bearer SEU_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Product",
    "organization_id": "ORG_ID",
    "prices": [{
      "type": "one_time",
      "price_amount": 1000,
      "price_currency": "brl"
    }]
  }'
```

**Resultado esperado**: ✅ HTTP 201 (não 500)

---

## 📊 O Que Mudou?

| Cenário | Antes | Depois |
|---------|-------|--------|
| Criar produto sem Stripe | ❌ 500 Error | ✅ 201 OK |
| Criar produto com Stripe | ✅ 201 OK | ✅ 201 OK |
| Erro no Stripe | ❌ 500 Error | ✅ 201 OK + log |
| Produtos existentes | ✅ Funcionam | ✅ Funcionam |

---

## 🔍 Como Verificar se Está Funcionando

### Logs para procurar (Cloud Run ou console local):

**✅ Sem Stripe configurado:**
```
Stripe not configured, skipping Stripe product creation product_id=xxx
```

**✅ Com Stripe configurado:**
```
Product created in Stripe product_id=xxx stripe_product_id=prod_xxx
```

**⚠️ Stripe configurado mas com erro:**
```
Failed to create product in Stripe, continuing without Stripe integration 
  product_id=xxx error=... error_type=AuthenticationError
```

---

## ⚠️ Importante Saber

### O que funciona agora:
- ✅ Criar produto sem Stripe
- ✅ Criar produto com Stripe
- ✅ Atualizar produto
- ✅ Arquivar produto
- ✅ Listar produtos

### O que ainda precisa de Stripe (será resolvido na FASE 2):
- ⚠️ Fazer checkout de produto sem `stripe_product_id`
- ⚠️ Criar assinatura de produto sem `stripe_product_id`

**Workaround temporário**: Configure o Stripe para usar checkout/assinatura OU aguarde FASE 2 (lazy creation)

---

## 🆘 Troubleshooting

### Problema: Ainda recebo erro 500
```bash
# Verificar se mudanças foram aplicadas
cd server
grep -n "_should_create_in_stripe" polar/product/service.py

# Deve mostrar: "64:    def _should_create_in_stripe(self) -> bool:"
```

Se não mostrar, as mudanças não foram aplicadas. Execute:
```bash
git pull
# Verificar se está no branch correto
```

### Problema: stripe_product_id deveria ser null mas está preenchido
```bash
# Verificar se Stripe está configurado
cd server
grep "POLAR_STRIPE_SECRET_KEY" .env

# Se estiver descomentado, o Stripe está ativo
# Para desabilitar:
# POLAR_STRIPE_SECRET_KEY="sk_test_..." 
# vira:
# # POLAR_STRIPE_SECRET_KEY="sk_test_..."
```

### Problema: Logs não aparecem
```bash
# Verificar nível de log
grep "LOG_LEVEL" server/.env

# Deve ser DEBUG ou INFO
# LOG_LEVEL=DEBUG
```

---

## 📱 Notificar Time

Após deploy, notificar:

```
✅ Deploy concluído: Stripe agora é opcional

🎯 O que mudou:
- Produtos podem ser criados sem Stripe configurado
- Erros do Stripe não causam mais HTTP 500
- stripe_product_id pode ser null

⚠️ Limitação temporária:
- Checkout ainda requer Stripe
- Lazy creation virá na FASE 2

📊 Impacto:
- Zero breaking changes
- Produtos existentes não afetados
- API pública não mudou
```

---

## 🎯 Próximos Passos (FASE 2 - Opcional)

Quando estiver pronto para adicionar Pagar.me:

1. Adicionar campos ao banco:
   - `pagarme_product_id`
   - `pagarme_plan_id`
   - `preferred_payment_processor`

2. Criar migration:
   ```bash
   cd server
   uv run alembic revision --autogenerate -m "add pagarme fields"
   uv run alembic upgrade head
   ```

3. Implementar serviço Pagar.me para produtos

4. Implementar lazy creation no checkout

Mais detalhes em: `STRIPE_OPTIONAL_IMPLEMENTATION.md`

---

## 📚 Documentação Completa

- **Detalhes técnicos**: `STRIPE_OPTIONAL_IMPLEMENTATION.md`
- **Changelog**: `CHANGELOG_STRIPE_OPTIONAL.md`
- **Script de teste**: `test_stripe_optional.sh`
- **Este guia**: `QUICK_START_STRIPE_OPTIONAL.md`

---

**Tempo estimado total**: 5-10 minutos  
**Risco**: Baixo (mudanças são aditivas)  
**Rollback**: Fácil (reverter commit)  

✨ **Pronto para produção!** ✨

