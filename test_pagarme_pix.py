#!/usr/bin/env python3
"""
Script de teste para Pagar.me com PIX no ambiente sandbox.

Uso:
    python test_pagarme_pix.py

Requisitos:
    - PAGARME_SECRET_KEY configurada em .env
    - Server rodando localmente (opcional)
"""

import asyncio
import os
import sys
from pathlib import Path

# Adicionar path do servidor
sys.path.insert(0, str(Path(__file__).parent / "server"))

import httpx
import structlog
from dotenv import load_dotenv

log = structlog.get_logger()

# Carregar variáveis de ambiente
load_dotenv("server/.env")

# Usar API key do config.py como fallback
PAGARME_SECRET_KEY = os.getenv(
    "POLAR_PAGARME_SECRET_KEY", 
    "sk_test_dc24386c807a4b6886f02fde7b10c423"
)
API_BASE_URL = "https://api.pagar.me/core/v5"

print(f"🔑 Usando API Key: {PAGARME_SECRET_KEY[:20]}...")


async def test_pagarme_connection():
    """Testa conexão básica com Pagar.me."""
    print("\n" + "="*70)
    print("🔌 TESTE 1: Conexão com Pagar.me")
    print("="*70)
    
    import base64
    auth_header = base64.b64encode(f"{PAGARME_SECRET_KEY}:".encode()).decode()
    
    async with httpx.AsyncClient(
        base_url=API_BASE_URL,
        headers={
            "Authorization": f"Basic {auth_header}",
            "Content-Type": "application/json",
        },
        timeout=30.0,
    ) as client:
        try:
            # Tentar listar customers (apenas para verificar autenticação)
            response = await client.get("/customers?page=1&size=1")
            response.raise_for_status()
            
            print("✅ Conexão com Pagar.me estabelecida")
            print(f"   Status: {response.status_code}")
            print(f"   API Key: {PAGARME_SECRET_KEY[:20]}...")
            return True
            
        except httpx.HTTPStatusError as e:
            print(f"❌ Erro de autenticação: {e.response.status_code}")
            print(f"   Resposta: {e.response.text[:200]}")
            return False
        except Exception as e:
            print(f"❌ Erro de conexão: {str(e)}")
            return False


async def test_create_customer():
    """Cria customer de teste no Pagar.me."""
    print("\n" + "="*70)
    print("👤 TESTE 2: Criar Customer")
    print("="*70)
    
    import base64
    import random
    
    auth_header = base64.b64encode(f"{PAGARME_SECRET_KEY}:".encode()).decode()
    
    async with httpx.AsyncClient(
        base_url=API_BASE_URL,
        headers={
            "Authorization": f"Basic {auth_header}",
            "Content-Type": "application/json",
        },
        timeout=30.0,
    ) as client:
        try:
            # Dados do customer de teste
            test_id = random.randint(1000, 9999)
            payload = {
                "name": "Ismael",
                "email": f"teste+{test_id}@fluu.digital",
                "type": "individual",
                "document": "12345678909",  # CPF de teste
                "phones": {
                    "mobile_phone": {
                        "country_code": "55",
                        "area_code": "11",
                        "number": "999999999"
                    }
                }
            }
            
            print(f"📤 Criando customer: {payload['email']}")
            response = await client.post("/customers", json=payload)
            response.raise_for_status()
            customer_data = response.json()
            
            customer_id = customer_data["id"]
            print(f"✅ Customer criado com sucesso")
            print(f"   ID: {customer_id}")
            print(f"   Email: {customer_data.get('email')}")
            
            return customer_id
            
        except httpx.HTTPStatusError as e:
            print(f"❌ Erro ao criar customer: {e.response.status_code}")
            print(f"   Resposta: {e.response.text[:500]}")
            return None
        except Exception as e:
            print(f"❌ Erro inesperado: {str(e)}")
            return None


async def test_create_pix_payment(customer_id: str):
    """Cria pagamento PIX de teste."""
    print("\n" + "="*70)
    print("💰 TESTE 3: Criar Pagamento PIX")
    print("="*70)
    
    import base64
    
    auth_header = base64.b64encode(f"{PAGARME_SECRET_KEY}:".encode()).decode()
    
    async with httpx.AsyncClient(
        base_url=API_BASE_URL,
        headers={
            "Authorization": f"Basic {auth_header}",
            "Content-Type": "application/json",
        },
        timeout=30.0,
    ) as client:
        try:
            # Criar order com PIX
            payload = {
                "customer_id": customer_id,
                "items": [
                    {
                        "amount": 10000,  # R$ 100,00 em centavos
                        "description": "Teste de pagamento PIX - Fluu",
                        "quantity": 1,
                    }
                ],
                "payments": [
                    {
                        "payment_method": "pix",
                        "pix": {
                            "expires_in": 3600,  # 1 hora
                        }
                    }
                ],
                "metadata": {
                    "test": "true",
                    "platform": "fluu",
                }
            }
            
            print(f"📤 Criando order PIX de R$ 100,00")
            response = await client.post("/orders", json=payload)
            response.raise_for_status()
            order_data = response.json()
            
            order_id = order_data["id"]
            print(f"✅ Order criada com sucesso")
            print(f"   Order ID: {order_id}")
            print(f"   Status: {order_data.get('status')}")
            
            # Extrair dados do PIX
            if order_data.get("charges") and len(order_data["charges"]) > 0:
                charge = order_data["charges"][0]
                charge_id = charge["id"]
                print(f"   Charge ID: {charge_id}")
                print(f"   Charge Status: {charge.get('status')}")
                
                last_tx = charge.get("last_transaction", {})
                qr_code = last_tx.get("qr_code")
                qr_code_url = last_tx.get("qr_code_url")
                expires_at = last_tx.get("expires_at")
                
                if qr_code:
                    print(f"\n🎉 PIX GERADO COM SUCESSO!")
                    print(f"   ╔══════════════════════════════════════════╗")
                    print(f"   ║  QR Code PIX                             ║")
                    print(f"   ╚══════════════════════════════════════════╝")
                    print(f"   Código PIX (copie e cole):")
                    print(f"   {qr_code[:50]}...")
                    print(f"   ...")
                    print(f"   {qr_code[-50:]}")
                    print(f"\n   📱 URL do QR Code: {qr_code_url}")
                    print(f"   ⏰ Expira em: {expires_at}")
                    print(f"\n   💡 Abra o app do seu banco e escaneie o QR Code")
                    print(f"      ou copie e cole o código PIX para testar o pagamento.")
                    
                    return {
                        "order_id": order_id,
                        "charge_id": charge_id,
                        "qr_code": qr_code,
                        "qr_code_url": qr_code_url,
                        "expires_at": expires_at,
                    }
                else:
                    print(f"⚠️  PIX criado mas QR Code não disponível")
                    print(f"   Resposta: {charge}")
            else:
                print(f"⚠️  Order criada mas sem charges")
                
            return None
            
        except httpx.HTTPStatusError as e:
            print(f"❌ Erro ao criar pagamento PIX: {e.response.status_code}")
            print(f"   Resposta: {e.response.text[:500]}")
            return None
        except Exception as e:
            print(f"❌ Erro inesperado: {str(e)}")
            import traceback
            traceback.print_exc()
            return None


async def test_check_payment_status(charge_id: str):
    """Verifica status do pagamento."""
    print("\n" + "="*70)
    print("🔍 TESTE 4: Verificar Status do Pagamento")
    print("="*70)
    
    import base64
    
    auth_header = base64.b64encode(f"{PAGARME_SECRET_KEY}:".encode()).decode()
    
    async with httpx.AsyncClient(
        base_url=API_BASE_URL,
        headers={
            "Authorization": f"Basic {auth_header}",
            "Content-Type": "application/json",
        },
        timeout=30.0,
    ) as client:
        try:
            print(f"📤 Consultando charge: {charge_id}")
            response = await client.get(f"/charges/{charge_id}")
            response.raise_for_status()
            charge_data = response.json()
            
            status = charge_data.get("status")
            amount = charge_data.get("amount", 0) / 100
            
            print(f"✅ Status do pagamento")
            print(f"   Charge ID: {charge_id}")
            print(f"   Status: {status}")
            print(f"   Valor: R$ {amount:.2f}")
            print(f"   Método: {charge_data.get('payment_method')}")
            
            status_emoji = {
                "pending": "⏳",
                "paid": "✅",
                "failed": "❌",
                "canceled": "🚫",
            }
            
            print(f"\n   {status_emoji.get(status, '❓')} Status: {status.upper()}")
            
            if status == "paid":
                print(f"   🎉 PAGAMENTO CONFIRMADO!")
            elif status == "pending":
                print(f"   ⏳ Aguardando pagamento...")
                print(f"   💡 Escaneie o QR Code para completar o pagamento")
            
            return charge_data
            
        except httpx.HTTPStatusError as e:
            print(f"❌ Erro ao consultar pagamento: {e.response.status_code}")
            print(f"   Resposta: {e.response.text[:500]}")
            return None
        except Exception as e:
            print(f"❌ Erro inesperado: {str(e)}")
            return None


async def main():
    """Executa todos os testes."""
    print("\n")
    print("╔═══════════════════════════════════════════════════════════════════╗")
    print("║                                                                   ║")
    print("║         🧪 TESTE PAGAR.ME + PIX - FLUU (SANDBOX)                ║")
    print("║                                                                   ║")
    print("╚═══════════════════════════════════════════════════════════════════╝")
    
    # Teste 1: Conexão
    if not await test_pagarme_connection():
        print("\n❌ Falha na conexão. Verifique a API Key.")
        return
    
    # Teste 2: Criar customer
    customer_id = await test_create_customer()
    if not customer_id:
        print("\n❌ Falha ao criar customer. Teste interrompido.")
        return
    
    # Teste 3: Criar pagamento PIX
    pix_data = await test_create_pix_payment(customer_id)
    if not pix_data:
        print("\n❌ Falha ao criar pagamento PIX. Teste interrompido.")
        return
    
    # Teste 4: Verificar status inicial
    await test_check_payment_status(pix_data["charge_id"])
    
    # Resumo
    print("\n" + "="*70)
    print("📊 RESUMO DOS TESTES")
    print("="*70)
    print("✅ Conexão com Pagar.me: OK")
    print("✅ Criação de customer: OK")
    print("✅ Criação de pagamento PIX: OK")
    print("✅ Geração de QR Code: OK")
    print("\n💡 PRÓXIMOS PASSOS:")
    print("   1. Escaneie o QR Code acima com app do banco (sandbox)")
    print("   2. Execute novamente o Teste 4 para verificar status atualizado")
    print("   3. Integre no CheckoutService do Fluu")
    print("\n📚 Documentação: ARQUITETURA_MULTI_GATEWAY.md")
    print("="*70)
    print()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n⚠️  Teste interrompido pelo usuário")
    except Exception as e:
        print(f"\n\n❌ Erro fatal: {str(e)}")
        import traceback
        traceback.print_exc()

