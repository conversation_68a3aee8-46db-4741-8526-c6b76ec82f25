# 🎯 PROMPT: Remover Dependência do Stripe na Criação de Produtos

## 📋 CONTEXTO

**Projeto**: Fluu (fork do Polar)  
**Localização**: `/Users/<USER>/Documents/www/Gateways/polar`  
**Stack**: Python/FastAPI (Backend), PostgreSQL, Redis

## 🔴 PROBLEMA ATUAL

Ao criar um produto via `POST /v1/products/`, o sistema **SEMPRE** tenta criar o produto no Stripe (linhas 250-265 de `server/polar/product/service.py`), causando erro 500 quando a chave do Stripe não está configurada.

```python
# server/polar/product/service.py - linha ~250
stripe_product = await stripe_service.create_product(...)  # ❌ SEMPRE executa
product.stripe_product_id = stripe_product.id

for price in product.all_prices:
    if isinstance(price, HasStripePriceId):
        stripe_price = await stripe_service.create_price_for_product(...)  # ❌ SEMPRE executa
        price.stripe_price_id = stripe_price.id
```

### Erro em Produção
```
POST https://api.fluu.digital/v1/products/
Status: 500 Internal Server Error
Error: stripe._error.AuthenticationError
```

---

## 🎯 OBJETIVO

**Tornar a criação de produto no gateway de pagamento OPCIONAL e configurável**, permitindo:

1. ✅ Criar produto **SEM** enviar para gateway de pagamento
2. ✅ Criar produto no **Pagar.me** quando configurado
3. ✅ Criar produto no **Stripe** quando configurado  
4. ✅ Suportar **múltiplos gateways** simultaneamente no futuro
5. ✅ Manter **compatibilidade** com código existente

---

## 📐 ARQUITETURA DESEJADA

### Princípios
1. **Gateway agnóstico**: Produto deve existir no banco independente de gateway
2. **Configuração explícita**: Apenas criar no gateway se explicitamente configurado
3. **Lazy creation**: Criar no gateway apenas quando necessário (checkout, cobrança)
4. **Múltiplos gateways**: Permitir produto ter IDs em vários gateways

### Fluxo Proposto
```
Criar Produto
    ↓
Salvar no Banco (SEMPRE) ✅
    ↓
Gateway configurado?
    ├─ SIM → Criar no gateway (assíncrono/lazy)
    └─ NÃO → Pular (gateway será configurado depois)
    ↓
Retornar produto criado ✅
```

---

## 🛠️ IMPLEMENTAÇÃO SUGERIDA

### Fase 1: Tornar Stripe Opcional (URGENTE)

#### Arquivo: `server/polar/product/service.py`

**Localização**: Método `create()` - linhas ~240-270

**Mudança**:
```python
# ANTES (linha ~250)
stripe_product = await stripe_service.create_product(...)  # ❌ Sempre executa
product.stripe_product_id = stripe_product.id

# DEPOIS
# Criar no Stripe apenas se configurado
if self._should_create_in_stripe():
    try:
        stripe_product = await stripe_service.create_product(
            product.get_stripe_name(),
            description=product.description,
            metadata=metadata,
        )
        product.stripe_product_id = stripe_product.id
        
        for price in product.all_prices:
            if isinstance(price, HasStripePriceId):
                stripe_price = await stripe_service.create_price_for_product(
                    stripe_product.id,
                    price.get_stripe_price_params(product.recurring_interval),
                )
                price.stripe_price_id = stripe_price.id
            session.add(price)
        
        log.info("Product created in Stripe", product_id=str(product.id), stripe_product_id=stripe_product.id)
    except Exception as e:
        log.warning(
            "Failed to create product in Stripe, continuing without Stripe integration",
            product_id=str(product.id),
            error=str(e),
            error_type=type(e).__name__,
        )
        # Produto continua sendo criado mesmo se Stripe falhar
else:
    log.info("Stripe not configured, skipping Stripe product creation", product_id=str(product.id))
```

**Adicionar método auxiliar**:
```python
def _should_create_in_stripe(self) -> bool:
    """Verifica se deve criar produto no Stripe."""
    from polar.config import settings
    return bool(
        settings.STRIPE_SECRET_KEY 
        and settings.STRIPE_SECRET_KEY != "" 
        and not settings.STRIPE_SECRET_KEY.startswith("YOUR_")
    )
```

#### Arquivo: `server/polar/product/service.py` - Método `update()`

**Localização**: Linhas ~430-450

**Aplicar mesma lógica** de verificação antes de criar preços no Stripe.

---

### Fase 2: Adicionar Suporte para Pagar.me (MÉDIO PRAZO)

#### 1. Adicionar campos ao modelo Product

**Arquivo**: `server/polar/models/product.py`

```python
class Product(RecordModel):
    # ... campos existentes ...
    
    # Stripe
    stripe_product_id: Mapped[str | None] = mapped_column(String, nullable=True, default=None)
    
    # Pagar.me (NOVO)
    pagarme_product_id: Mapped[str | None] = mapped_column(String, nullable=True, default=None)
    pagarme_plan_id: Mapped[str | None] = mapped_column(String, nullable=True, default=None)
    
    # Configuração de gateway preferencial
    preferred_payment_processor: Mapped[PaymentProcessor | None] = mapped_column(
        Enum(PaymentProcessor), nullable=True, default=None
    )
```

#### 2. Migration do Banco

```bash
cd server
uv run alembic revision --autogenerate -m "add pagarme fields to product"
uv run alembic upgrade head
```

#### 3. Criar Serviço Pagar.me para Produtos

**Arquivo**: `server/polar/integrations/payment_providers/pagarme/product_service.py` (NOVO)

```python
import structlog
from polar.config import settings
from polar.models import Product, ProductPrice

log = structlog.get_logger()

class PagarmeProductService:
    """Serviço para gerenciar produtos no Pagar.me."""
    
    async def create_product(
        self,
        product: Product,
        prices: list[ProductPrice],
    ) -> dict:
        """
        Cria produto no Pagar.me.
        
        Nota: Pagar.me não tem conceito de "produto" separado.
        Criamos planos diretamente associados aos preços.
        """
        if not self._is_configured():
            log.warning("Pagar.me not configured")
            return {}
        
        # Lógica para criar plano no Pagar.me
        # Retorna IDs dos planos criados
        pass
    
    def _is_configured(self) -> bool:
        """Verifica se Pagar.me está configurado."""
        return bool(
            settings.ENABLE_PAGARME
            and settings.PAGARME_SECRET_KEY
            and settings.PAGARME_SECRET_KEY != ""
        )

pagarme_product_service = PagarmeProductService()
```

#### 4. Integrar no ProductService

**Arquivo**: `server/polar/product/service.py`

```python
from polar.integrations.payment_providers.pagarme.product_service import pagarme_product_service

class ProductService:
    async def create(self, session, create_schema, auth_subject):
        # ... criar produto no banco ...
        
        # Criar no Stripe se configurado
        if self._should_create_in_stripe():
            await self._create_in_stripe(session, product)
        
        # Criar no Pagar.me se configurado
        if self._should_create_in_pagarme():
            await self._create_in_pagarme(session, product)
        
        await session.flush()
        return product
    
    def _should_create_in_pagarme(self) -> bool:
        """Verifica se deve criar produto no Pagar.me."""
        from polar.config import settings
        return bool(settings.ENABLE_PAGARME)
    
    async def _create_in_pagarme(self, session, product):
        """Cria produto no Pagar.me."""
        try:
            result = await pagarme_product_service.create_product(
                product, product.all_prices
            )
            product.pagarme_product_id = result.get('product_id')
            product.pagarme_plan_id = result.get('plan_id')
            log.info("Product created in Pagar.me", product_id=str(product.id))
        except Exception as e:
            log.warning(
                "Failed to create in Pagar.me, continuing",
                error=str(e)
            )
```

---

## 📋 TAREFAS PARA O AGENTE

### ✅ FASE 1: URGENTE (Resolver erro 500)

1. **Modificar `server/polar/product/service.py`**:
   - [ ] Adicionar método `_should_create_in_stripe()`
   - [ ] Envolver chamadas Stripe em `if self._should_create_in_stripe():`
   - [ ] Adicionar try/except com log de warning
   - [ ] Fazer o mesmo no método `update()`
   - [ ] Fazer o mesmo no método `update_prices()` se houver

2. **Modificar `server/polar/product/service.py` - outros métodos**:
   - [ ] Buscar TODOS os lugares que chamam `stripe_service.*`
   - [ ] Aplicar mesma lógica de verificação
   - [ ] Garantir que falhas no Stripe não impedem operações

3. **Testar localmente**:
   - [ ] Remover/comentar `POLAR_STRIPE_SECRET_KEY` do `.env`
   - [ ] Tentar criar produto
   - [ ] ✅ Deve funcionar sem erro 500
   - [ ] ✅ Produto deve ser salvo no banco
   - [ ] ⚠️ Log deve mostrar "Stripe not configured, skipping..."

4. **Commit e Deploy**:
   - [ ] Commit: `fix: make Stripe optional for product creation`
   - [ ] Deploy backend

### 🔄 FASE 2: MÉDIO PRAZO (Suporte Pagar.me)

5. **Adicionar campos ao modelo Product**:
   - [ ] `pagarme_product_id`
   - [ ] `pagarme_plan_id`
   - [ ] `preferred_payment_processor`

6. **Criar migration**:
   - [ ] `alembic revision -m "add pagarme fields to product"`

7. **Criar PagarmeProductService**:
   - [ ] Implementar `create_product()`
   - [ ] Implementar `update_product()`
   - [ ] Implementar `create_plan()` (Pagar.me usa planos)

8. **Integrar no ProductService**:
   - [ ] Adicionar `_should_create_in_pagarme()`
   - [ ] Adicionar `_create_in_pagarme()`
   - [ ] Chamar após `_create_in_stripe()`

### 🚀 FASE 3: FUTURO (Múltiplos Gateways)

9. **Abstrair lógica de gateway**:
   - [ ] Criar interface `PaymentGatewayAdapter`
   - [ ] Implementar `StripeAdapter`, `PagarmeAdapter`
   - [ ] Loop sobre gateways configurados

10. **Permitir escolha de gateway preferencial**:
    - [ ] Adicionar campo na UI de criação de produto
    - [ ] Usar `preferred_payment_processor` ao processar pagamentos

---

## 🔍 ARQUIVOS A MODIFICAR

### FASE 1 (Urgente)
```
server/polar/product/service.py (PRINCIPAL)
server/polar/product/tasks.py (se houver chamadas Stripe)
server/polar/checkout/service.py (verificar lazy creation)
```

### FASE 2 (Médio prazo)
```
server/polar/models/product.py (adicionar campos)
server/migrations/versions/XXXX_add_pagarme_fields.py (NOVO - migration)
server/polar/integrations/payment_providers/pagarme/product_service.py (NOVO)
server/polar/product/service.py (integrar Pagar.me)
```

---

## 💡 CONSIDERAÇÕES IMPORTANTES

### 1. Pagamentos Existentes
- Produtos **já criados** no Stripe continuam funcionando
- Não quebra produtos existentes
- Migration deve ser **aditiva** (novos campos nullable)

### 2. Checkout Flow
Ao processar checkout:
```python
# Verificar se produto tem ID do gateway
if not product.stripe_product_id and settings.STRIPE_SECRET_KEY:
    # Lazy creation: criar agora se necessário
    await stripe_service.create_product(...)
    product.stripe_product_id = stripe_product.id

# Ou usar Pagar.me
if not product.pagarme_product_id and settings.ENABLE_PAGARME:
    await pagarme_service.create_product(...)
    product.pagarme_product_id = pagarme_product.id
```

### 3. Migração de Dados
- Produtos existentes: `stripe_product_id` continua
- Novos produtos: `stripe_product_id` pode ser NULL
- Quando Pagar.me ativado: preencher `pagarme_product_id`

---

## 📝 EXEMPLOS DE CÓDIGO

### Exemplo 1: Verificação de Gateway

```python
from polar.config import settings
from polar.enums import PaymentProcessor

def get_available_payment_processors() -> list[PaymentProcessor]:
    """Retorna lista de gateways configurados."""
    processors = []
    
    if settings.STRIPE_SECRET_KEY and settings.STRIPE_SECRET_KEY != "":
        processors.append(PaymentProcessor.stripe)
    
    if settings.ENABLE_PAGARME and settings.PAGARME_SECRET_KEY:
        processors.append(PaymentProcessor.pagarme)
    
    return processors
```

### Exemplo 2: Criação Condicional

```python
async def _create_in_payment_gateways(
    self,
    session: AsyncSession,
    product: Product,
) -> None:
    """Cria produto nos gateways configurados."""
    metadata = {
        "product_id": str(product.id),
        "organization_id": str(product.organization_id),
    }
    
    # Stripe (opcional)
    if self._should_create_in_stripe():
        try:
            stripe_product = await stripe_service.create_product(...)
            product.stripe_product_id = stripe_product.id
            log.info("Product created in Stripe", product_id=str(product.id))
        except Exception as e:
            log.warning("Stripe creation failed, continuing", error=str(e))
    
    # Pagar.me (opcional)
    if self._should_create_in_pagarme():
        try:
            pagarme_result = await pagarme_service.create_product(...)
            product.pagarme_product_id = pagarme_result.get('id')
            log.info("Product created in Pagar.me", product_id=str(product.id))
        except Exception as e:
            log.warning("Pagar.me creation failed, continuing", error=str(e))
```

### Exemplo 3: Lazy Creation no Checkout

```python
async def process_checkout(session, product, customer):
    """Processa checkout, criando produto no gateway se necessário."""
    
    # Determinar gateway a usar (Pagar.me tem prioridade)
    if settings.ENABLE_PAGARME:
        if not product.pagarme_product_id:
            # Lazy creation
            await pagarme_service.create_product(product)
        
        return await pagarme_service.create_checkout(product, customer)
    
    elif settings.STRIPE_SECRET_KEY:
        if not product.stripe_product_id:
            # Lazy creation
            await stripe_service.create_product(product)
        
        return await stripe_service.create_checkout(product, customer)
    
    else:
        raise PaymentGatewayNotConfiguredError()
```

---

## 🎯 TAREFAS ESPECÍFICAS PARA O AGENTE

### TAREFA 1: Análise Completa
```
ANALISE todos os arquivos que chamam stripe_service no contexto de produtos:
- server/polar/product/service.py
- server/polar/product/tasks.py  
- server/polar/checkout/service.py
- server/polar/order/service.py
- server/polar/subscription/service.py

LISTE todas as funções que:
1. Criam produto no Stripe
2. Criam preços no Stripe  
3. Atualizam produto no Stripe
4. Deletam produto no Stripe

IDENTIFIQUE quais são críticas (bloqueiam operação) vs opcionais.
```

### TAREFA 2: Implementar Verificação Condicional
```
MODIFIQUE server/polar/product/service.py para:

1. Adicionar método _should_create_in_stripe() que retorna True apenas se:
   - settings.STRIPE_SECRET_KEY está definida
   - settings.STRIPE_SECRET_KEY não está vazia
   - settings.STRIPE_SECRET_KEY não começa com "YOUR_"

2. Envolver TODAS as chamadas ao stripe_service em:
   if self._should_create_in_stripe():
       try:
           # chamadas stripe
       except Exception as e:
           log.warning("Stripe failed, continuing without Stripe")

3. Garantir que o produto SEMPRE é salvo no banco, independente do Stripe.

4. Adicionar logs informativos:
   - "Stripe not configured, skipping Stripe integration"
   - "Product created in Stripe successfully"
   - "Stripe creation failed, product saved without Stripe ID"
```

### TAREFA 3: Testar Sem Stripe
```
TESTE o fluxo completo sem Stripe configurado:

1. Remover POLAR_STRIPE_SECRET_KEY do .env
2. Iniciar backend local
3. Criar produto via API POST /v1/products/
4. VERIFICAR:
   ✅ Retorna HTTP 201 (não 500)
   ✅ Produto está no banco de dados
   ✅ stripe_product_id é NULL
   ✅ Log mostra "Stripe not configured, skipping..."

5. Adicionar POLAR_STRIPE_SECRET_KEY válida
6. Reiniciar backend
7. Criar novo produto
8. VERIFICAR:
   ✅ Produto criado no banco E no Stripe
   ✅ stripe_product_id preenchido
```

### TAREFA 4: Preparar para Pagar.me
```
PREPARE a estrutura para Pagar.me:

1. Adicionar campos ao modelo Product:
   - pagarme_product_id: str | None
   - pagarme_plan_id: str | None
   - preferred_payment_processor: PaymentProcessor | None

2. Criar migration:
   - Campos nullable (não quebra produtos existentes)
   - Índices se necessário

3. Criar stub do serviço:
   - server/polar/integrations/payment_providers/pagarme/product_service.py
   - Métodos vazios por enquanto (implementar depois)

4. Documentar próximos passos no código:
   # TODO: Implement Pagar.me product creation
   # TODO: Implement Pagar.me plan creation
```

---

## ✅ CRITÉRIOS DE SUCESSO

### Fase 1 (Urgente)
- [ ] Criar produto SEM Stripe configurado → **HTTP 201** (não 500)
- [ ] Criar produto COM Stripe configurado → **Cria no Stripe também**
- [ ] Falha do Stripe → **Produto salvo, log de warning, HTTP 201**
- [ ] Produtos existentes → **Continuam funcionando**

### Fase 2 (Médio prazo)
- [ ] Migration aplicada sem erros
- [ ] Novos campos disponíveis no modelo
- [ ] Estrutura preparada para Pagar.me
- [ ] Documentação clara de como adicionar novo gateway

---

## 📚 ARQUIVOS DE REFERÊNCIA

### Já Implementado (Usar como referência)
```
server/polar/integrations/payment_providers/pagarme/provider.py
server/polar/integrations/payment_providers/registry.py
server/polar/integrations/payment_providers/base.py
server/polar/config.py (ENABLE_PAGARME)
```

### Stripe (Para entender lógica atual)
```
server/polar/integrations/stripe/service.py
server/polar/integrations/stripe/schemas.py
```

---

## 🚨 ATENÇÃO

### NÃO REMOVER
- ❌ Não remover campos `stripe_product_id` existentes
- ❌ Não remover `stripe_service` (produtos antigos usam)
- ❌ Não quebrar compatibilidade com webhooks do Stripe

### ADICIONAR
- ✅ Tornar Stripe **opcional**, não removê-lo
- ✅ Adicionar **verificações condicionais**
- ✅ Adicionar **tratamento de erros**
- ✅ Adicionar **logs informativos**

---

## 🎯 PRIORIDADE DE EXECUÇÃO

1. **URGENTE** (hoje): Tornar Stripe opcional - Resolver erro 500
2. **ALTA** (esta semana): Adicionar campos Pagar.me ao modelo
3. **MÉDIA** (próxima semana): Implementar criação no Pagar.me
4. **BAIXA** (futuro): Abstrair para suportar N gateways

---

## 📊 IMPACTO ESPERADO

| Área | Antes | Depois |
|------|-------|--------|
| Criar produto sem gateway | ❌ Erro 500 | ✅ HTTP 201 |
| Criar produto com Stripe | ✅ Funciona | ✅ Funciona |
| Criar produto com Pagar.me | ❌ Não suportado | ✅ Funciona |
| Produtos existentes | ✅ Funcionam | ✅ Funcionam |
| Checkouts Stripe | ✅ Funcionam | ✅ Funcionam |
| Checkouts Pagar.me | ⚠️ Parcial | ✅ Completo |

---

## 🔧 COMANDO PARA EXECUTAR

Após o agente fazer as mudanças:

```bash
cd /Users/<USER>/Documents/www/Gateways/polar

# Testar localmente sem Stripe
cd server
# Comentar POLAR_STRIPE_SECRET_KEY no .env
uv run task api

# Testar criar produto
# Deve retornar 201 sem erro

# Commit
git add server/polar/product/service.py
git commit -m "fix: make Stripe optional for product creation

- Product creation no longer requires Stripe
- Stripe integration is now conditional based on configuration
- Errors in Stripe don't block product creation
- Preparing for multi-gateway support (Pagar.me, etc)
"

# Deploy
cd ..
./deploy/cloud-run/deploy-backend.sh
```

---

## 📞 REFERÊNCIAS

- **Pagar.me Docs**: https://docs.pagar.me/
- **Stripe Docs**: https://docs.stripe.com/
- **Código Pagar.me existente**: `server/polar/integrations/payment_providers/pagarme/`
- **Registry pattern**: `server/polar/integrations/payment_providers/registry.py`

---

## ✅ RESULTADO FINAL ESPERADO

Após todas as fases:

```python
# Criar produto sem gateway
POST /v1/products/ → 201 OK (salvo no banco, sem gateway)

# Criar produto com Pagar.me ativado
POST /v1/products/ → 201 OK (salvo no banco + Pagar.me)

# Criar produto com Stripe e Pagar.me ativados
POST /v1/products/ → 201 OK (salvo no banco + ambos gateways)

# Falha em um gateway
POST /v1/products/ → 201 OK (salvo no banco + gateways que funcionaram)
```

**Produto no banco é a fonte da verdade, gateways são integrações opcionais.**

---

**Criado em**: 2025-11-10  
**Prioridade**: 🔴 CRÍTICA (bloqueia criação de produtos)  
**Tempo estimado Fase 1**: ~30 minutos de desenvolvimento + 10 min deploy

